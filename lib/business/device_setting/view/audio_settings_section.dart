import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../common/util/i18n.dart';
import '../device_setting_logic.dart';
import 'audio_balance_control.dart';
import 'common/setting_card_factory.dart';
import 'setting_card.dart';

/// 音频设置区域组件 (使用可扩展的 SettingCardFactory)
class AudioSettingsSection extends StatelessWidget {
  const AudioSettingsSection({super.key});

  @override
  Widget build(BuildContext context) {
    final logic = Get.find<DeviceSettingLogic>();
    final factory = SettingCardFactory(logic);

    return Column(
      children: [
        _buildBalanceCard(context),
        const SizedBox(height: 6),

        // --- 获取并添加特定于设备的音频设置卡片 (IIS相关设置 for D900, Filter/Decode Mode for DX5) ---
        ..._buildDeviceSpecificAudioCards(context, factory),

        // --- 通用音频设置 (Switches) ---
        // 需要 Obx 包裹，因为它们的值依赖于 state
        Obx(() => factory.createAudioMonitoringSettingCard(context)),
        
        // 只有非D900设备才显示蓝牙APTX设置
        if (!logic.isD900Device) ...[
          const SizedBox(height: 6),
          Obx(() => factory.createBluetoothAptxSettingCard(context)),
        ],
        
        // 只有D900设备才显示USB DSD直通设置
        if (logic.isD900Device) ...[
          const SizedBox(height: 6),
          Obx(() => factory.createUsbDsdPassthroughSettingCard(context)),
        ],
      ],
    );
  }

  /// 构建音频平衡控制卡片
  Widget _buildBalanceCard(BuildContext context) {
    return SettingCard(
      title: l10n.audioBalance,
      showTitle: false,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.audioBalance,
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            const AudioBalanceControl(),
          ],
        ),
      ),
    );
  }

  /// 辅助方法，构建特定于设备的音频相关卡片列表
  List<Widget> _buildDeviceSpecificAudioCards(
      BuildContext context, SettingCardFactory factory) {
    // 调用工厂获取特定音频设置卡片列表
    final specificCards = factory.createAudioSpecificSettingCards(context);

    if (specificCards.isNotEmpty) {
      List<Widget> spacedCards = [];
      for (int i = 0; i < specificCards.length; i++) {
        spacedCards.add(specificCards[i]);
        // 在特定卡片和第一个通用 Switch 卡片之间，以及特定卡片之间加间距
        spacedCards.add(const SizedBox(height: 6));
      }
      return spacedCards;
    } else {
      return []; // 没有特定卡片则返回空列表
    }
  }
}
