import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

// Corrected relative paths, assuming these files are in lib/common, lib/l10n, lib/theme
import '../../common/util/i18n.dart';
import '../../l10n/app_localizations.dart';
import '../../theme/color_palettes.dart';
import '../../theme/text_styles.dart';
import './device_settings_logic.dart'; // Corrected to be explicit relative path

class DeviceSettingsPage extends GetView<DeviceSettingsLogic> {
  const DeviceSettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    // final l10n = AppLocalizations.of(context)!; // Keep commented if AppLocalizations is not found
    // For now, using hardcoded strings or simple l10n.get('key') if you have i18n.dart setup

    return Scaffold(
      appBar: AppBar(
        title: Text(
          l10n.deviceSettingsTitle ?? 'Device Settings',
          style: TextStyles.instance.h2(),
        ),
        backgroundColor: ColorPalettes.instance.background,
        centerTitle: true,
        iconTheme: IconThemeData(color: ColorPalettes.instance.firstText),
        elevation: 0,
      ),
      body: _buildBody(context),
    );
  }

  Widget _buildBody(BuildContext context) {
    // final l10n = AppLocalizations.of(context)!;

    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSettingsSectionTitle(
            l10n.displaySettingsTitle ?? 'Display Settings',
            Icons.display_settings_outlined,
          ),
          _buildDropdownSettingCard(
            title: l10n.themeSettingTitle ?? 'Theme',
            currentValue:
                controller
                    .selectedTheme
                    .value, // This now correctly refers to the Obx value
            options: controller.themeOptions,
            onChanged: controller.onThemeChanged,
            icon: Icons.color_lens_outlined,
          ),
          _buildDropdownSettingCard(
            title: l10n.homePageSettingTitle ?? 'Home Page',
            currentValue:
                controller
                    .selectedHomePage
                    .value, // This now correctly refers to the Obx value
            options: controller.homePageOptions,
            onChanged: controller.onHomePageChanged,
            icon: Icons.home_outlined,
          ),
          _buildDropdownSettingCard(
            title: l10n.brightnessSettingTitle ?? 'Brightness',
            currentValue:
                controller
                    .selectedBrightness
                    .value, // This now correctly refers to the Obx value
            options: controller.brightnessOptions,
            onChanged: controller.onBrightnessChanged,
            icon: Icons.brightness_6_outlined,
          ),

          // TODO: Add VU Meter 0dDB Level (D900, DX5II)
          // TODO: Add VU Meter Display (D900, DX5II)
          SizedBox(height: 24.h),

          // TODO: Add Input Settings Section
          // TODO: Add Output Settings Section
          // TODO: Add PEQ Configuration Section
          // TODO: Add Advanced Settings Section
          // TODO: Add System Settings Section
        ],
      ),
    );
  }

  Widget _buildSettingsSectionTitle(String title, IconData icon) {
    return Padding(
      padding: EdgeInsets.only(top: 16.h, bottom: 8.h),
      child: Row(
        children: [
          Icon(icon, color: ColorPalettes.instance.primary, size: 24.sp),
          SizedBox(width: 10.w),
          Text(
            title,
            style: TextStyles.instance.h2(
              color: ColorPalettes.instance.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDropdownSettingCard<T>({
    required String title,
    required T
    currentValue, // Will be wrapped by Obx in the DropdownButtonFormField
    required List<T> options,
    required ValueChanged<T?> onChanged,
    required IconData icon,
    String? subtitle,
  }) {
    return Card(
      margin: EdgeInsets.symmetric(vertical: 8.h),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      color: ColorPalettes.instance.card,
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: ColorPalettes.instance.primary, size: 20.sp),
                SizedBox(width: 12.w),
                Text(
                  title,
                  style: TextStyles.instance.h3(fontWeight: FontWeight.w500),
                ),
              ],
            ),
            if (subtitle != null)
              Padding(
                padding: EdgeInsets.only(top: 4.h, left: 32.w),
                child: Text(
                  subtitle,
                  style: TextStyles.instance.caption(
                    color: ColorPalettes.instance.secondText,
                  ),
                ),
              ),
            SizedBox(height: 8.h),
            Padding(
              padding: EdgeInsets.only(left: 32.w),
              child: Obx(
                () => DropdownButtonFormField<T>(
                  // Use controller.selectedTheme.value directly for Obx to track changes
                  // This assumes your controller's RxString fields are being correctly observed.
                  value:
                      currentValue, // This should be the .value of the Rx variable from the controller
                  items:
                      options.map((T optionValue) {
                        // Renamed to avoid conflict
                        return DropdownMenuItem<T>(
                          value: optionValue,
                          child: Text(
                            optionValue.toString(),
                            style: TextStyles.instance.body1(),
                          ),
                        );
                      }).toList(),
                  onChanged: onChanged,
                  decoration: InputDecoration(
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 12.w,
                      vertical: 8.h,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8.r),
                      borderSide: BorderSide(
                        color: ColorPalettes.instance.divider.withValues(
                          alpha: 0.5,
                        ),
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8.r),
                      borderSide: BorderSide(
                        color: ColorPalettes.instance.divider.withValues(
                          alpha: 0.5,
                        ),
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8.r),
                      borderSide: BorderSide(
                        color: ColorPalettes.instance.primary,
                        width: 1.5,
                      ),
                    ),
                    filled: true,
                    fillColor: ColorPalettes.instance.background,
                  ),
                  dropdownColor: ColorPalettes.instance.card,
                  style: TextStyles.instance.body1(
                    color: ColorPalettes.instance.firstText,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Helper for l10n if not using AppLocalizations fully yet
// Remove or replace with your actual l10n setup
// Ensure AppLocalizations and t() method are correctly defined if you uncomment this.
/*
extension L10nHelper on AppLocalizations? {
    String? get deviceSettingsTitle => this?.t('device_settings_title') ?? 'Device Settings';
    String? get displaySettingsTitle => this?.t('display_settings_title') ?? 'Display Settings';
    String? get themeSettingTitle => this?.t('theme_setting_title') ?? 'Theme';
    String? get homePageSettingTitle => this?.t('home_page_setting_title') ?? 'Home Page';
    String? get brightnessSettingTitle => this?.t('brightness_setting_title') ?? 'Brightness';
    // Add other keys here
}
*/

// Temporary l10n placeholder until AppLocalizations can be confirmed/fixed
// This allows the UI to build without full localization, using keys as text.
class L10nPlaceholder {
  String t(String key) => key; // Returns the key itself if no translation found
  String? get deviceSettingsTitle => t('device_settings_title');
  String? get displaySettingsTitle => t('display_settings_title');
  String? get themeSettingTitle => t('theme_setting_title');
  String? get homePageSettingTitle => t('home_page_setting_title');
  String? get brightnessSettingTitle => t('brightness_setting_title');
}

final l10n = L10nPlaceholder(); // Make a global or passed instance available
