#ifndef __CONTROLLER_SCANNER_H__
#define __CONTROLLER_SCANNER_H__
#include "BluetoothManager.h"
#include "BluetoothDevice.h"
#include "common.h"
#include <vector>
#include <memory>
extern "C" 
{
    struct controller_scan_result_t
    {
        char *name;
        long device;
        int rssi;
        uint16_t vendorId;
        uint16_t productId;
    };

    struct controller_scanner_callback_t
    {
        void (*on_scan_results)(long flutter_object, struct controller_scan_result_t *results, size_t count);
        void (*on_scan_failed)(long flutter_object, int errorCode);
    };

    struct controller_scanner_t
    {
        void *handle;
        long flutter_object;
        struct controller_scanner_callback_t callback;
    };

    long controller_scanner_create(long flutter_object, struct controller_scanner_callback_t *callback);
    void controller_scanner_destory(long native_object);
    void controller_scanner_startScan(long native_object);
    void controller_scanner_stopScan(long native_object);
}
namespace Topping
{
    class ControllerScanner : public BluetoothLeScanner::ScanCallback
    {
    public:
        struct ScanResult
        {
            BluetoothDevice device;
            int rssi;
            SupportedDevice *supportedDevice;
        };

        class ScanCallback
        {
        public:
            ScanCallback() {}
            virtual ~ScanCallback() {}
            virtual void onBatchScanResults(const std::vector<ScanResult> &results) = 0;
            virtual void onScanFailed(int errorCode) = 0;
        };

    public:
        ControllerScanner();
        ~ControllerScanner();
        void startScan(ScanCallback *callback);
        void stopScan(ScanCallback *callback);

    protected:
        virtual void onBatchScanResults(const std::vector<BluetoothLeScanner::ScanResult> &results) override;
        virtual void onScanFailed(int errorCode) override;

    private:
        bool isLe();
        uint16_t fromBe16(uint16_t value);
        uint32_t fromBe32(uint32_t value);
        bool matchSupportedDevice(SupportedDevice *&supportedDevice, const BluetoothLeScanner::ScanResult &result);

    private:
        BluetoothLeScanner *mBluetoothLeScanner;
        ScanCallback *mScanCallback;
    };
}
#endif