#include "BluetoothManager.h"
namespace Topping
{
    BluetoothManager::BluetoothManager() : mBluetoothLeAdapter(nullptr)
    {
    }

    BluetoothManager::~BluetoothManager()
    {
    }
    
    BluetoothManager &BluetoothManager::getInstance()
    {
        static BluetoothManager sBluetoothManager;
        return sBluetoothManager;
    }

    void BluetoothManager::registerBluetoothLeAdapter(BluetoothLeAdapter *bluetoothLeAdapter)
    {
        mBluetoothLeAdapter = bluetoothLeAdapter;
    }

    BluetoothLeAdapter *BluetoothManager::getBluetoothLeAdapter()
    {
        return mBluetoothLeAdapter;
    }
}