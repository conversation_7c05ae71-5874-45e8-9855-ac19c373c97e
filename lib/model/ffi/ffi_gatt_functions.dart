import 'dart:ffi';

import 'ffi_gatt_characteristic.dart';

base class FFIGattFunctions extends Struct {
  external Pointer<NativeFunction<Int64 Function(Int64)>> init;

  external Pointer<NativeFunction<Void Function(Int64)>> uninit;

  external Pointer<NativeFunction<Void Function(Int64)>> close;

  external Pointer<NativeFunction<Void Function(Int64)>> connect;

  external Pointer<NativeFunction<Void Function(Int64)>> disconnect;

  external Pointer<NativeFunction<Int32 Function(Int64, Int32)>> request_mtu;

  external Pointer<
    NativeFunction<Int32 Function(Int64, Pointer<FFIGattCharacteristic>)>
  >
  write_characteristic;

  external Pointer<
    NativeFunction<Int32 Function(Int64, Pointer<FFIGattCharacteristic>, Int32)>
  >
  set_characteristic_notification;

  external Pointer<NativeFunction<Int64 Function(Int64, Pointer<Char>)>>
  get_service;
}
