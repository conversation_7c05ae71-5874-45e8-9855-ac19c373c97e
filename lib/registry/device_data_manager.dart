import 'package:flutter_blue_plus/flutter_blue_plus.dart';

import '../model/bluetooth/ble_device.dart';
import '../model/enums/device_mode_type.dart';
import '../utils/log_util.dart';

/// 统一设备管理类
class DeviceDataManager {
  static final DeviceDataManager _instance = DeviceDataManager._internal();

  factory DeviceDataManager() => _instance;

  // 设备管理 - 多种索引方式
  final Map<int, BleDevice> _devicesById = {}; // ID索引
  final Map<int, BleDevice> _devicesByHandle = {}; // 句柄索引
  final Map<String, BleDevice> _devicesByName = {}; // 名称索引
  final Map<String, BleDevice> _devicesByMac = {}; // MAC地址索引

  DeviceDataManager._internal();

  /// 注册设备到所有索引
  void registerDevice(BleDevice device) {
    // 检查是否已存在相同句柄的设备
    BleDevice? existingDevice = getDeviceByHandle(device.nativeHandle);

    if (existingDevice != null) {
      // 如果已存在设备，但新设备有Flutter设备对象而现有设备没有
      if (existingDevice.flutterDevice == null &&
          device.flutterDevice != null) {
        Log.i("更新设备 ${device.name} 的 Flutter 设备对象");
        existingDevice.flutterDevice = device.flutterDevice;

        // 更新MAC地址索引
        final macAddress = device.flutterDevice!.remoteId.str;
        _devicesByMac[macAddress] = existingDevice;
      }

      // 更新RSSI值
      existingDevice.rssi = device.rssi;
      return;
    }

    // 添加新设备到所有索引
    _devicesById[device.id] = device;
    _devicesByHandle[device.nativeHandle] = device;
    _devicesByName[device.name] = device;

    // 如果有 FlutterDevice，添加到 MAC 地址索引
    if (device.flutterDevice != null) {
      final macAddress = device.flutterDevice!.remoteId.str;
      _devicesByMac[macAddress] = device;
      Log.i("注册设备并添加MAC索引: ${device.name}, MAC: $macAddress");
    } else {
      Log.i("注册设备(无MAC索引): ${device.name}, 句柄: ${device.nativeHandle}");
    }
  }

  /// 通过ID获取设备
  BleDevice? getDeviceById(int id) => _devicesById[id];

  /// 通过句柄获取设备
  BleDevice? getDeviceByHandle(int handle) => _devicesByHandle[handle];

  /// 通过名称获取设备
  BleDevice? getDeviceByName(String name) => _devicesByName[name];

  /// 通过MAC地址获取设备
  BleDevice? getDeviceByMac(String macAddress) => _devicesByMac[macAddress];

  /// 根据任何可用信息查找设备
  BleDevice? findDevice({
    int? id,
    int? handle,
    String? name,
    String? macAddress,
  }) {
    // 先检查句柄，因为这是最直接的标识
    if (handle != null && _devicesByHandle.containsKey(handle)) {
      return _devicesByHandle[handle];
    }

    // 然后检查MAC地址，因为这是唯一的
    if (macAddress != null && _devicesByMac.containsKey(macAddress)) {
      return _devicesByMac[macAddress];
    }

    // 再检查ID
    if (id != null && _devicesById.containsKey(id)) {
      return _devicesById[id];
    }

    // 最后检查名称，因为名称可能重复
    if (name != null && _devicesByName.containsKey(name)) {
      return _devicesByName[name];
    }

    // 如果没有精确匹配，尝试模糊匹配名称
    if (name != null && name.isNotEmpty) {
      // 尝试查找名称包含的设备
      for (var device in _devicesByName.values) {
        if (device.name.contains(name) || name.contains(device.name)) {
          Log.i("找到模糊匹配设备: ${device.name} (查找: $name)");
          return device;
        }
      }
    }

    return null;
  }

  /// 从持久化数据恢复设备，使用MAC地址作为唯一标识
  BleDevice restoreDeviceFromPersistent({
    required String id,
    required String name,
    required String macAddress,
    int? rssi,
    DeviceModeType deviceType = DeviceModeType.unknown, // 设备类型
  }) {
    Log.i("尝试从持久化数据恢复设备: $name, MAC: $macAddress deviceType: $deviceType");

    // 首先检查是否已存在相同MAC地址的设备
    if (_devicesByMac.containsKey(macAddress)) {
      Log.i("找到已存在的设备，使用现有实例");
      final device = _devicesByMac[macAddress]!;
      device.isPersistent = true; // 标记为持久化设备
      return device;
    }

    // 从MAC地址创建Flutter设备
    final remoteId = DeviceIdentifier(macAddress);
    final flutterDevice = BluetoothDevice(remoteId: remoteId);

    // 计算句柄...
    final handleBase = macAddress.hashCode;
    final idHash = id.hashCode;
    final combinedHandle =
        ((handleBase & 0xFFFFFFFF) << 16) | (idHash & 0xFFFF);

    // 创建BleDevice
    final device = BleDevice(
      id: int.parse(id),
      name: name,
      rssi: rssi ?? 50,
      nativeHandle: combinedHandle,
      flutterDevice: flutterDevice,
      deviceType: deviceType,
    );

    device.isPersistent = true; // 标记为持久化设备

    // 注册设备
    registerDevice(device);

    Log.i(
      "已恢复设备: $name (id: ${device.id}, handle: ${device.nativeHandle}, MAC: $macAddress, 类型: ${deviceType.toString().split('.').last})",
    );

    return device;
  }

  /// 合并设备信息，保留较新的设备但不丢失 flutterDevice
  void mergeDevice(BleDevice device) {
    // 查找现有设备
    BleDevice? existingDevice = findDevice(
      id: device.id,
      handle: device.nativeHandle,
      macAddress: device.flutterDevice?.remoteId.str,
    );

    if (existingDevice != null) {
      // 更新现有设备属性而不是完全替换
      existingDevice.rssi = device.rssi;
      // 可以添加其他需要更新的属性

      // 记录设备已更新
      Log.i(
        "DeviceManager: 更新设备 ${existingDevice.name} (RSSI: ${existingDevice.rssi})",
      );
    } else {
      // 只有新设备才注册
      registerDevice(device);
    }
  }

  /// 清除设备注册
  void unregisterDevice(BleDevice device) {
    _devicesById.remove(device.id);
    _devicesByHandle.remove(device.nativeHandle);
    _devicesByName.remove(device.name);

    // 如果有MAC地址，也从MAC索引中移除
    if (device.flutterDevice != null) {
      _devicesByMac.remove(device.flutterDevice!.remoteId.str);
    }

    Log.i(
      "DeviceManager: 移除设备 ${device.name} (id: ${device.id}, handle: ${device.nativeHandle})",
    );
  }

  /// 获取所有设备列表
  List<BleDevice> getAllDevices() {
    return _devicesById.values.toList();
  }

  /// 清除所有设备
  void clearAllDevices() {
    _devicesById.clear();
    _devicesByHandle.clear();
    _devicesByName.clear();
    _devicesByMac.clear();
    Log.i("DeviceManager: 已清除所有设备");
  }

  /// 清除所有扫描结果
  void clearScanResults() {
    // 找出所有要移除的设备(非持久化设备)
    final devicesToRemove =
        _devicesById.values
            .where((device) => device.isPersistent == false)
            .toList();

    // 只移除非持久化设备，避免清空后重新注册
    for (var device in devicesToRemove) {
      _devicesById.remove(device.id);
      _devicesByHandle.remove(device.nativeHandle);
      _devicesByName.remove(device.name);

      if (device.flutterDevice != null) {
        _devicesByMac.remove(device.flutterDevice!.remoteId.str);
      }
    }
  }

  /// 清除所有扫描结果，但保留已连接的设备
  void clearScanResultsExceptConnected(BleDevice connectedDevice) {
    // 先清空所有设备
    _devicesById.clear();
    _devicesByHandle.clear();
    _devicesByName.clear();
    _devicesByMac.clear();

    // 重新注册已连接设备
    registerDevice(connectedDevice);

    Log.i("DeviceManager: 已清除所有扫描设备，保留${connectedDevice.name}");
  }
}
