# Topping Control
|版本|修改者|描述|
|:-:|:-:|:-|
|V100|陈智勇|初版|
## 描述
Topping Control 是一套基于蓝牙BLE的Topping Control app和Topping设备端之间互联互通的传输控制协议。整套协议分为三部分，分别是广播协议、传输协议、设备控制协议。
## 协议版本号
协议版本号是一个32位无符号整数，用于指示app端和设备端实现的协议版本，版本号分为两部分，十位和个位表示副版本号，百位及以上表示主版本号，主版本号一致的两个协议必须保证相互兼容，副版本号则可以用于不引起兼容问题的一般升级。
## 广播协议
广播协议基于GAP层，描述了设备端广播报文格式和app端扫描发现设备。
### 设备端广播报文
设备端发送可连接广播，广播间隔可根据设备端功耗要求而定，广播最大传输字节是31字节，需要带有设备名称（类型0x09，长度是设备名长度+1）、自定义数据（类型0xff，长度0x0b），自定义数据分别是两字节厂商ID（拓品是0xf4fb）、两字节产品id、两字节设备id、四字节协议版本号。
|长度|类型|设备名|长度|类型|厂商id|产品id|设备id|协议版本
|:-:|:-:|:-:|:-:|:-:|:-:|:-:|:-:|:-:|
|0x08|0x09|Topping(根据实际名称，注意广播长度)|0x0b|0xff|0xf4fb|XXX(2 byte大端序)|XX(2 byte大端序)|XXX(4 byte大端序)
### app端发现设备
app端扫描广播包，根据厂商id、产品id等信息识别出可显示的设备，把设备名和信号强度显示出来。
## 传输协议
传输协议基于BLE GATT层进行数据的传输，定义了一个Service，并为这个Service定义了两个Characteristic，一个用于app向设备端发动数据、另一个用于设备端向app通知数据，并提供给app读取权限，其次还定义了Characteristic值的基本数据格式，用于封装上层数据。
### Service UUID
000090FB-0000-1000-8000-00805F9B34FB
### Write Characteristic UUID
00008EFA-0000-1000-8000-00805F9B34FB
### Read and notify Characteristic UUID
00009CF1-0000-1000-8000-00805F9B34FB
### Characteristic值的封装格式
上层数据通过json格式进行封装，封装后通过Characteristic值进行传输，基本封装类型为：
```json
{
	"cmd": 1,
	"type": 0,
	"session_id": 0,
    "errcode": 0,
    "errmsg": "",
	"data": {

	}
}
```
#### cmd：由设备控制协议进行定义，0xff及以下应该作为通用命令保留，设备控制协议应该使用大于0xff的范围。
#### type：0表示request；1表示response
#### session_id：当type为request时表示为request id，每次发送请求就+1；当type为response时为response id，等于请求的request id
#### errcode：错误码
#### errmsg：错误信息
#### data：data是一个json对象，用于传输命令附带的数据，如果没有则传空对象
#### 错误码
错误码零和负数属于通用错误码，具体的设备协议错误码应该用正数，避免跟通用错误码冲突。
|errcode|errmsg|
|:-:|:-|
|0|OK|
|-1|未知的cmd命令|
|-2|未知的app|
## 握手鉴权协议
握手鉴权协议用于app识别设备身份和设备识别app身份，并用于建立安全连接，协议命令：
|描述|cmd|type|data|数据类型|是否需要回答|
|:-:|:-:|:-:|:-:|:-:|:-:|
|app请求鉴权|0x01|0|app_version|string|是|
|-|-|-|app_random|string|-|
|鉴权回答|0x01|1|dev_version|string|-|
|-|-|-|dev_random|string|-|
|-|-|-|vendor_id|string|-|
|-|-|-|product_id|string|-|
|-|-|-|signature|string|-|
|请求鉴定app身份|0x02|0|vendor_id|string|是|
|-|-|-|signature|string|-|
|鉴权回答|0x02|1|-|-|-|
握手分为以下步骤：
1. app端扫描设备，并展示可展示的设备。
2. app端向设备发起连接，并建立起非安全连接。
3. 双端都需要对连接状态进行管理，对于非安全连接，只能接收鉴权相关的命令。
4. app端生成随机码app_random并发起请求鉴权cmd=0x01。
5. 设备端生成随机码dev_random，并生成签名，signature生成方式：对dev_version + dev_random + vendor_id + product_id + app_random组成的字符串用设备端私钥进行ecdsa签名，并进行basa64编码。
6. 设备端发起响应cmd=0x01。
7. app端对签名进行验签确定设备端身份，并对比app_version和dev_version主版本号确定是否兼容，验证通过则标记连接状态为安全连接并进行第8步，否则进行相应提示并断开非安全连接。
8. app端生成签名，signature生成方式：对vendor_id + dev_random组成的字符串用app端私钥进行ecdsa签名，并进行basa64编码。
9. app端发起请求鉴定app身份，cmd=0x02。
10. 设备端对app签名进行验签，验证通过则标记连接状态为安全连接，否则断开当前连接。
## 设备控制协议
设备控制协议基于传输协议并根据具体设备端的需求进行定义，设备端只需要实现自己的设备控制协议即可，app需要实现所有的设备控制协议。
### Demo device
Demo device是一个基于linux IPC的一个模拟设备，旨在通过模拟实现进行代码调试和给真实设备提供一个接口实现和接口调用的例子。
#### VENDOR ID
0x51ef
#### PRODUCT ID
0x52ef
#### PRIVATE KEY
0x2e, 0xb1, 0x4e, 0xd4, 0x76, 0xc4, 0x50, 0xd3, 0x1a, 0x0e, 0x49, 0x25, 0x05, 0x1e, 0x18, 0x51, 0xea, 0xd4, 0x44, 0xdc, 0xff, 0x03, 0x9e, 0xb5, 0xb8, 0xe0, 0x19, 0x38, 0xc4, 0xe9, 0x81, 0xf2
#### CMD
|描述|cmd|type|data|数据类型|是否需要回答|
|:-:|:-:|:-:|:-:|:-:|:-:|
|发送消息|0X100|0|-|json|-|
### DX5II
DX5II
#### VENDOR ID
0x5f4b
#### PRODUCT ID
0x6ac0
#### PRIVATE KEY
0x9a, 0x01, 0xc3, 0x16, 0x67, 0x4c, 0x4c, 0xfd, 0xe3, 0xcc, 0xe4, 0x78, 0x17, 0x3f, 0xe5, 0x16, 0xb1, 0xaf, 0x6b, 0x03, 0xcb, 0x72, 0xfd, 0x33, 0x3d, 0x63, 0x16, 0x32, 0x0a, 0x9d, 0x3f, 0xa4
#### CMD
|描述|cmd|type|data|数据类型|是否需要回答|说明|
|:-:|:-:|:-:|:-:|:-:|:-:|:-:|
|开关机|0X100|0|is_on|boolean|是|-|
|回答|0X100|1|is_on|boolean|-|-|
|设置设备名称|0X102|0|device_name|string|是|蓝牙名称长度不超过32，此命令蓝牙会重启|
|回答|0X102|1|device_name|string|-|-|
|设置设备音量|0X104|0|volume|number|是|范围-99dB-0dB|
|回答|0X104|1|volume|number|-|-|
|设置静音|0X106|0|is_mute|boolean|是|-|
|回答|0X106|1|is_mute|boolean|-|-|
|设置输入类型|0X108|0|input_type|number|是|0:USB;1:光纤;2:同轴;3:蓝牙|
|回答|0X108|1|input_type|number|-|-|
|设置输出类型|0X10A|0|output_type|number|是|0:单端;1:平衡;2:单端+平衡;3:关闭|
|回答|0X10A|1|output_type|number|-|-|
|激活耳放输出|0X10C|0|enable|boolean|是||
|回答|0X10C|1|enable|boolean|-|-|
|设置耳放增益|0X10E|0|gain_type|number|是|0:低增益;1:高增益|
|回答|0X10E|1|gain_type|number|-|-|
|设置显示模式|0X110|0|display_mode|number|是|0:Normal;1:vu;2:fft|
|回答|0X110|1|display_mode|number|-|-|
|设置主题|0X112|0|theme|number|是|0:极光;1:橙色;2:秘鲁色;3:豆绿色;4:深卡其色;5:玫瑰棕色;6:蓝色;7:幻紫色;8:白色|
|回答|0X112|1|theme|number|-|-|
|设置开关机触发|0X114|0|trigger_type|number|是|0:信号;1:12V;2:关闭|
|回答|0X114|1|trigger_type|number|-|-|
|设置声道平衡|0X116|0|balance|number|是|-|
|回答|0X116|1|balance|number|-|-|
|设置滤波器|0X11A|0|filter_type|number|是|0:最小相位;1:线性相位快速滚降变迹;2:线性相位快速滚降;3:线性相位快速滚降低纹波;4:线性相位慢速滚降;5:最小相位快速滚降;6:最小相位慢速滚降;7:最小相位慢速滚降低扩散|
|回答|0X11A|1|filter_type|number|-|-|
|设置解码模式|0X11C|0|decode_mode|number|是|0:前级;1:DAC|
|回答|0X11C|1|decode_mode|number|-|-|
|激活音频蓝牙|0X11E|0|enable|boolean|是|-|
|回答|0X11E|1|enable|boolean|-|-|
|激活蓝牙APTX|0X120|0|enable|boolean|是|此命令蓝牙会重启|
|回答|0X120|1|enable|boolean|-|-|
|激活遥控|0X122|0|enable|boolean|是|-|
|回答|0X122|1|enable|boolean|-|-|
|设置多功能按键|0X124|0|key_type|number|是|0:静音;1:输入选择;2:线路输出选择;3:耳放输出选择;4:主页选择;5:亮度选择;6:熄屏;7:PCM滤波器选择;8:PEQ选择|
|回答|0X124|1|key_type|number|-|-|
|设置USB模式|0X126|0|usb_mode|number|是|0:USB1.0;1:USB2.0|
|回答|0X126|1|usb_mode|number|-|-|
|设置屏幕亮度|0X128|0|brightness_type|number|是|0:低;1:中;2:高;3:自动|
|回答|0X128|1|brightness_type|number|-|-|
|设置语言|0X12A|0|language|number|是|0:英文;1:中文|
|回答|0X12A|1|language|number|-|-|
|重置高级|0X12C|0|-|-|是|-|
|回答|0X12C|1|-|-|-|-|
|恢复出厂设置|0X12E|0|-|-|是|-|
|回答|0X12E|1|-|-|-|-|
|获取所有设置|0X130|0|-|-|是|-|
|回答|0X130|1|is_on|boolean|-|-|
|-|-|-|device_name|string|-|-|
|-|-|-|volume|number|-|-|
|-|-|-|is_mute|boolean|-|-|
|-|-|-|input_type|number|-|-|
|-|-|-|output_type|number|-|-|
|-|-|-|headphone_enable|boolean|-|-|
|-|-|-|headphone_gain|number|-|-|
|-|-|-|display_mode|number|-|-|
|-|-|-|theme|number|-|-|
|-|-|-|power_trigger|number|-|-|
|-|-|-|left_balance|number|-|-|
|-|-|-|right_balance|number|-|-|
|-|-|-|pcm_filter|number|-|-|
|-|-|-|decode_mode|number|-|-|
|-|-|-|audio_bt_enable|boolean|-|-|
|-|-|-|aptx_enable|boolean|-|-|
|-|-|-|remote_enable|boolean|-|-|
|-|-|-|multifunction_key|number|-|-|
|-|-|-|usb_mode|number|-|-|
|-|-|-|screen_brightness|number|-|-|
|-|-|-|language|number|-|-|
|-|-|-|sampling|number|-|0:UNLOCK;      1-16:44.1K - 49.15M|
|获取采样率|0x130|0|sampling|number|是|0:UNLOCK;      1~16:44.1K~49.15M|
|回答|0x130|1|sampling|number|-|-|
### D900
D900
#### VENDOR ID
0xa85e
#### PRODUCT ID
0x561d
#### PRIVATE KEY
0x6c, 0x60, 0xce, 0x78, 0x72, 0x8f, 0x45, 0xef, 0x0e, 0x76, 0x96, 0xcc, 0x35, 0x75, 0x7d, 0x8a, 0x90, 0xc2, 0xae, 0xbe, 0xaf, 0xc6, 0x55, 0x4d, 0x06, 0x2c, 0xa4, 0xe2, 0x8f, 0xd2, 0xf4, 0xfb
#### CMD
|描述|cmd|type|data|数据类型|是否需要回答|说明|
|:-:|:-:|:-:|:-:|:-:|:-:|:-:|
|开关机|0X100|0|is_on|boolean|是|-|
|回答|0X100|1|is_on|boolean|-|-|
|设置设备音量|0X102|0|volume|number|是|范围-99dB--8dB|
|回答|0X102|1|volume|number|-|-|
|设置静音|0X104|0|is_mute|boolean|是|-|
|回答|0X104|1|is_mute|boolean|-|-|
|设置输入类型|0X106|0|input_type|number|是|0:USB;1:光纤1;2:光纤2;3:同轴1;4:同轴2;5:AES;6:IIS;7:蓝牙|
|回答|0X106|1|input_type|number|-|-|
|设置输出类型|0X108|0|output_type|number|是|0:DAC;1:前级;2:全部;|
|回答|0X108|1|output_type|number|-|-|
|设置显示模式|0X10a|0|display_mode|number|是|0:Normal;1:vu;2:fft|
|回答|0X10a|1|display_mode|number|-|-|
|设置主题|0X10c|0|theme|number|是|0:极光;1:橙色;2:秘鲁色;3:豆绿色;4:深卡其色;5:玫瑰棕色;6:蓝色;7:幻紫色;8:白色|
|回答|0X10c|1|theme|number|-|-|
|设置开关机触发|0X10e|0|trigger_type|number|是|0:信号;1:12V;2:关闭|
|回答|0X10e|1|trigger_type|number|-|-|
|USB选择|0X110|0|type|number|是|0:type-c;1:type-b;2:auto|
|回答|0X110|1|type|number|-|-|
|设置声道平衡|0X112|0|balance|number|是|-|
|回答|0X112|1|balance|number|-|-|
|激活音频蓝牙|0X114|0|enable|boolean|是|-|
|回答|0X114|1|enable|boolean|-|-|
|激活蓝牙APTX|0X116|0|enable|boolean|是|此命令蓝牙会重启|
|回答|0X116|1|enable|boolean|-|-|
|激活遥控|0X118|0|enable|boolean|是|-|
|回答|0X118|1|enable|boolean|-|-|
|设置多功能按键|0X11a|0|key_type|number|是|0:输入选择;1:输出选择;2:主页选择;3:亮度选择;4:熄屏;5:PEQ选择;6:静音;|
|回答|0X11a|1|key_type|number|-|-|
|USB DSD激活|0X11c|0|enable|boolean|是|-|
|回答|0X11c|1|enable|boolean|-|-|
|设置USB模式|0X11e|0|usb_mode|number|是|0:USB1.0;1:USB2.0|
|回答|0X11e|1|usb_mode|number|-|-|
|设置iis相位|0X120|0|phase|number|是|0:标准;1:反向|
|回答|0X120|1|phase|number|-|-|
|设置iis dsd通道|0X122|0|channel|number|是|0:标准;1:交换|
|回答|0X122|1|channel|number|-|-|
|设置屏幕亮度|0X124|0|brightness_type|number|是|0:低;1:中;2:高;3:自动|
|回答|0X124|1|brightness_type|number|-|-|
|设置语言|0X126|0|language|number|是|0:英文;1:中文|
|回答|0X126|1|language|number|-|-|
|恢复出厂设置|0X128|0|-|-|是|-|
|回答|0X128|1|-|-|-|-|
|获取所有设置|0X12a|0|-|-|是|-|
|回答|0X12a|1|is_on|boolean|-|-|
|-|-|-|device_name|string|-|-|
|-|-|-|volume|number|-|-|
|-|-|-|is_mute|boolean|-|-|
|-|-|-|input_type|number|-|-|
|-|-|-|output_type|number|-|-|
|-|-|-|display_mode|number|-|-|
|-|-|-|theme|number|-|-|
|-|-|-|power_trigger|number|-|-|
|-|-|-|usb_select|number|-|-|
|-|-|-|balance|number|-|-|
|-|-|-|audio_bt_enable|boolean|-|-|
|-|-|-|aptx_enable|boolean|-|-|
|-|-|-|remote_enable|boolean|-|-|
|-|-|-|multifunction_key|number|-|-|
|-|-|-|usb_dsd_enable|boolean|-|-|
|-|-|-|usb_mode|number|-|-|
|-|-|-|iis_phase|number|-|-|
|-|-|-|iis_channel|number|-|-|
|-|-|-|screen_brightness|number|-|-|
|-|-|-|language|number|-|-|
|-|-|-|sampling|number|-|-|

