2025-05-15 09:58:05.015 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.015 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:05.015 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   _ExamplesMenuState._addLog (package:test1_example/examples_menu.dart:84:9)[0m
2025-05-15 09:58:05.015 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:05.015 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 尝试直接连接QCC5125设备 (MAC: 53:4A:52:FE:00:01)...[0m
2025-05-15 09:58:05.016 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.016 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.016 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:05.016 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   _ExamplesMenuState._addLog (package:test1_example/examples_menu.dart:84:9)[0m
2025-05-15 09:58:05.016 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:05.016 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 已将设备注册到DeviceDataManager: QCC5125, 句柄: 10001[0m
2025-05-15 09:58:05.016 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.016 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.016 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:05.017 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   _ExamplesMenuState._addLog (package:test1_example/examples_menu.dart:84:9)[0m
2025-05-15 09:58:05.017 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:05.017 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 创建直连设备成功，准备连接: QCC5125 (MAC: 53:4A:52:FE:00:01)[0m
2025-05-15 09:58:05.017 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.017 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.017 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:05.017 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)[0m
2025-05-15 09:58:05.017 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:05.017 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 D900设备管理器已初始化[0m
2025-05-15 09:58:05.017 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.018 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.018 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:05.018 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   D900DeviceManager.connect (package:topping_ble_control/device/d900/d900_device_manager.dart:568:9)[0m
2025-05-15 09:58:05.018 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:05.018 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 D900: 连接到设备, 句柄: 10001[0m
2025-05-15 09:58:05.018 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.018 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.018 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:05.018 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)[0m
2025-05-15 09:58:05.018 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:05.019 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 设备[D900] 连接状态: 连接到设备, 句柄: 10001[0m
2025-05-15 09:58:05.019 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.019 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.019 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:05.019 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   ToppingDeviceManager.connectDevice (package:topping_ble_control/device/topping_device_manager.dart:334:9)[0m
2025-05-15 09:58:05.019 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:05.019 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 D900 连接时间打印 --- 准备连接设备，句柄: 10001, 时间戳: 1747274285019[0m
2025-05-15 09:58:05.019 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.019 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.019 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:05.019 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   ToppingDeviceManager.connectDevice (package:topping_ble_control/device/topping_device_manager.dart:395:9)[0m
2025-05-15 09:58:05.019 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:05.019 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 D900 发送连接中事件完成, 时间戳: 1747274285019[0m
2025-05-15 09:58:05.019 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.020 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.020 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:05.020 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   ToppingDeviceManager.connectDevice (package:topping_ble_control/device/topping_device_manager.dart:398:9)[0m
2025-05-15 09:58:05.020 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:05.020 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 D900 调用底层连接函数, 时间戳: 1747274285020[0m
2025-05-15 09:58:05.020 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.020 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.020 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:05.020 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleOperationManager.connect (package:topping_ble_control/bluetooth/ble_operation_manager.dart:152:11)[0m
2025-05-15 09:58:05.020 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:05.020 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 开始连接设备，句柄: 10001[0m
2025-05-15 09:58:05.020 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.020 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.020 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:05.020 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleConnector.connectToDevice (package:topping_ble_control/bluetooth/connection/ble_connector.dart:34:9)[0m
2025-05-15 09:58:05.020 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:05.020 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 尝试连接设备，句柄: 10001[0m
2025-05-15 09:58:05.021 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.021 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.021 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:05.021 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)[0m
2025-05-15 09:58:05.021 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:05.021 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 设备[设备] 连接状态: 尝试连接设备 (句柄: 10001)[0m
2025-05-15 09:58:05.021 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.021 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.021 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:05.021 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleConnector._prepareDeviceForConnection (package:topping_ble_control/bluetooth/connection/ble_connector.dart:115:9)[0m
2025-05-15 09:58:05.021 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:05.021 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 准备设备连接，句柄: 10001[0m
2025-05-15 09:58:05.021 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.022 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.022 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:05.022 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleConnector._prepareDeviceForConnection (package:topping_ble_control/bluetooth/connection/ble_connector.dart:160:9)[0m
2025-05-15 09:58:05.022 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:05.022 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 设备准备就绪[0m
2025-05-15 09:58:05.022 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.022 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.022 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:05.022 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleConnector._connectWithTimeout (package:topping_ble_control/bluetooth/connection/ble_connector.dart:179:9)[0m
2025-05-15 09:58:05.022 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:05.022 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 尝试连接设备: QCC5125, 时间戳: 1747274285022[0m
2025-05-15 09:58:05.022 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.024 21284-21284 ViewRootImplExtImpl     com.example.test1_example            D  the up motion event handled by client, just return
2025-05-15 09:58:05.026 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.027 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:05.027 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   D900DeviceBindings.connect (package:topping_ble_control/device/d900/d900_device_bindings.dart:496:11)[0m
2025-05-15 09:58:05.027 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:05.027 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 D900DeviceBindings: D900DeviceBindings: 连接设备, 句柄: 10001[0m
2025-05-15 09:58:05.027 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.027 21284-21284 Topping Controller      com.example.test1_example            I  [BLE日志] ControllerClient::connect被调用, callback: 0xb4000078ce9534e8
2025-05-15 09:58:05.027 21284-21284 Topping Controller      com.example.test1_example            I  [BLE日志] 构造FlutterGatt对象, 回调指针: 0xb4000078ce953508
2025-05-15 09:58:05.027 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.027 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:05.027 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleFfiInitializer._registerGattFunctions.<anonymous closure> (package:topping_ble_control/bluetooth/ffi/ble_ffi_initializer.dart:129:13)[0m
2025-05-15 09:58:05.027 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:05.027 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 C++调用init, nativeObject: -5476376629556584928[0m
2025-05-15 09:58:05.027 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.028 21284-21284 Topping Controller      com.example.test1_example            I  [BLE日志] FlutterGatt回调状态:
2025-05-15 09:58:05.028 21284-21284 Topping Controller      com.example.test1_example            I    - this指针: 0xb400007873075e20
2025-05-15 09:58:05.028 21284-21284 Topping Controller      com.example.test1_example            I    - mCallback指针: 0xb4000078ce953508
2025-05-15 09:58:05.028 21284-21284 Topping Controller      com.example.test1_example            I    - mFlutterObject: 1747274285027
2025-05-15 09:58:05.028 21284-21284 Topping Controller      com.example.test1_example            I    - mCallback有效
2025-05-15 09:58:05.028 21284-21284 Topping Controller      com.example.test1_example            I    - mFunctions.init: 0x77de800118
2025-05-15 09:58:05.028 21284-21284 Topping Controller      com.example.test1_example            I    - mFunctions.write_characteristic: 0x77de800148
2025-05-15 09:58:05.028 21284-21284 Topping Controller      com.example.test1_example            I    - mFunctions.set_characteristic_notification: 0x77de800150
2025-05-15 09:58:05.028 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.028 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:05.028 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleBindings._gattConnect (package:topping_ble_control/bluetooth/ble_bindings.dart:98:9)[0m
2025-05-15 09:58:05.028 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:05.028 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 Connecting to device[0m
2025-05-15 09:58:05.028 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.028 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.028 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:05.028 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleFfiInitializer._registerGattFunctions.<anonymous closure> (package:topping_ble_control/bluetooth/ffi/ble_ffi_initializer.dart:142:13)[0m
2025-05-15 09:58:05.028 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:05.028 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 C++调用connect, flutterObject: 1747274285027[0m
2025-05-15 09:58:05.028 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.029 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.029 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:05.029 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleFfiInitializer._handleConnect (package:topping_ble_control/bluetooth/ffi/ble_ffi_initializer.dart:188:9)[0m
2025-05-15 09:58:05.029 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:05.029 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 连接请求，句柄: 10001[0m
2025-05-15 09:58:05.029 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.029 21284-21284 Topping Controller      com.example.test1_example            I  [BLE日志] ControllerClient状态(connect完成):
2025-05-15 09:58:05.029 21284-21284 Topping Controller      com.example.test1_example            I    - this指针: 0xb4000078ce953508
2025-05-15 09:58:05.029 21284-21284 Topping Controller      com.example.test1_example            I    - mBluetoothGatt: 0xb400007873075e20
2025-05-15 09:58:05.029 21284-21284 Topping Controller      com.example.test1_example            I    - mCallback: 0xb4000078ce9534e8
2025-05-15 09:58:05.029 21284-21284 Topping Controller      com.example.test1_example            I    - mState: 2
2025-05-15 09:58:05.029 21284-21284 Topping Controller      com.example.test1_example            I    - mBleMtu: 260
2025-05-15 09:58:05.029 21284-21284 Topping Controller      com.example.test1_example            I    - BluetoothGatt有效
2025-05-15 09:58:05.029 21284-21284 Topping Controller      com.example.test1_example            I    - Callback有效
2025-05-15 09:58:05.030 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.030 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:05.030 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   D900DeviceManager.connectNative (package:topping_ble_control/device/d900/d900_device_manager.dart:79:9)[0m
2025-05-15 09:58:05.030 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:05.030 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 D900 底层连接函数调用完成, 时间戳: 1747274285030[0m
2025-05-15 09:58:05.031 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.031 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.031 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:05.031 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)[0m
2025-05-15 09:58:05.031 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:05.031 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 设备[D900] 连接状态: 调用底层connectNative, 句柄: 10001[0m
2025-05-15 09:58:05.031 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.031 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.031 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:05.031 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   ToppingDeviceManager.connectDevice (package:topping_ble_control/device/topping_device_manager.dart:420:11)[0m
2025-05-15 09:58:05.031 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:05.031 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 D900 底层连接函数调用完成, 时间戳: 1747274285031[0m
2025-05-15 09:58:05.031 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.045 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.045 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:05.045 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleOperationManager.connect (package:topping_ble_control/bluetooth/ble_operation_manager.dart:152:11)[0m
2025-05-15 09:58:05.045 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:05.045 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 开始连接设备，句柄: 10001[0m
2025-05-15 09:58:05.045 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.046 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.046 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:05.046 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleConnector.connectToDevice (package:topping_ble_control/bluetooth/connection/ble_connector.dart:34:9)[0m
2025-05-15 09:58:05.046 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:05.046 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 尝试连接设备，句柄: 10001[0m
2025-05-15 09:58:05.046 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.046 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.046 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:05.046 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)[0m
2025-05-15 09:58:05.046 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:05.046 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 设备[设备] 连接状态: 尝试连接设备 (句柄: 10001)[0m
2025-05-15 09:58:05.046 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.046 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.046 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:05.046 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleConnector._prepareDeviceForConnection (package:topping_ble_control/bluetooth/connection/ble_connector.dart:115:9)[0m
2025-05-15 09:58:05.046 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:05.046 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 准备设备连接，句柄: 10001[0m
2025-05-15 09:58:05.046 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.046 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.047 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:05.047 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleConnector._prepareDeviceForConnection (package:topping_ble_control/bluetooth/connection/ble_connector.dart:160:9)[0m
2025-05-15 09:58:05.047 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:05.047 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 设备准备就绪[0m
2025-05-15 09:58:05.047 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.047 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.047 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:05.047 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleConnector._connectWithTimeout (package:topping_ble_control/bluetooth/connection/ble_connector.dart:179:9)[0m
2025-05-15 09:58:05.047 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:05.047 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 尝试连接设备: QCC5125, 时间戳: 1747274285047[0m
2025-05-15 09:58:05.047 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.048 21284-21284 [FBP-Android]           com.example.test1_example            D  [FBP] onMethodCall: connect
2025-05-15 09:58:05.049 21284-21284 BluetoothGatt           com.example.test1_example            D  connect() - device: XX:XX:XX:XX:00:01, auto: false
2025-05-15 09:58:05.049 21284-21284 BluetoothGatt           com.example.test1_example            D  registerApp()
2025-05-15 09:58:05.049 21284-21284 BluetoothGatt           com.example.test1_example            D  registerApp() - UUID=be27c68e-989c-474f-b80e-1fc978bb92a1
2025-05-15 09:58:05.052 21284-21532 BluetoothGatt           com.example.test1_example            D  onClientRegistered() - status=0 clientIf=15
2025-05-15 09:58:05.054 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.054 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:05.054 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)[0m
2025-05-15 09:58:05.054 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:05.055 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 发送到[测试设备]: 测试蓝牙发送数据[0m
2025-05-15 09:58:05.055 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.060 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.060 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:05.060 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)[0m
2025-05-15 09:58:05.060 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:05.060 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 接收自[测试设备]: 测试蓝牙接收数据[0m
2025-05-15 09:58:05.060 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.069 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.069 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:05.069 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)[0m
2025-05-15 09:58:05.069 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:05.069 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 设备[测试设备] 连接状态: 已连接[0m
2025-05-15 09:58:05.069 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.076 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.076 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:05.076 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)[0m
2025-05-15 09:58:05.076 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:05.076 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 执行命令: 测试命令执行[0m
2025-05-15 09:58:05.076 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.082 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.082 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:05.082 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   D900DeviceManager._testBluetoothLogging (package:topping_ble_control/device/d900/d900_device_manager.dart:64:9)[0m
2025-05-15 09:58:05.082 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:05.082 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 测试蓝牙日志已写入[0m
2025-05-15 09:58:05.082 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.261 21284-21327 BluetoothGatt           com.example.test1_example            D  onClientConnectionState() - status=0 clientIf=15 connected=true device=53:4A:52:FE:00:01
2025-05-15 09:58:05.261 21284-21327 [FBP-Android]           com.example.test1_example            D  [FBP] onConnectionStateChange:connected
2025-05-15 09:58:05.261 21284-21327 [FBP-Android]           com.example.test1_example            D  [FBP]   status: SUCCESS
2025-05-15 09:58:05.263 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.263 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:05.263 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleConnector._setupConnectionStateListener.<anonymous closure> (package:topping_ble_control/bluetooth/connection/ble_connector.dart:65:15)[0m
2025-05-15 09:58:05.263 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:05.263 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 监听到连接状态变化: BluetoothConnectionState.connected, 时间戳: 1747274285262[0m
2025-05-15 09:58:05.263 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.263 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.263 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:05.263 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)[0m
2025-05-15 09:58:05.263 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:05.263 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 设备[QCC5125] 连接状态: 状态变化为 BluetoothConnectionState.connected[0m
2025-05-15 09:58:05.263 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.264 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.264 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:05.264 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   DeviceDataManager.registerDevice (package:topping_ble_control/registry/device_data_manager.dart:52:11)[0m
2025-05-15 09:58:05.264 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:05.264 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 注册设备并添加MAC索引: QCC5125, MAC: 53:4A:52:FE:00:01[0m
2025-05-15 09:58:05.264 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.264 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.264 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:05.264 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   DeviceDataManager.clearScanResultsExceptConnected (package:topping_ble_control/registry/device_data_manager.dart:246:9)[0m
2025-05-15 09:58:05.264 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:05.264 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 DeviceManager: 已清除所有扫描设备，保留QCC5125[0m
2025-05-15 09:58:05.264 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.264 21284-21284 [FBP-Android]           com.example.test1_example            D  [FBP] onMethodCall: connect
2025-05-15 09:58:05.264 21284-21284 [FBP-Android]           com.example.test1_example            D  [FBP] already connected
2025-05-15 09:58:05.540 21284-21284 flutter                 com.example.test1_example            I  data = 5ec7fffe006578da358b4b0a80201445f772c70e2c2acacd84f81ee1c00f2a41887b4f8366f79ec3a9308ea02681f2448692029973b6c19f96becb299940fc6f972f28408074d150153ac6f3e634922e2629bb1b2c694fc10db4ad723f96635ed0da0bac4e221271c3016f
2025-05-15 09:58:05.540 21284-21284 flutter                 com.example.test1_example            I  data1 = 5ec7fffe00b378da258bc90e82301445ffe56d251154064958688c71835014d195a1b496415aa47520847f97e8eee69e737ac86a02ee4c03d535145c5d0349a52c04bf16e36f6840db3613e48fc65d4b062e8006245529b83dbc2827a2fdd960cd6cc31c992c184fd5b31d2b38e8f792d024c8a38a61b4893fbb49c492c7f66d0589e1a0f9ad489b05aef353e88b4bdc618cedca6ff8d4ae7484b9e0e4b88f55c34cb93e3b6459f250a195853c0f86e10b34023d41b437cb16
2025-05-15 09:58:05.540 21284-21284 flutter                 com.example.test1_example            I  result = 9419925525401011202185313975101283220692471141991444422022051322483022519215426513612379131102247158195169481421603812924268134146215311518219315915019020341153642521111514740641281162098021581982432305214646384118727441057919313180173114631509994208218111727834181131951111
2025-05-15 09:58:05.541 21284-21284 flutter                 com.example.test1_example            I  result1 = 94199255254017912021837139201141304820692552291093717846738810414011313180202091491611801506590164117321321271512322382301581151222001062238763213532092933731654441912222711110464219541922814319893756461286368541184611884039162253217962051081952815344247921317929435623224714620836200163138971801376318773196146199246109513722516024917372155517424383232139752209714023720211124821217411613218522422818414385195761856259100892428016114913360151342251152261651805520322
2025-05-15 09:58:05.545 21284-21284 flutter                 com.example.test1_example            I  [38;5;196m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.545 21284-21284 flutter                 com.example.test1_example            I  [38;5;196m│ #0   Log.e (package:topping_ble_control/utils/log_util.dart:32:13)[0m
2025-05-15 09:58:05.545 21284-21284 flutter                 com.example.test1_example            I  [38;5;196m│ #1   _BluetoothAppState._addLog (package:test1_example/main.dart:1098:9)[0m
2025-05-15 09:58:05.545 21284-21284 flutter                 com.example.test1_example            I  [38;5;196m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:05.545 21284-21284 flutter                 com.example.test1_example            I  [38;5;196m│ ⛔ BLE Example 初始化完成 (使用 DeviceFactory)[0m
2025-05-15 09:58:05.545 21284-21284 flutter                 com.example.test1_example            I  [38;5;196m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:05.618 21284-21284 [FBP-Android]           com.example.test1_example            D  [FBP] onMethodCall: requestMtu
2025-05-15 09:58:05.619 21284-21284 BluetoothGatt           com.example.test1_example            D  configureMTU() - device: XX:XX:XX:XX:00:01 mtu: 512
2025-05-15 09:58:05.623 21284-21323 BluetoothGatt           com.example.test1_example            D  onConfigureMTU() - Device=53:4A:52:FE:00:01 mtu=263 status=0
2025-05-15 09:58:05.623 21284-21323 [FBP-Android]           com.example.test1_example            D  [FBP] onMtuChanged:
2025-05-15 09:58:05.623 21284-21323 [FBP-Android]           com.example.test1_example            D  [FBP]   mtu: 263
2025-05-15 09:58:05.623 21284-21323 [FBP-Android]           com.example.test1_example            D  [FBP]   status: GATT_SUCCESS (0)
2025-05-15 09:58:05.630 21284-21284 [FBP-Android]           com.example.test1_example            D  [FBP] onMethodCall: discoverServices
2025-05-15 09:58:05.630 21284-21284 BluetoothGatt           com.example.test1_example            D  discoverServices() - device: XX:XX:XX:XX:00:01
2025-05-15 09:58:05.635 21284-21323 BluetoothGatt           com.example.test1_example            D  onSearchComplete() = Device=53:4A:52:FE:00:01 Status=0
2025-05-15 09:58:05.636 21284-21323 [FBP-Android]           com.example.test1_example            D  [FBP] onServicesDiscovered:
2025-05-15 09:58:05.636 21284-21323 [FBP-Android]           com.example.test1_example            D  [FBP]   count: 5
2025-05-15 09:58:05.636 21284-21323 [FBP-Android]           com.example.test1_example            D  [FBP]   status: 0GATT_SUCCESS
2025-05-15 09:58:05.998 21284-21284 [FBP-Android]           com.example.test1_example            D  [FBP] onMethodCall: requestMtu
2025-05-15 09:58:05.998 21284-21284 BluetoothGatt           com.example.test1_example            D  configureMTU() - device: XX:XX:XX:XX:00:01 mtu: 512
2025-05-15 09:58:06.003 21284-21323 BluetoothGatt           com.example.test1_example            D  onConfigureMTU() - Device=53:4A:52:FE:00:01 mtu=263 status=0
2025-05-15 09:58:06.004 21284-21323 [FBP-Android]           com.example.test1_example            D  [FBP] onMtuChanged:
2025-05-15 09:58:06.004 21284-21323 [FBP-Android]           com.example.test1_example            D  [FBP]   mtu: 263
2025-05-15 09:58:06.005 21284-21323 [FBP-Android]           com.example.test1_example            D  [FBP]   status: GATT_SUCCESS (0)
2025-05-15 09:58:06.012 21284-21284 [FBP-Android]           com.example.test1_example            D  [FBP] onMethodCall: setNotifyValue
2025-05-15 09:58:06.014 21284-21284 BluetoothGatt           com.example.test1_example            D  setCharacteristicNotification() - uuid: 00002a05-0000-1000-8000-00805f9b34fb enable: true
2025-05-15 09:58:06.113 21284-21323 [FBP-Android]           com.example.test1_example            D  [FBP] onDescriptorWrite:
2025-05-15 09:58:06.114 21284-21323 [FBP-Android]           com.example.test1_example            D  [FBP]   chr: 2a05
2025-05-15 09:58:06.114 21284-21323 [FBP-Android]           com.example.test1_example            D  [FBP]   desc: 2902
2025-05-15 09:58:06.115 21284-21323 [FBP-Android]           com.example.test1_example            D  [FBP]   status: GATT_SUCCESS (0)
2025-05-15 09:58:06.119 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.120 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:06.120 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleConnector._discoverServices (package:topping_ble_control/bluetooth/connection/ble_connector.dart:205:11)[0m
2025-05-15 09:58:06.120 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:06.120 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 服务发现完成，发现 5 个服务, 时间戳: 1747274286119[0m
2025-05-15 09:58:06.120 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.121 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.121 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:06.122 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleConnector._registerServices (package:topping_ble_control/bluetooth/connection/ble_connector.dart:218:9)[0m
2025-05-15 09:58:06.122 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:06.122 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 注册 5 个服务[0m
2025-05-15 09:58:06.122 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.122 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.123 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:06.123 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleNativeNotifier.notifyConnectionStateChange (package:topping_ble_control/bluetooth/ffi/ble_native_notifier.dart:24:9)[0m
2025-05-15 09:58:06.123 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:06.123 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 通知连接状态变化: FfiBluetoothProfileState.stateConnected -> FfiBluetoothProfileState.stateConnected nativeObject: -5476376629556584928[0m
2025-05-15 09:58:06.123 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.124 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.124 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:06.124 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)[0m
2025-05-15 09:58:06.124 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:06.124 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 设备[设备] 连接状态: 状态变化为 FfiBluetoothProfileState.stateConnected[0m
2025-05-15 09:58:06.124 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.125 21284-21284 Topping Controller      com.example.test1_example            I  onStateChange; state:0
2025-05-15 09:58:06.126 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.126 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:06.126 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   D900DeviceBindings._onStateChange (package:topping_ble_control/device/d900/d900_device_bindings.dart:287:9)[0m
2025-05-15 09:58:06.126 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:06.127 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 Flutter回调: onStateChange, flutterObject: 1747274285018, state: 0[0m
2025-05-15 09:58:06.127 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.127 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.128 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:06.128 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   D900DeviceManager._handleStateChange (package:topping_ble_control/device/d900/d900_device_manager.dart:142:9)[0m
2025-05-15 09:58:06.128 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:06.128 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 D900: 收到状态变更: BleConnectionState.connectedUnsafe, 设备句柄: 10001, 时间戳: 1747274286127[0m
2025-05-15 09:58:06.128 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.129 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.129 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:06.129 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   D900DeviceManager.verify (package:topping_ble_control/device/d900/d900_device_manager.dart:391:9)[0m
2025-05-15 09:58:06.129 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:06.129 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 D900: 开始验证设备[0m
2025-05-15 09:58:06.129 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.130 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.130 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:06.130 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)[0m
2025-05-15 09:58:06.130 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:06.130 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 执行命令: 开始验证设备[0m
2025-05-15 09:58:06.131 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.132 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.132 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:06.132 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)[0m
2025-05-15 09:58:06.132 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:06.132 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 开始验证设备: QCC5125[0m
2025-05-15 09:58:06.132 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.132 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.132 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:06.132 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   VerifyInterceptor.startVerifying (package:topping_ble_control/utils/verify_interceptor.dart:67:11)[0m
2025-05-15 09:58:06.132 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:06.133 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 VerifyInterceptor: 开始验证模式，会话ID：1747274286132_1[0m
2025-05-15 09:58:06.133 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.133 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.133 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:06.133 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)[0m
2025-05-15 09:58:06.133 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:06.133 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 [VERIFY] 🔍 ==== 验证开始 ==== 🔍 [会话ID: 1747274286132_1][0m
2025-05-15 09:58:06.133 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.133 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.133 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:06.133 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   VerifyInterceptor.startVerifying (package:topping_ble_control/utils/verify_interceptor.dart:74:11)[0m
2025-05-15 09:58:06.133 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:06.133 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 [VERIFY] ====== 开始验证阶段 1 ======[0m
2025-05-15 09:58:06.133 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.134 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.134 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:06.134 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleFfiInitializer._registerGattFunctions.<anonymous closure> (package:topping_ble_control/bluetooth/ffi/ble_ffi_initializer.dart:168:15)[0m
2025-05-15 09:58:06.134 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:06.134 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 C++调用getService: flutterObject: 1747274285027, uuid: 000090FB-0000-1000-8000-00805F9B34FB[0m
2025-05-15 09:58:06.134 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.135 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.135 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:06.135 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleFfiInitializer._registerGattFunctions.<anonymous closure> (package:topping_ble_control/bluetooth/ffi/ble_ffi_initializer.dart:154:13)[0m
2025-05-15 09:58:06.135 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:06.135 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 C++调用writeCharacteristic, flutterObject: 1747274285027[0m
2025-05-15 09:58:06.135 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.136 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.136 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:06.136 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleNativeNotifier.notifyServicesDiscovered (package:topping_ble_control/bluetooth/ffi/ble_native_notifier.dart:51:9)[0m
2025-05-15 09:58:06.136 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:06.136 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 通知服务发现完成 nativeObject: -5476376629556584928[0m
2025-05-15 09:58:06.136 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.136 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.136 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:06.136 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)[0m
2025-05-15 09:58:06.136 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:06.136 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 蓝牙服务发现完成[0m
2025-05-15 09:58:06.136 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.137 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.137 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:06.137 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleFfiInitializer._registerGattFunctions.<anonymous closure> (package:topping_ble_control/bluetooth/ffi/ble_ffi_initializer.dart:168:15)[0m
2025-05-15 09:58:06.137 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:06.137 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 C++调用getService: flutterObject: 1747274285027, uuid: 000090FB-0000-1000-8000-00805F9B34FB[0m
2025-05-15 09:58:06.137 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.137 21284-21284 Topping Controller      com.example.test1_example            I  [BLE日志] FlutterGatt::setCharacteristicNotification被调用, UUID: 00009CF1-0000-1000-8000-00805F9B34FB, enable: 1
2025-05-15 09:58:06.137 21284-21284 Topping Controller      com.example.test1_example            I  [BLE日志] FlutterGatt回调状态:
2025-05-15 09:58:06.137 21284-21284 Topping Controller      com.example.test1_example            I    - this指针: 0xb400007873075e20
2025-05-15 09:58:06.137 21284-21284 Topping Controller      com.example.test1_example            I    - mCallback指针: 0xb4000078ce953508
2025-05-15 09:58:06.137 21284-21284 Topping Controller      com.example.test1_example            I    - mFlutterObject: 1747274285027
2025-05-15 09:58:06.137 21284-21284 Topping Controller      com.example.test1_example            I    - mCallback有效
2025-05-15 09:58:06.137 21284-21284 Topping Controller      com.example.test1_example            I    - mFunctions.init: 0x77de800118
2025-05-15 09:58:06.137 21284-21284 Topping Controller      com.example.test1_example            I    - mFunctions.write_characteristic: 0x77de800148
2025-05-15 09:58:06.137 21284-21284 Topping Controller      com.example.test1_example            I    - mFunctions.set_characteristic_notification: 0x77de800150
2025-05-15 09:58:06.137 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.137 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:06.137 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleFfiInitializer._registerGattFunctions.<anonymous closure> (package:topping_ble_control/bluetooth/ffi/ble_ffi_initializer.dart:159:13)[0m
2025-05-15 09:58:06.137 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:06.137 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 C++调用setCharacteristicNotification, flutterObject: 1747274285027, enable: 1[0m
2025-05-15 09:58:06.137 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.137 21284-21284 Topping Controller      com.example.test1_example            I  [BLE日志] setCharacteristicNotification结果: 1
2025-05-15 09:58:06.139 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.139 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:06.139 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleCharacteristicHandler.writeCharacteristic (package:topping_ble_control/bluetooth/gatt/characteristic_handler.dart:38:9)[0m
2025-05-15 09:58:06.139 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:06.139 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 写入特征值: 00008efa-0000-1000-8000-00805f9b34fb, 数据: [94, 199, 255, 254, 0, 100, 120, 218, 53, 139, 59, 10, 192, 32, 20, 4, 239, 178, 181, 133, 18, 66, 208, 203, 4, 241, 61, 130, 133, 31, 84, 2, 65, 188, 123, 52, 144, 110, 119, 134, 233, 112, 129, 96, 148, 64, 123, 50, 195, 72, 129, 202, 181, 250, 20, 79, 79, 223, 229, 82, 92, 34, 254, 119, 168, 23, 12, 32, 64, 182, 89, 152, 14, 155, 243, 121, 115, 89, 201, 20, 74, 202, 233, 22, 43, 54, 82, 10, 19, 233, 93, 107, 125, 108, 82, 97, 140, 23, 139, 77, 33, 230, 80, 228, 223, 155][0m
2025-05-15 09:58:06.139 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.139 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.139 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:06.139 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleCharacteristicHandler.writeCharacteristic (package:topping_ble_control/bluetooth/gatt/characteristic_handler.dart:42:9)[0m
2025-05-15 09:58:06.139 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:06.139 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 写入特征值: 00008efa-0000-1000-8000-00805f9b34fb, 数据: 5e c7 ff fe 00 64 78 da 35 8b 3b 0a c0 20 14 04 ef b2 b5 85 12 42 d0 cb 04 f1 3d 82 85 1f 54 02 41 bc 7b 34 90 6e 77 86 e9 70 81 60 94 40 7b 32 c3 48 81 ca b5 fa 14 4f 4f df e5 52 5c 22 fe 77 a8 17 0c 20 40 b6 59 98 0e 9b f3 79 73 59 c9 14 4a ca e9 16 2b 36 52 0a 13 e9 5d 6b 7d 6c 52 61 8c 17 8b 4d 21 e6 50 e4 df 9b,[0m
2025-05-15 09:58:06.140 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 时间戳: 1747274286139[0m
2025-05-15 09:58:06.140 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.140 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.140 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:06.140 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)[0m
2025-05-15 09:58:06.140 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:06.140 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 发送到[特征值]: 00008efa-0000-1000-8000-00805f9b34fb - 5e c7 ff fe 00 64 78 da 35 8b 3b 0a c0 20 14 04 ef b2 b5 85 12 42 d0 cb 04 f1 3d 82 85 1f 54 02 41 bc 7b 34 90 6e 77 86 e9 70 81 60 94 40 7b 32 c3 48 81 ca b5 fa 14 4f 4f df e5 52 5c 22 fe 77 a8 17 0c 20 40 b6 59 98 0e 9b f3 79 73 59 c9 14 4a ca e9 16 2b 36 52 0a 13 e9 5d 6b 7d 6c 52 61 8c 17 8b 4d 21 e6 50 e4 df 9b[0m
2025-05-15 09:58:06.140 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.140 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.140 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:06.140 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)[0m
2025-05-15 09:58:06.140 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:06.140 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 [VERIFY] 🔄 验证数据(发送) [会话ID: 1747274286132_1] [阶段1][0m
2025-05-15 09:58:06.140 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.142 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.142 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:06.142 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleCharacteristicHandler.setCharacteristicNotification (package:topping_ble_control/bluetooth/gatt/characteristic_handler.dart:321:11)[0m
2025-05-15 09:58:06.142 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:06.142 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 设置特征值通知: 00009CF1-0000-1000-8000-00805F9B34FB, 启用: true, 时间戳: 1747274286142[0m
2025-05-15 09:58:06.142 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.143 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.143 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:06.143 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleNotificationHelper.enableNotifications (package:topping_ble_control/bluetooth/ble_notification_helper.dart:28:11)[0m
2025-05-15 09:58:06.143 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:06.143 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 已启用 DX9 的通知特征[0m
2025-05-15 09:58:06.143 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.143 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.143 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:06.143 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleConnector._enableNotifications.<anonymous closure> (package:topping_ble_control/bluetooth/connection/ble_connector.dart:248:11)[0m
2025-05-15 09:58:06.143 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:06.143 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 设备通知启用成功[0m
2025-05-15 09:58:06.143 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.143 21284-21284 flutter                 com.example.test1_example            I  ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-05-15 09:58:06.143 21284-21284 flutter                 com.example.test1_example            I  │ #0   Log.d (package:topping_ble_control/utils/log_util.dart:20:13)
2025-05-15 09:58:06.143 21284-21284 flutter                 com.example.test1_example            I  │ #1   BleCharacteristicHandler.setCharacteristicNotification (package:topping_ble_control/bluetooth/gatt/characteristic_handler.dart:331:13)
2025-05-15 09:58:06.143 21284-21284 flutter                 com.example.test1_example            I  ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-05-15 09:58:06.143 21284-21284 flutter                 com.example.test1_example            I  │ 🐛 已取消之前的订阅: 00009CF1-0000-1000-8000-00805F9B34FB
2025-05-15 09:58:06.143 21284-21284 flutter                 com.example.test1_example            I  └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-05-15 09:58:06.144 21284-21284 [FBP-Android]           com.example.test1_example            D  [FBP] onMethodCall: writeCharacteristic
2025-05-15 09:58:06.151 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.151 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:06.151 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager.deleteVerifyLogFile (package:topping_ble_control/utils/app_log_manager.dart:153:13)[0m
2025-05-15 09:58:06.151 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:06.151 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 已删除旧的验证日志文件[0m
2025-05-15 09:58:06.151 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.159 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.159 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:06.159 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   VerifyInterceptor.interceptSendData (package:topping_ble_control/utils/verify_interceptor.dart:152:11)[0m
2025-05-15 09:58:06.159 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:06.159 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 [VERIFY] [阶段1] 数据内容 [会话ID: 1747274286132_1]: 5e c7 ff fe 00 64 78 da 35 8b 3b 0a c0 20 14 04 ef b2 b5 85 12 42 d0 cb 04 f1 3d 82 85 1f 54 02 41 bc 7b 34 90 6e 77 86 e9 70 81 60 94 40 7b 32 c3 48 81 ca b5 fa 14 4f 4f df e5 52 5c 22 fe 77 a8 17 0c 20 40 b6 59 98 0e 9b f3 79 73 59 c9 14 4a ca e9 16 2b 36 52 0a 13 e9 5d 6b 7d 6c 52 61 8c 17 8b 4d 21 e6 50 e4 df 9b[0m
2025-05-15 09:58:06.159 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.161 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.161 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:06.161 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   VerifyInterceptor.interceptSendData (package:topping_ble_control/utils/verify_interceptor.dart:161:11)[0m
2025-05-15 09:58:06.161 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:06.161 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 [VERIFY] [阶段1] 数据信息 [会话ID: 1747274286132_1]: 数据长度: 106 字节, 时间: 2025-05-15 09:58:06.161255[0m
2025-05-15 09:58:06.161 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.234 21284-21323 [FBP-Android]           com.example.test1_example            D  [FBP] onCharacteristicWrite:
2025-05-15 09:58:06.234 21284-21323 [FBP-Android]           com.example.test1_example            D  [FBP]   chr: 8efa
2025-05-15 09:58:06.234 21284-21323 [FBP-Android]           com.example.test1_example            D  [FBP]   status: GATT_SUCCESS (0)
2025-05-15 09:58:06.238 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.239 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:06.239 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleCharacteristicHandler._writeToCharacteristic.<anonymous closure> (package:topping_ble_control/bluetooth/gatt/characteristic_handler.dart:169:15)[0m
2025-05-15 09:58:06.239 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:06.239 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 写入特征值成功，特征值UUID: 00008efa-0000-1000-8000-00805f9b34fb, 时间戳: 1747274286238[0m
2025-05-15 09:58:06.239 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.240 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.241 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:06.241 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleCharacteristicHandler._writeToCharacteristic (package:topping_ble_control/bluetooth/gatt/characteristic_handler.dart:268:11)[0m
2025-05-15 09:58:06.241 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:06.241 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 写入操作成功, 特征值UUID: 00008efa-0000-1000-8000-00805f9b34fb, 时间戳: 1747274286240[0m
2025-05-15 09:58:06.241 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.242 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.242 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:06.242 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)[0m
2025-05-15 09:58:06.242 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:06.243 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 [VERIFY] ✅ 验证结果: 成功 [会话ID: 1747274286132_1] [阶段1][0m
2025-05-15 09:58:06.243 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 - 原因: 写入结果[0m
2025-05-15 09:58:06.243 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.244 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.244 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:06.244 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleOperationManager.writeCharacteristic (package:topping_ble_control/bluetooth/ble_operation_manager.dart:179:11)[0m
2025-05-15 09:58:06.244 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:06.244 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 写特征值成功[0m
2025-05-15 09:58:06.244 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.245 21284-21284 [FBP-Android]           com.example.test1_example            D  [FBP] onMethodCall: discoverServices
2025-05-15 09:58:06.246 21284-21284 BluetoothGatt           com.example.test1_example            D  discoverServices() - device: XX:XX:XX:XX:00:01
2025-05-15 09:58:06.252 21284-21323 BluetoothGatt           com.example.test1_example            D  onSearchComplete() = Device=53:4A:52:FE:00:01 Status=0
2025-05-15 09:58:06.252 21284-21323 [FBP-Android]           com.example.test1_example            D  [FBP] onServicesDiscovered:
2025-05-15 09:58:06.252 21284-21323 [FBP-Android]           com.example.test1_example            D  [FBP]   count: 5
2025-05-15 09:58:06.252 21284-21323 [FBP-Android]           com.example.test1_example            D  [FBP]   status: 0GATT_SUCCESS
2025-05-15 09:58:06.261 21284-21284 [FBP-Android]           com.example.test1_example            D  [FBP] onMethodCall: setNotifyValue
2025-05-15 09:58:06.262 21284-21284 BluetoothGatt           com.example.test1_example            D  setCharacteristicNotification() - uuid: 00009cf1-0000-1000-8000-00805f9b34fb enable: true
2025-05-15 09:58:06.269 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.269 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:06.270 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   VerifyInterceptor.interceptWriteResult (package:topping_ble_control/utils/verify_interceptor.dart:188:11)[0m
2025-05-15 09:58:06.270 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:06.270 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 [VERIFY] ✅ ==== 阶段 1 写入成功 ==== [会话ID: 1747274286132_1][0m
2025-05-15 09:58:06.270 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.351 21284-21323 [FBP-Android]           com.example.test1_example            D  [FBP] onDescriptorWrite:
2025-05-15 09:58:06.352 21284-21323 [FBP-Android]           com.example.test1_example            D  [FBP]   chr: 9cf1
2025-05-15 09:58:06.352 21284-21323 [FBP-Android]           com.example.test1_example            D  [FBP]   desc: 2902
2025-05-15 09:58:06.352 21284-21323 [FBP-Android]           com.example.test1_example            D  [FBP]   status: GATT_SUCCESS (0)
2025-05-15 09:58:06.356 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.356 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:06.356 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BleCharacteristicHandler.setCharacteristicNotification (package:topping_ble_control/bluetooth/gatt/characteristic_handler.dart:419:19)[0m
2025-05-15 09:58:06.356 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:06.357 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 成功启用特征值通知: 00009CF1-0000-1000-8000-00805F9B34FB, 时间戳: 1747274286355[0m
2025-05-15 09:58:06.357 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.358 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.358 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:06.358 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)[0m
2025-05-15 09:58:06.358 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:06.358 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 [VERIFY] ✅ 验证结果: 成功 [会话ID: 1747274286132_1] [阶段1][0m
2025-05-15 09:58:06.359 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 - 原因: 写入结果：成功设置特征值通知[0m
2025-05-15 09:58:06.359 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.375 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:06.375 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)[0m
2025-05-15 09:58:06.375 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ #1   VerifyInterceptor.interceptWriteResult (package:topping_ble_control/utils/verify_interceptor.dart:188:11)[0m
2025-05-15 09:58:06.375 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
2025-05-15 09:58:06.375 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m│ 💡 [VERIFY] ✅ ==== 阶段 1 写入成功 ==== [会话ID: 1747274286132_1][0m
2025-05-15 09:58:06.375 21284-21284 flutter                 com.example.test1_example            I  [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2025-05-15 09:58:07.024 21284-21323 BluetoothGatt           com.example.test1_example            D  onConnectionUpdated() - Device=53:4A:52:FE:00:01 interval=72 latency=4 timeout=400 status=0
2025-05-15 09:58:10.167 21284-21323 BluetoothGatt           com.example.test1_example            D  onPhyUpdate() - status=0 address=53:4A:52:FE:00:01 txPhy=1 rxPhy=2
