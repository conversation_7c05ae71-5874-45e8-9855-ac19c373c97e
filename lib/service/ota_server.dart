import 'dart:async';
import 'dart:collection';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';

import '../gaia/confirmation_type.dart';
import '../gaia/gaia.dart';
import '../gaia/gaia_packet_ble.dart';
import '../gaia/op_codes.dart';
import '../gaia/resume_points.dart';
import '../gaia/upgrade_start_cfm_status.dart';
import '../gaia/vmu_packet.dart'; // 文件名保持不变，类名已在VMUPacket.dart中修改
import '../gaia/rwcp/rwcp_client.dart';
import '../gaia/rwcp/rwcp_listener.dart';
import '../utils/string_utils.dart';

class OtaServer extends GetxService implements RwcpListener {
  var logText = "".obs;
  final String tag = "OtaServer";

  // 设备版本信息
  var deviceVersion = "".obs;

  // 升级前的版本信息
  var previousVersion = "".obs;

  // 升级是否成功
  var upgradeSuccess = false.obs;

  // 蓝牙设备
  BluetoothDevice? _device;

  // 服务和特征UUID
  final String otaServiceUuid = "00001100-d102-11e1-9b23-00025b00a5a5";
  final String notifyCharUuid = "00001102-d102-11e1-9b23-00025b00a5a5";
  final String writeCharUuid = "00001101-d102-11e1-9b23-00025b00a5a5";
  final String writeNoResCharUuid = "00001103-d102-11e1-9b23-00025b00a5a5";

  // 服务和特征
  BluetoothService? _otaService;
  BluetoothCharacteristic? _notifyChar;
  BluetoothCharacteristic? _writeChar;
  BluetoothCharacteristic? _writeNoResChar;

  /// To know if the upgrade process is currently running.
  RxBool isUpgrading = false.obs;

  bool transFerComplete = false;

  /// To know how many times we try to start the upgrade.
  var mStartAttempts = 0;

  /// The offset to use to upload data on the device.
  var mStartOffset = 0;

  /// The file to upload on the device.
  List<int>? mBytesFile;

  List<int> writeBytes = [];

  /// The maximum value for the data length of a VM upgrade packet for the data transfer step.
  var mMaxLengthForDataTransfer = 16;

  var mPayloadSizeMax = 16;

  /// To know if the packet with the operation code "UPGRADE_DATA" which was sent was the last packet to send.
  bool wasLastPacket = false;

  int mBytesToSend = 0;

  int mResumePoint = -1;

  var mIsRWCPEnabled = false.obs;
  int sendPkgCount = 0;

  RxDouble updatePer = RxDouble(0);

  /// To know if we have to disconnect after any event which occurs as a fatal error from the board.
  bool hasToAbort = false;

  final writeQueue = Queue<List<int>>();

  StreamSubscription<List<int>>? _notifySubscription;

  StreamSubscription<List<int>>? _rwcpSubscription;

  String fileMd5 = "";

  var percentage = 0.0.obs;

  Timer? _timer;

  var timeCount = 0.obs;

  //RWCP
  ListQueue<double> mProgressQueue = ListQueue();

  late RwcpClient mRWCPClient;

  int mTransferStartTime = 0;

  int writeRTCPCount = 0;

  File? file;

  static OtaServer get to => Get.find();

  @override
  void onInit() {
    super.onInit();
    mRWCPClient = RwcpClient(this);

    // 监听蓝牙状态变化
    FlutterBluePlus.adapterState.listen((state) {
      switch (state) {
        case BluetoothAdapterState.on:
          addLog("蓝牙打开");
          break;
        case BluetoothAdapterState.off:
          addLog("蓝牙关闭");
          break;
        default:
          break;
      }
    });
  }

  /// 设置要使用的蓝牙设备
  Future<bool> setDevice(BluetoothDevice device) async {
    try {
      _device = device;
      addLog('设置设备: ${device.platformName}');

      // 检查设备连接状态
      BluetoothConnectionState currentState = await device.connectionState.first;
      if (currentState != BluetoothConnectionState.connected) {
        addLog('设备未连接，尝试连接...');
        try {
          await device.connect();
          addLog('设备连接成功');
        } catch (e) {
          addLog('设备连接失败: $e');
          return false;
        }
      }

      // 发现服务
      List<BluetoothService> services = await device.discoverServices();

      // 查找 OTA 服务
      _otaService = services.firstWhere(
        (service) =>
            service.uuid.toString().toLowerCase() ==
            otaServiceUuid.toLowerCase(),
        orElse: () => throw Exception('未找到 OTA 服务'),
      );

      // 查找并设置特征
      _findAndSetCharacteristics();

      // 注册通知
      await registerNotice();

      return true;
    } catch (e) {
      addLog('设置设备失败: $e');
      return false;
    }
  }

  /// 查找并设置特征
  void _findAndSetCharacteristics() {
    if (_otaService == null) {
      addLog('未找到 OTA 服务，无法设置特征');
      return;
    }

    try {
      // 查找通知特征
      _notifyChar = _otaService!.characteristics.firstWhere(
        (c) => c.uuid.toString().toLowerCase() == notifyCharUuid.toLowerCase(),
        orElse: () => throw Exception('未找到通知特征'),
      );

      // 查找写入特征
      _writeChar = _otaService!.characteristics.firstWhere(
        (c) => c.uuid.toString().toLowerCase() == writeCharUuid.toLowerCase(),
        orElse: () => throw Exception('未找到写入特征'),
      );

      // 查找无响应写入特征
      _writeNoResChar = _otaService!.characteristics.firstWhere(
        (c) =>
            c.uuid.toString().toLowerCase() == writeNoResCharUuid.toLowerCase(),
        orElse: () => throw Exception('未找到无响应写入特征'),
      );

      addLog('特征设置成功');
    } catch (e) {
      addLog('设置特征失败: $e');
    }
  }

  void writeMsg(List<int> data) {
    scheduleMicrotask(() async {
      await writeData(data);
    });
  }

  Future<void> registerRWCP() async {
    if (_device == null || _writeNoResChar == null) {
      addLog('设备或特征未设置，无法注册 RWCP');
      return;
    }

    // 取消之前的订阅
    await _rwcpSubscription?.cancel();

    // 订阅无响应写入特征的通知
    _rwcpSubscription = _writeNoResChar!.onValueReceived.listen(
      (data) {
        mRWCPClient.onReceiveRwcpSegment(data);
      },
      onError: (error) {
        addLog('接收 RWCP 数据错误: $error');
      },
    );

    // 启用通知
    await _writeNoResChar!.setNotifyValue(true);

    addLog("RWCP 注册成功");

    // 如果传输已完成，重新连接
    if (transFerComplete) {
      await Future.delayed(const Duration(seconds: 1));
      transFerComplete = false;
      addLog("传输已完成，重新连接");
      sendUpgradeConnect();
    }
    // 移除自动启动升级的逻辑，让用户手动点击按钮开始升级
  }

  /// 注册通知
  Future<void> registerNotice() async {
    if (_device == null || _notifyChar == null) {
      addLog('设备或通知特征未设置，无法注册通知');
      return;
    }

    // 取消之前的订阅
    await _notifySubscription?.cancel();

    // 订阅通知特征
    _notifySubscription = _notifyChar!.onValueReceived.listen(
      (data) {
        addLog("收到通知>${StringUtils.byteToHexString(data)}");
        handleRecMsg(data);
      },
      onError: (error) {
        addLog('接收通知错误: $error');
      },
    );

    // 启用通知
    await _notifyChar!.setNotifyValue(true);

    await Future.delayed(const Duration(seconds: 1));

    // 发送注册通知命令
    GaiaPacketBLE packet = GaiaPacketBLE.buildGaiaNotificationPacket(
      Gaia.commandRegisterNotification,
      Gaia.vmuPacket,
      null,
      Gaia.ble,
    );
    writeMsg(packet.getBytes());

    // 如果开启RWCP那么需要在重连之后启用RWCP
    if (isUpgrading.value && transFerComplete && mIsRWCPEnabled.value) {
      // 开启RWCP
      await Future.delayed(const Duration(seconds: 1));
      writeMsg(StringUtils.hexStringToBytes("000A022E01"));
    }
  }

  void startUpdate() async {
    // 保存当前版本号作为升级前的版本
    previousVersion.value = deviceVersion.value;
    addLog('升级前版本: ${previousVersion.value}');

    // 重置升级成功状态
    upgradeSuccess.value = false;

    // 清空日志和重置状态
    logText.value = "";
    writeBytes.clear();
    writeRTCPCount = 0;
    mProgressQueue.clear();
    mTransferStartTime = 0;
    timeCount.value = 0;
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      timeCount.value += 1;
    });
    sendPkgCount = 0;
    updatePer.value = 0;
    writeQueue.clear();
    resetUpload();

    // 直接开始升级过程
    addLog('开始升级连接');
    isUpgrading.value = true; // 设置为 true，表示正在升级
    sendUpgradeConnect();
  }

  void handleRecMsg(List<int> data) async {
    GaiaPacketBLE packet = GaiaPacketBLE.fromByte(data) ?? GaiaPacketBLE(0);
    if (packet.isAcknowledgement()) {
      int status = packet.getStatus();
      if (status == Gaia.success) {
        receiveSuccessfulAcknowledgement(packet);

        // 处理版本信息响应
        if (packet.getCommand() == Gaia.commandGetApplicationVersion + 0x8000) {
          final payload = packet.mPayload ?? [];
          if (payload.isNotEmpty) {
            // 直接显示原始数据，便于调试
            String rawData = StringUtils.byteToHexString(payload);
            addLog("原始版本数据: $rawData");

            // 解析版本信息
            try {
              // 尝试从返回的数据中提取有用信息
              List<String> parts = [];

              // 如果有足够的数据
              if (payload.length >= 2) {
                // 第一个字节可能是主版本号
                int majorVersion = payload[0];
                parts.add("主版本: $majorVersion");
              }

              if (payload.length >= 3) {
                // 第二个字节可能是次版本号
                int minorVersion = payload[1];
                parts.add("次版本: $minorVersion");
              }

              // 如果有更多数据，可能是其他版本信息
              if (payload.length > 3) {
                // 尝试将剩余字节解析为文本
                String additionalInfo = StringUtils.byteToHexString(
                  payload.sublist(3),
                );
                parts.add("其他信息: $additionalInfo");
              }

              // 合并所有信息
              String versionInfo = parts.join("\n");

              // 如果没有解析出任何信息，显示原始数据
              if (versionInfo.isEmpty) {
                versionInfo = "原始数据: $rawData";
              }

              deviceVersion.value = versionInfo;
              addLog("获取到设备版本信息: $versionInfo");
            } catch (e) {
              addLog("解析版本信息出错: $e");
              deviceVersion.value = "原始数据: $rawData";
            }
          }
        }
      } else {
        receiveUnsuccessfulAcknowledgement(packet);
      }
    } else if (packet.getCommand() == Gaia.commandEventNotification) {
      final payload = packet.mPayload ?? [];
      //000AC0010012
      if (payload.isNotEmpty) {
        int event = packet.getEvent();
        if (event == Gaia.vmuPacket) {
          createAcknowledgmentRequest();
          await Future.delayed(const Duration(milliseconds: 1000));
          receiveVMUPacket(payload.sublist(1));
          return;
        } else {
          // not supported
          return;
        }
      } else {
        createAcknowledgmentRequest();
        await Future.delayed(const Duration(milliseconds: 1000));
        return;
      }
    }
  }

  void receiveSuccessfulAcknowledgement(GaiaPacketBLE packet) {
    addLog(
      "receiveSuccessfulAcknowledgement ${StringUtils.intTo2HexString(packet.getCommand())}",
    );

    // 处理版本信息响应
    if (packet.getCommand() == Gaia.commandGetApplicationVersion + 0x8000) {
      final payload = packet.mPayload ?? [];
      if (payload.isNotEmpty) {
        // 直接显示原始数据，便于调试
        String rawData = StringUtils.byteToHexString(payload);
        addLog("原始版本数据: $rawData");

        // 直接使用原始数据作为版本信息
        deviceVersion.value =
            "设备ID: ${rawData.substring(0, 16)}\n"
            "版本信息: ${rawData.substring(16)}";
      }
    }
    // 处理设备特性信息响应 (COMMAND_GET_HOST_FEATURE_INFORMATION)
    else if (packet.getCommand() ==
        Gaia.commandGetHostFeatureInformation + 0x8000) {
      final payload = packet.mPayload ?? [];
      if (payload.isNotEmpty) {
        // 直接显示原始数据，便于调试
        String rawData = StringUtils.byteToHexString(payload);
        addLog("原始设备特性数据: $rawData");

        // 解析设备特性信息
        try {
          // 第一个字节是状态码，应该是0x00表示成功
          if (payload[0] == 0x00 && payload.length > 1) {
            // 从第二个字节开始是特性数据
            List<String> features = [];

            // 如果有足够的数据，解析特性信息
            if (payload.length >= 2) {
              String featureHex = StringUtils.byteToHexString(
                payload.sublist(1),
              );
              features.add("特性数据: $featureHex");

              // 如果有更多特定的解析需求，可以在这里添加
            }

            // 更新设备版本信息
            deviceVersion.value = features.join("\n");
            addLog("获取到设备特性信息: ${features.join(', ')}");
          } else {
            deviceVersion.value = "原始数据: $rawData";
          }
        } catch (e) {
          addLog("解析设备特性信息出错: $e");
          deviceVersion.value = "原始数据: $rawData";
        }
      }
    }
    switch (packet.getCommand()) {
      case Gaia.commandVmUpgradeConnect:
        {
          addLog('收到升级连接响应');

          // 设置数据传输参数
          int size = mPayloadSizeMax;
          if (mIsRWCPEnabled.value) {
            size = mPayloadSizeMax - 1;
            size = (size % 2 == 0) ? size : size - 1;
          }
          mMaxLengthForDataTransfer =
              (size - VmuPacket.REQUIRED_INFORMATION_LENGTH).toInt();
          addLog(
            "mMaxLengthForDataTransfer $mMaxLengthForDataTransfer mPayloadSizeMax $mPayloadSizeMax",
          );

          // 开始升级过程
          resetUpload();
          sendSyncReq();
        }
        break;
      case Gaia.commandVmUpgradeDisconnect:
        stopUpgrade();
        break;
      case Gaia.commandVmUpgradeControl:
        onSuccessfulTransmission();
        break;
      case Gaia.commandSetDataEndpointMode:
        if (mIsRWCPEnabled.value) {
          registerRWCP();
        } else {
          _rwcpSubscription?.cancel();
        }

        break;
    }
  }

  void receiveUnsuccessfulAcknowledgement(GaiaPacketBLE packet) {
    addLog("命令发送失败${StringUtils.intTo2HexString(packet.getCommand())}");
    if (packet.getCommand() == Gaia.commandVmUpgradeConnect ||
        packet.getCommand() == Gaia.commandVmUpgradeControl) {
      sendUpgradeDisconnect();
    } else if (packet.getCommand() == Gaia.commandVmUpgradeDisconnect) {
    } else if (packet.getCommand() == Gaia.commandSetDataEndpointMode ||
        packet.getCommand() == Gaia.commandGetDataEndpointMode) {
      mIsRWCPEnabled.value = false;
      onRWCPNotSupported();
    } else if (packet.getCommand() == Gaia.commandGetHostFeatureInformation) {
      // 如果设备特性信息命令失败，尝试获取应用版本
      addLog("设备不支持特性信息命令，尝试获取应用版本");
      GaiaPacketBLE versionPacket = GaiaPacketBLE(
        Gaia.commandGetApplicationVersion,
      );
      writeMsg(versionPacket.getBytes());
    }
  }

  void startUpgradeProcess() {
    // 直接开始升级过程
    resetUpload();
    sendSyncReq();
  }

  /// <p>To reset the file transfer.</p>
  void resetUpload() {
    transFerComplete = false;
    mStartAttempts = 0;
    mBytesToSend = 0;
    mStartOffset = 0;
  }

  void stopUpgrade() async {
    _timer?.cancel();
    timeCount.value = 0;
    abortUpgrade();
    resetUpload();
    writeRTCPCount = 0;
    updatePer.value = 0;
    isUpgrading.value = false;
    await Future.delayed(const Duration(milliseconds: 500));
    sendUpgradeDisconnect();
  }

  void sendSyncReq() async {
    //A2305C3A9059C15171BD33F3BB08ADE4 MD5
    //000A0642130004BB08ADE4
    final filePath = await getApplicationDocumentsDirectory();
    final saveBinPath = "${filePath.path}/1.bin";
    File file = File(saveBinPath);
    mBytesFile = await file.readAsBytes();
    int fileSize = mBytesFile?.length ?? 0;
    fileMd5 = StringUtils.file2md5(mBytesFile ?? []).toUpperCase();
    addLog("读取到文件MD5$fileMd5");
    addLog("文件大小: $fileSize 字节");
    final endMd5 = StringUtils.hexStringToBytes(fileMd5.substring(24));
    VmuPacket packet = VmuPacket.get(OpCodes.upgradeSyncReq, data: endMd5);
    sendVmuPacket(packet, false);
  }

  /// <p>To send a VMUPacket over the defined protocol communication.</p>
  ///
  /// @param bytes
  ///              The packet to send.
  /// @param isTransferringData
  ///              True if the packet is about transferring the file data, false for any other packet.
  void sendVmuPacket(VmuPacket packet, bool isTransferringData) {
    // Renaming method definition
    List<int> bytes = packet.getBytes();
    if (isTransferringData && mIsRWCPEnabled.value) {
      final packet = GaiaPacketBLE(
        Gaia.commandVmUpgradeControl,
        mPayload: bytes,
      );
      try {
        List<int> bytes = packet.getBytes();
        if (mTransferStartTime <= 0) {
          mTransferStartTime = DateTime.now().millisecond;
        }
        bool success = mRWCPClient.sendData(bytes);
        if (!success) {
          addLog(
            "Fail to send GAIA packet for GAIA command: ${packet.getCommandId()}",
          );
        }
      } catch (e) {
        addLog("Exception when attempting to create GAIA packet: $e");
      }
    } else {
      final pkg = GaiaPacketBLE(Gaia.commandVmUpgradeControl, mPayload: bytes);
      writeMsg(pkg.getBytes());
    }
  }

  void receiveVMUPacket(List<int> data) {
    try {
      final packet = VmuPacket.getPackageFromByte(data);
      // 始终处理 VMU 包，不再检查 isUpgrading 状态
      if (packet != null) {
        handleVmuPacket(packet);
      } else {
        addLog("无法解析 VMU 包");
      }
    } catch (e) {
      addLog("receiveVMUPacket $e");
    }
  }

  ///创建回包
  void createAcknowledgmentRequest() {
    writeMsg(StringUtils.hexStringToBytes("000AC00300"));
  }

  void handleVmuPacket(VmuPacket? packet) {
    // Renaming method definition
    switch (packet?.mOpCode) {
      case OpCodes.upgradeSyncCfm:
        receiveSyncCFM(packet);
        break;
      case OpCodes.upgradeStartCfm:
        receiveStartCFM(packet);
        break;
      case OpCodes.upgradeDataBytesReq:
        receiveDataBytesREQ(packet);
        break;
      case OpCodes.upgradeAbortCfm:
        receiveAbortCFM();
        break;
      case OpCodes.upgradeErrorWarnInd:
        receiveErrorWarnIND(packet);
        break;
      case OpCodes.upgradeIsValidationDoneCfm:
        receiveValidationDoneCFM(packet);
        break;
      case OpCodes.upgradeTransferCompleteInd:
        receiveTransferCompleteIND();
        break;
      case OpCodes.upgradeCommitReq:
        receiveCommitREQ();
        break;
      case OpCodes.upgradeCompleteInd:
        receiveCompleteIND();
        break;
    }
  }

  void sendUpgradeConnect() async {
    GaiaPacketBLE packet = GaiaPacketBLE(Gaia.commandVmUpgradeConnect);
    writeMsg(packet.getBytes());
  }

  void cancelNotification() async {
    GaiaPacketBLE packet = GaiaPacketBLE.buildGaiaNotificationPacket(
      Gaia.commandCancelNotification,
      Gaia.vmuPacket,
      null,
      Gaia.ble,
    );
    writeMsg(packet.getBytes());
  }

  void sendUpgradeDisconnect() {
    GaiaPacketBLE packet = GaiaPacketBLE(Gaia.commandVmUpgradeDisconnect);
    writeMsg(packet.getBytes());
  }

  void receiveSyncCFM(VmuPacket? packet) {
    List<int> data = packet?.mData ?? [];
    if (data.length >= 6) {
      int step = data[0];
      addLog("上次传输步骤 step $step");
      if (step == ResumePoints.inProgress) {
        setResumePoint(step);
      } else {
        mResumePoint = step;
      }
    } else {
      mResumePoint = ResumePoints.dataTransfer;
    }
    sendStartReq();
  }

  /// To send an UPGRADE_START_REQ message.
  void sendStartReq() {
    VmuPacket packet = VmuPacket.get(OpCodes.upgradeStartReq);
    sendVmuPacket(packet, false);
  }

  void receiveStartCFM(VmuPacket? packet) {
    List<int> data = packet?.mData ?? [];
    if (data.length >= 3) {
      if (data[0] == UpgradeStartCFMStatus.success) {
        mStartAttempts = 0;
        // the device is ready for the upgrade, we can go to the resume point or to the upgrade beginning.
        switch (mResumePoint) {
          case ResumePoints.commit:
            askForConfirmation(ConfirmationType.commit);
            break;
          case ResumePoints.transferComplete:
            askForConfirmation(ConfirmationType.transferComplete);
            break;
          case ResumePoints.inProgress:
            askForConfirmation(ConfirmationType.inProgress);
            break;
          case ResumePoints.validation:
            sendValidationDoneReq();
            break;
          case ResumePoints.dataTransfer:
          default:
            sendStartDataReq();
            break;
        }
      }
    }
  }

  void receiveAbortCFM() {
    addLog("receiveAbortCFM");
    stopUpgrade();
  }

  void receiveErrorWarnIND(VmuPacket? packet) async {
    List<int> data = packet?.mData ?? [];
    sendErrorConfirmation(data); //
    int returnCode = StringUtils.extractIntFromByteArray(data, 0, 2, false);
    //A2305C3A9059C15171BD33F3BB08ADE4
    addLog(
      "receiveErrorWarnIND 升级失败 错误码0x${returnCode.toRadixString(16)} fileMd5$fileMd5",
    );
    //noinspection IfCanBeSwitch
    if (returnCode == 0x81) {
      addLog("包不通过");
      askForConfirmation(ConfirmationType.warningFileIsDifferent);
    } else if (returnCode == 0x21) {
      addLog("电量过低");
      askForConfirmation(ConfirmationType.batteryLowOnDevice);
    } else {
      stopUpgrade();
    }
  }

  void receiveValidationDoneCFM(VmuPacket? packet) {
    addLog("receiveValidationDoneCFM");
    List<int> data = packet?.getBytes() ?? [];
    if (data.length == 2) {
      final time = StringUtils.extractIntFromByteArray(data, 0, 2, false);
      Future.delayed(
        Duration(milliseconds: time),
      ).then((value) => sendValidationDoneReq());
    } else {
      sendValidationDoneReq();
    }
  }

  void receiveTransferCompleteIND() {
    addLog("receiveTransferCompleteIND");
    transFerComplete = true;
    setResumePoint(ResumePoints.transferComplete);

    // 记录传输完成
    addLog("固件传输完成，等待设备确认");

    askForConfirmation(ConfirmationType.transferComplete);
  }

  void receiveCommitREQ() {
    addLog("receiveCommitREQ");
    setResumePoint(ResumePoints.commit);
    askForConfirmation(ConfirmationType.commit);
  }

  void receiveCompleteIND() {
    isUpgrading.value = false;
    upgradeSuccess.value = true;
    addLog("receiveCompleteIND 升级完成");

    // 记录升级前后的版本信息
    addLog("升级前版本: ${previousVersion.value}");
    addLog("升级完成，设备将重启并应用新固件");

    // 在断开连接前尝试获取新版本
    Future.delayed(const Duration(seconds: 1), () {
      // 断开升级连接
      disconnectUpgrade();
    });
  }

  void sendValidationDoneReq() {
    VmuPacket packet = VmuPacket.get(OpCodes.upgradeIsValidationDoneReq);
    sendVmuPacket(packet, false);
  }

  void sendStartDataReq() {
    setResumePoint(ResumePoints.dataTransfer);
    VmuPacket packet = VmuPacket.get(OpCodes.upgradeStartDataReq);
    sendVmuPacket(packet, false);
  }

  void setResumePoint(int point) {
    mResumePoint = point;
  }

  void receiveDataBytesREQ(VmuPacket? packet) {
    List<int> data = packet?.mData ?? [];

    // Checking the data has the good length
    if (data.length == OpCodes.dataLength) {
      // retrieving information from the received packet
      //REC 120300080000002400000000
      //SEND 000A064204000D0000030000FFFF0001FFFF0002
      var lengthByte = [data[0], data[1], data[2], data[3]];
      var fileByte = [data[4], data[5], data[6], data[7]];
      mBytesToSend = int.parse(
        StringUtils.byteToHexString(lengthByte),
        radix: 16,
      );
      int fileOffset = int.parse(
        StringUtils.byteToHexString(fileByte),
        radix: 16,
      );

      addLog(
        "${StringUtils.byteToHexString(data)}本次发包: $fileOffset $mBytesToSend",
      );
      // we check the value for the offset
      mStartOffset +=
          (fileOffset > 0 &&
                  fileOffset + mStartOffset < (mBytesFile?.length ?? 0))
              ? fileOffset
              : 0;

      // if the asked length doesn't fit with possibilities we use the maximum length we can use.
      mBytesToSend = (mBytesToSend > 0) ? mBytesToSend : 0;
      // if the requested length will look for bytes out of the array we reduce it to the remaining length.
      int remainingLength = mBytesFile?.length ?? 0 - mStartOffset;
      mBytesToSend =
          (mBytesToSend < remainingLength) ? mBytesToSend : remainingLength;
      if (mIsRWCPEnabled.value) {
        while (mBytesToSend > 0) {
          sendNextDataPacket();
        }
      } else {
        addLog("receiveDataBytesREQ: sendNextDataPacket");
        sendNextDataPacket();
      }
    } else {
      addLog("UpgradeError 数据传输失败");
      abortUpgrade();
    }
  }

  void abortUpgrade() {
    if (mRWCPClient.isRunningASession()) {
      mRWCPClient.cancelTransfer();
    }
    mProgressQueue.clear();
    sendAbortReq();
    isUpgrading.value = false;
  }

  void sendAbortReq() {
    VmuPacket packet = VmuPacket.get(OpCodes.upgradeAbortReq);
    sendVmuPacket(packet, false);
  }

  //主要发包逻辑
  void sendNextDataPacket() {
    if (!isUpgrading.value) {
      stopUpgrade();
      return;
    }
    // inform listeners about evolution
    onFileUploadProgress();
    int bytesToSend =
        mBytesToSend < mMaxLengthForDataTransfer - 1
            ? mBytesToSend
            : mMaxLengthForDataTransfer - 1;
    // to know if we are sending the last data packet.
    bool lastPacket = (mBytesFile ?? []).length - mStartOffset <= bytesToSend;
    if (lastPacket) {
      addLog(
        "mMaxLengthForDataTransfer$mMaxLengthForDataTransfer bytesToSend$bytesToSend lastPacket$lastPacket",
      );
    }
    List<int> dataToSend = [];
    for (int i = 0; i < bytesToSend; i++) {
      dataToSend.add((mBytesFile ?? [])[mStartOffset + i]);
    }

    if (lastPacket) {
      wasLastPacket = true;
      mBytesToSend = 0;
    } else {
      mStartOffset += bytesToSend;
      mBytesToSend -= bytesToSend;
    }

    sendData(lastPacket, dataToSend);
  }

  //计算进度
  void onFileUploadProgress() {
    double percentage = (mStartOffset * 100.0 / (mBytesFile ?? []).length);
    percentage =
        (percentage < 0)
            ? 0
            : (percentage > 100)
            ? 100
            : percentage;
    if (mIsRWCPEnabled.value) {
      mProgressQueue.add(percentage);
    } else {
      updatePer.value = percentage;
      // 添加日志，显示当前进度
      addLog(
        "当前升级进度: ${percentage.toStringAsFixed(2)}% ($mStartOffset/${(mBytesFile ?? []).length})",
      );
    }
  }

  void sendData(bool lastPacket, List<int> data) {
    List<int> dataToSend = [];
    dataToSend.add(lastPacket ? 0x01 : 0x00);
    dataToSend.addAll(data);
    sendPkgCount++;
    VmuPacket packet = VmuPacket.get(OpCodes.upgradeData, data: dataToSend);
    sendVmuPacket(packet, true);
  }

  void onSuccessfulTransmission() {
    if (wasLastPacket) {
      if (mResumePoint == ResumePoints.dataTransfer) {
        wasLastPacket = false;
        setResumePoint(ResumePoints.validation);
        sendValidationDoneReq();
      }
    } else if (hasToAbort) {
      hasToAbort = false;
      abortUpgrade();
    } else {
      if (mBytesToSend > 0 &&
          mResumePoint == ResumePoints.dataTransfer &&
          !mIsRWCPEnabled.value) {
        sendNextDataPacket();
      }
    }
  }

  void onRWCPNotSupported() {
    addLog("RWCP onRWCPNotSupported");
  }

  void askForConfirmation(int type) {
    int code = -1;
    switch (type) {
      case ConfirmationType.commit:
        {
          code = OpCodes.upgradeCommitCfm;
        }
        break;
      case ConfirmationType.inProgress:
        {
          code = OpCodes.upgradeInProgressRes;
        }
        break;
      case ConfirmationType.transferComplete:
        {
          code = OpCodes.upgradeTransferCompleteRes;
        }
        break;
      case ConfirmationType.batteryLowOnDevice:
        {
          sendSyncReq();
        }
        return;
      case ConfirmationType.warningFileIsDifferent:
        {
          stopUpgrade();
        }
        return;
    }
    addLog("askForConfirmation ConfirmationType type $type $code");
    VmuPacket packet = VmuPacket.get(code, data: [0]);
    sendVmuPacket(packet, false); // Already correct, ensuring consistency
  }

  void sendErrorConfirmation(List<int> data) {
    VmuPacket packet = VmuPacket.get(
      OpCodes.upgradeErrorWarnRes,
      data: data,
    );
    sendVmuPacket(packet, false); // Already correct, ensuring consistency
  }

  void disconnectUpgrade() {
    cancelNotification();
    sendUpgradeDisconnect();
  }

  @override
  void onTransferFailed() {
    abortUpgrade();
  }

  @override
  void onTransferFinished() {
    onSuccessfulTransmission();
    mProgressQueue.clear();
  }

  @override
  void onTransferProgress(int acknowledged) {
    if (acknowledged > 0) {
      double percentage = 0;
      while (acknowledged > 0 && mProgressQueue.isNotEmpty) {
        percentage = mProgressQueue.removeFirst();
        acknowledged--;
      }
      if (mIsRWCPEnabled.value) {
        updatePer.value = percentage;
      }
      // addLog("$mIsRWCPEnabled 升级进度$percentage");
    }
  }

  @override
  bool sendRwcpSegment(List<int> bytes) {
    writeMsgRWCP(bytes);
    return true;
  }

  /// 一般命令写入通道
  Future<void> writeData(List<int> data) async {
    if (_device == null || _writeChar == null) {
      addLog('设备或写入特征未设置，无法写入数据');
      return;
    }

    addLog(
      "${DateTime.now()} wenDataWrite start>${StringUtils.byteToHexString(data)}",
    );
    await Future.delayed(const Duration(milliseconds: 100));

    try {
      await _writeChar!.write(data, withoutResponse: false);
      addLog(
        "${DateTime.now()} wenDataWrite end>${StringUtils.byteToHexString(data)}",
      );
    } catch (e) {
      addLog('写入数据失败: $e');
    }
  }

  /// RWCP写入通道
  Future<void> writeMsgRWCP(List<int> data) async {
    if (_device == null || _writeNoResChar == null) {
      addLog('设备或无响应写入特征未设置，无法写入RWCP数据');
      return;
    }

    await Future.delayed(const Duration(milliseconds: 100));

    try {
      await _writeNoResChar!.write(data, withoutResponse: true);
    } catch (e) {
      addLog('写入RWCP数据失败: $e');
    }
  }

  /// 断开连接并清理资源
  void disconnect() {
    // 取消所有订阅
    _notifySubscription?.cancel();
    _rwcpSubscription?.cancel();

    // 重置设备和特征
    _device = null;
    _otaService = null;
    _notifyChar = null;
    _writeChar = null;
    _writeNoResChar = null;

    // 重置状态
    deviceVersion.value = "";
  }

  /// 协商MTU并设置负载大小
  Future<void> restPayloadSize() async {
    if (_device == null) {
      addLog('设备未设置，无法协商MTU');
      return;
    }

    try {
      // 请求MTU
      int mtu = await _device!.requestMtu(256);

      if (!mIsRWCPEnabled.value) {
        mtu = 23; // 如果不使用RWCP，使用默认值
      }

      int dataSize = mtu - 3;
      mPayloadSizeMax = dataSize - 4;
      addLog("协商mtu $mtu mPayloadSizeMax $mPayloadSizeMax");
    } catch (e) {
      addLog('协商MTU失败: $e');
      // 使用默认值
      mPayloadSizeMax = 16;
    }
  }

  void addLog(String s) {
    debugPrint("wenTest $s");
    logText.value += "$s\n";
  }
}
