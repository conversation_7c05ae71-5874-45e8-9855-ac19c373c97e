import 'dart:ffi';
import 'dart:typed_data'; // 用于 Uint8List
import 'dart:convert'; // 用于 utf8.encode

import 'package:ffi/ffi.dart';
import 'package:topping_ble_control/model/base/topping_device_settings.dart';
import 'package:topping_ble_control/model/ffi/ffi_dx5ii_settings.dart';
import 'package:topping_ble_control/utils/log_util.dart';

/// Flutter友好的DX5II设备设置模型类
class Dx5iiSettings extends ToppingDeviceSettings {
  /// 耳机启用状态，DX5II特有
  final bool headphoneEnabled;

  /// 耳机增益，DX5II特有
  final int headphoneGain;

  /// PCM滤波器，DX5II特有
  final int pcmFilter;

  /// 解码模式，DX5II特有
  final int decodeMode;

  // --- 新增字段 (DX5 III 新功能) ---
  /// VU表0dDB幅值
  final int vuMeterLevel;

  /// VU条显示模式
  final int vuMeterDisplayMode;

  /// 输入选项位掩码
  final int inputOptions;

  /// 输出选项位掩码
  final int outputOptions;

  /// 音量步进
  final int volumeStep;

  /// 极性设置
  final int polarity;

  /// 音量记忆方式
  final int volumeMemoryMode;

  /// PEQ记忆方式
  final int peqMemoryMode;

  /// 主按键功能
  final int mainKeyFunction;

  /// 遥控A键功能
  final int remoteAKeyFunction;

  /// 遥控B键功能
  final int remoteBKeyFunction;

  /// 构造函数
  Dx5iiSettings({
    required super.isOn,
    required super.deviceName,
    required super.volume,
    required super.mute,
    required super.inputType,
    required super.outputType,
    required this.headphoneEnabled,
    required this.headphoneGain,
    required super.displayMode,
    required super.theme,
    required super.powerTrigger,
    required super.balance,
    required this.pcmFilter,
    required this.decodeMode,
    required super.audioBluetooth,
    required super.bluetoothAptx,
    required super.remoteEnabled,
    required super.multifunctionKey,
    required super.usbMode,
    required super.screenBrightness,
    required super.language,
    required super.sampling,
    // --- 新增参数 (DX5 III 新功能) ---
    required this.vuMeterLevel,
    required this.vuMeterDisplayMode,
    required this.inputOptions,
    required this.outputOptions,
    required this.volumeStep,
    required this.polarity,
    required this.volumeMemoryMode,
    required this.peqMemoryMode,
    required this.mainKeyFunction,
    required this.remoteAKeyFunction,
    required this.remoteBKeyFunction,
  });

  /// 从FFI设置构造
  factory Dx5iiSettings.fromFFI(Pointer<FFIDx5iiSettings> ffiSettingsPtr) {
    // 检查指针是否为空
    if (ffiSettingsPtr == nullptr) {
      Log.e("从FFI构造Dx5iiSettings失败：传入的指针为空");
      // 返回一个安全的默认值
      return _defaultSettings("空指针错误");
    }

    // 解引用指针获取引用
    final ffiSettings = ffiSettingsPtr.ref;

    try {
      Log.i("开始从FFI设置构造: ${ffiSettingsPtr.address}");

      // --- 读取deviceName (char[32]) ---
      final nameBytes = Uint8List(32);
      for (int i = 0; i < 32; i++) {
        nameBytes[i] = ffiSettings.device_name[i];
      }
      String name = ToppingDeviceSettings.readDeviceNameFromFFI(nameBytes);

      // --- 读取其他字段 ---
      // 使用try-catch包裹每个字段读取，增加健壮性
      int readVolume = 0;
      try {
        readVolume = ffiSettings.volume;
      } catch (e) {
        Log.e("读取volume失败: $e");
      }
      bool readMute = false;
      try {
        readMute = ffiSettings.is_mute != 0;
      } catch (e) {
        Log.e("读取is_mute失败: $e");
      }
      int readInputType = 0;
      try {
        readInputType = ffiSettings.input_type;
      } catch (e) {
        Log.e("读取input_type失败: $e");
      }
      int readOutputType = 0;
      try {
        readOutputType = ffiSettings.output_type;
      } catch (e) {
        Log.e("读取output_type失败: $e");
      }
      bool readHeadphoneEnabled = false;
      try {
        readHeadphoneEnabled = ffiSettings.headphone_enable != 0;
      } catch (e) {
        Log.e("读取headphone_enable失败: $e");
      }
      int readHeadphoneGain = 0;
      try {
        readHeadphoneGain = ffiSettings.headphone_gain;
      } catch (e) {
        Log.e("读取headphone_gain失败: $e");
      }
      int readDisplayMode = 0;
      try {
        readDisplayMode = ffiSettings.display_mode;
      } catch (e) {
        Log.e("读取display_mode失败: $e");
      }
      int readTheme = 0;
      try {
        readTheme = ffiSettings.theme;
      } catch (e) {
        Log.e("读取theme失败: $e");
      }
      int readPowerTrigger = 0;
      try {
        readPowerTrigger = ffiSettings.power_trigger;
      } catch (e) {
        Log.e("读取power_trigger失败: $e");
      }
      int readBalance = 0;
      try {
        readBalance = ffiSettings.balance;
      } catch (e) {
        Log.e("读取balance失败: $e");
      }
      int readPcmFilter = 0;
      try {
        readPcmFilter = ffiSettings.pcm_filter;
      } catch (e) {
        Log.e("读取pcm_filter失败: $e");
      }
      int readDecodeMode = 0;
      try {
        readDecodeMode = ffiSettings.decode_mode;
      } catch (e) {
        Log.e("读取decode_mode失败: $e");
      }
      bool readAudioBluetooth = false;
      try {
        readAudioBluetooth = ffiSettings.audio_bt_enable != 0;
      } catch (e) {
        Log.e("读取audio_bt_enable失败: $e");
      }
      bool readBluetoothAptx = false;
      try {
        readBluetoothAptx = ffiSettings.aptx_enable != 0;
      } catch (e) {
        Log.e("读取aptx_enable失败: $e");
      }
      bool readRemoteEnabled = false;
      try {
        readRemoteEnabled = ffiSettings.remote_enable != 0;
      } catch (e) {
        Log.e("读取remote_enable失败: $e");
      }
      int readMultifunctionKey = 0;
      try {
        readMultifunctionKey = ffiSettings.multifunction_key;
      } catch (e) {
        Log.e("读取multifunction_key失败: $e");
      }
      int readUsbMode = 0;
      try {
        readUsbMode = ffiSettings.usb_mode;
      } catch (e) {
        Log.e("读取usb_mode失败: $e");
      }
      int readScreenBrightness = 0;
      try {
        readScreenBrightness = ffiSettings.screen_brightness;
      } catch (e) {
        Log.e("读取screen_brightness失败: $e");
      }
      int readLanguage = 0;
      try {
        readLanguage = ffiSettings.language;
      } catch (e) {
        Log.e("读取language失败: $e");
      }
      int readSampling = 0;
      try {
        readSampling = ffiSettings.sampling;
      } catch (e) {
        Log.e("读取sampling失败: $e");
      }
      bool readIsOn = false;
      try {
        readIsOn = ffiSettings.is_on != 0;
      } catch (e) {
        Log.e("读取is_on失败: $e");
      }

      // --- 读取新增字段 (DX5 III 新功能) ---
      int readVuMeterLevel = 0;
      try {
        readVuMeterLevel = ffiSettings.vu_meter_level;
      } catch (e) {
        Log.e("读取vu_meter_level失败: $e");
      }
      int readVuMeterDisplayMode = 0;
      try {
        readVuMeterDisplayMode = ffiSettings.vu_meter_display_mode;
      } catch (e) {
        Log.e("读取vu_meter_display_mode失败: $e");
      }
      int readInputOptions = 0;
      try {
        readInputOptions = ffiSettings.input_options;
      } catch (e) {
        Log.e("读取input_options失败: $e");
      }
      int readOutputOptions = 0;
      try {
        readOutputOptions = ffiSettings.output_options;
      } catch (e) {
        Log.e("读取output_options失败: $e");
      }
      int readVolumeStep = 0;
      try {
        readVolumeStep = ffiSettings.volume_step;
      } catch (e) {
        Log.e("读取volume_step失败: $e");
      }
      int readPolarity = 0;
      try {
        readPolarity = ffiSettings.polarity;
      } catch (e) {
        Log.e("读取polarity失败: $e");
      }
      int readVolumeMemoryMode = 0;
      try {
        readVolumeMemoryMode = ffiSettings.volume_memory_mode;
      } catch (e) {
        Log.e("读取volume_memory_mode失败: $e");
      }
      int readPeqMemoryMode = 0;
      try {
        readPeqMemoryMode = ffiSettings.peq_memory_mode;
      } catch (e) {
        Log.e("读取peq_memory_mode失败: $e");
      }
      int readMainKeyFunction = 0;
      try {
        readMainKeyFunction = ffiSettings.main_key_function;
      } catch (e) {
        Log.e("读取main_key_function失败: $e");
      }
      int readRemoteAKeyFunction = 0;
      try {
        readRemoteAKeyFunction = ffiSettings.remote_a_key_function;
      } catch (e) {
        Log.e("读取remote_a_key_function失败: $e");
      }
      int readRemoteBKeyFunction = 0;
      try {
        readRemoteBKeyFunction = ffiSettings.remote_b_key_function;
      } catch (e) {
        Log.e("读取remote_b_key_function失败: $e");
      }

      return Dx5iiSettings(
        isOn: readIsOn,
        deviceName: name,
        volume: readVolume,
        mute: readMute,
        inputType: readInputType,
        outputType: readOutputType,
        headphoneEnabled: readHeadphoneEnabled,
        headphoneGain: readHeadphoneGain,
        displayMode: readDisplayMode,
        theme: readTheme,
        powerTrigger: readPowerTrigger,
        balance: readBalance,
        pcmFilter: readPcmFilter,
        decodeMode: readDecodeMode,
        audioBluetooth: readAudioBluetooth,
        bluetoothAptx: readBluetoothAptx,
        remoteEnabled: readRemoteEnabled,
        multifunctionKey: readMultifunctionKey,
        usbMode: readUsbMode,
        screenBrightness: readScreenBrightness,
        language: readLanguage,
        sampling: readSampling,
        // --- 新增字段 (DX5 III 新功能) ---
        vuMeterLevel: readVuMeterLevel,
        vuMeterDisplayMode: readVuMeterDisplayMode,
        inputOptions: readInputOptions,
        outputOptions: readOutputOptions,
        volumeStep: readVolumeStep,
        polarity: readPolarity,
        volumeMemoryMode: readVolumeMemoryMode,
        peqMemoryMode: readPeqMemoryMode,
        mainKeyFunction: readMainKeyFunction,
        remoteAKeyFunction: readRemoteAKeyFunction,
        remoteBKeyFunction: readRemoteBKeyFunction,
      );
    } catch (e, s) {
      // 捕获更广泛的异常，并打印堆栈跟踪
      Log.e("从FFI创建Dx5iiSettings时发生严重错误: $e\nStack trace:\n$s");
      // 出现任何严重错误时返回安全的默认值
      return _defaultSettings("构造函数错误");
    }
  }

  /// 提供默认设置的私有辅助方法
  static Dx5iiSettings _defaultSettings(String errorContext) {
    Log.w("返回默认Dx5iiSettings，错误上下文: $errorContext");
    return Dx5iiSettings(
      isOn: false,
      deviceName: "默认名称 ($errorContext)",
      volume: -1, // 使用特殊值表示错误/默认
      mute: false,
      inputType: 0,
      outputType: 0,
      headphoneEnabled: false,
      headphoneGain: 0,
      displayMode: 0,
      theme: 0,
      powerTrigger: 0,
      balance: 0,
      pcmFilter: 0,
      decodeMode: 0,
      audioBluetooth: false,
      bluetoothAptx: false,
      remoteEnabled: false,
      multifunctionKey: 0,
      usbMode: 0,
      screenBrightness: 0,
      language: 0,
      sampling: 0,
      // --- 新增字段默认值 (DX5 III 新功能) ---
      vuMeterLevel: 0,
      vuMeterDisplayMode: 0,
      inputOptions: 0,
      outputOptions: 0,
      volumeStep: 0,
      polarity: 0,
      volumeMemoryMode: 0,
      peqMemoryMode: 0,
      mainKeyFunction: 0,
      remoteAKeyFunction: 0,
      remoteBKeyFunction: 0,
    );
  }

  /// 转换为FFI设置
  Pointer<FFIDx5iiSettings> toFFI() {
    final ffiSettingsPtr = calloc<FFIDx5iiSettings>();
    try {
      final ffiSettings = ffiSettingsPtr.ref;

      // 写入deviceName
      final nameBytes = utf8.encode(deviceName);
      final List<int> nameBytesPadded = List.filled(32, 0); // 用0填充
      final lengthToCopy =
          nameBytes.length < 32 ? nameBytes.length : 31; // 确保'\0'

      for (int i = 0; i < lengthToCopy; i++) {
        nameBytesPadded[i] = nameBytes[i];
      }

      // 将Dart List<int>写入FFI
      for (int i = 0; i < 32; i++) {
        ffiSettings.device_name[i] = nameBytesPadded[i];
      }

      // 写入其他字段
      ffiSettings.is_on = isOn ? 1 : 0;
      ffiSettings.volume = volume;
      ffiSettings.is_mute = mute ? 1 : 0;
      ffiSettings.input_type = inputType;
      ffiSettings.output_type = outputType;
      ffiSettings.headphone_enable = headphoneEnabled ? 1 : 0;
      ffiSettings.headphone_gain = headphoneGain;
      ffiSettings.display_mode = displayMode;
      ffiSettings.theme = theme;
      ffiSettings.power_trigger = powerTrigger;
      ffiSettings.balance = balance;
      ffiSettings.pcm_filter = pcmFilter;
      ffiSettings.decode_mode = decodeMode;
      ffiSettings.audio_bt_enable = audioBluetooth ? 1 : 0;
      ffiSettings.aptx_enable = bluetoothAptx ? 1 : 0;
      ffiSettings.remote_enable = remoteEnabled ? 1 : 0;
      ffiSettings.multifunction_key = multifunctionKey;
      ffiSettings.usb_mode = usbMode;
      ffiSettings.screen_brightness = screenBrightness;
      ffiSettings.language = language;
      ffiSettings.sampling = sampling;

      // --- 写入新增字段 (DX5 III 新功能) ---
      ffiSettings.vu_meter_level = vuMeterLevel;
      ffiSettings.vu_meter_display_mode = vuMeterDisplayMode;
      ffiSettings.input_options = inputOptions;
      ffiSettings.output_options = outputOptions;
      ffiSettings.volume_step = volumeStep;
      ffiSettings.polarity = polarity;
      ffiSettings.volume_memory_mode = volumeMemoryMode;
      ffiSettings.peq_memory_mode = peqMemoryMode;
      ffiSettings.main_key_function = mainKeyFunction;
      ffiSettings.remote_a_key_function = remoteAKeyFunction;
      ffiSettings.remote_b_key_function = remoteBKeyFunction;

      return ffiSettingsPtr;
    } catch (e) {
      Log.e("转换为FFI设置时出错: $e");
      calloc.free(ffiSettingsPtr); // 出错时释放已分配的内存
      rethrow; // 重新抛出异常
    }
  }

  /// 释放由toFFI创建的FFI指针
  static void freeCreatedFFIPointer(Pointer<FFIDx5iiSettings> ffiSettingsPtr) {
    if (ffiSettingsPtr != nullptr) {
      calloc.free(ffiSettingsPtr);
    }
  }

  /// 重写copy方法创建副本
  @override
  Dx5iiSettings copy() {
    return Dx5iiSettings(
      isOn: isOn,
      deviceName: deviceName,
      volume: volume,
      mute: mute,
      inputType: inputType,
      outputType: outputType,
      headphoneEnabled: headphoneEnabled,
      headphoneGain: headphoneGain,
      displayMode: displayMode,
      theme: theme,
      powerTrigger: powerTrigger,
      balance: balance,
      pcmFilter: pcmFilter,
      decodeMode: decodeMode,
      audioBluetooth: audioBluetooth,
      bluetoothAptx: bluetoothAptx,
      remoteEnabled: remoteEnabled,
      multifunctionKey: multifunctionKey,
      usbMode: usbMode,
      screenBrightness: screenBrightness,
      language: language,
      sampling: sampling,
      // --- 新增字段 (DX5 III 新功能) ---
      vuMeterLevel: vuMeterLevel,
      vuMeterDisplayMode: vuMeterDisplayMode,
      inputOptions: inputOptions,
      outputOptions: outputOptions,
      volumeStep: volumeStep,
      polarity: polarity,
      volumeMemoryMode: volumeMemoryMode,
      peqMemoryMode: peqMemoryMode,
      mainKeyFunction: mainKeyFunction,
      remoteAKeyFunction: remoteAKeyFunction,
      remoteBKeyFunction: remoteBKeyFunction,
    );
  }

  /// 将对象转换为字符串用于调试
  @override
  String toString() {
    return super.toString().replaceFirst(
      '}',
      ', '
          'headphoneEnabled: $headphoneEnabled, '
          'headphoneGain: $headphoneGain, '
          'pcmFilter: $pcmFilter, '
          'decodeMode: $decodeMode, '
          'vuMeterLevel: $vuMeterLevel, '
          'vuMeterDisplayMode: $vuMeterDisplayMode, '
          'inputOptions: $inputOptions, '
          'outputOptions: $outputOptions, '
          'volumeStep: $volumeStep, '
          'polarity: $polarity, '
          'volumeMemoryMode: $volumeMemoryMode, '
          'peqMemoryMode: $peqMemoryMode, '
          'mainKeyFunction: $mainKeyFunction, '
          'remoteAKeyFunction: $remoteAKeyFunction, '
          'remoteBKeyFunction: $remoteBKeyFunction}',
    );
  }
}
