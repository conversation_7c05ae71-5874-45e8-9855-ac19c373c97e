#ifndef __BLUETOOTH_GATT_CHARACTERISTIC_H__
#define __BLUETOOTH_GATT_CHARACTERISTIC_H__
#include <string>
#include <stdint.h>
#include <vector>
namespace Topping
{
    class BluetoothGattCharacteristic
    {
    public:
        enum CharacteristicProperty
        {
            CHARACTERISTIC_PROPERTY_WRITE = 0x01,
            CHARACTERISTIC_PROPERTY_WRITE_NO_RESPONSE = 0x02,
            CHARACTERISTIC_PROPERTY_READ = 0x04,
            CHARACTERISTIC_PROPERTY_NOTIFY = 0x08,
            CHARACTERISTIC_PROPERTY_INDICATE = 0x10,
        };

    public:
        BluetoothGattCharacteristic(const std::string &uuid, int property);
        ~BluetoothGattCharacteristic();
        void setUUID(const char *uuid);
        std::string getUUID();
        void setProperty(int property);
        int getProperty();
        void setValue(const uint8_t *value, int value_len);
        const std::vector<uint8_t> &getValue();

    private:
        std::string mUUID;
        int mProperty;
        std::vector<uint8_t> mValue;
    };
}
#endif