import 'dart:async';

import 'package:flutter_blue_plus/flutter_blue_plus.dart';

import '../../model/bluetooth/ble_device.dart';
import '../../model/ffi/ffi_bluetooth_profile_state.dart';
import '../../registry/device_data_manager.dart';
import '../../registry/service_registry.dart';
import '../../utils/app_log_manager.dart';
import '../../utils/errors.dart';
import '../../utils/log_util.dart';
import '../ble_notification_helper.dart';
import '../ffi/ble_ffi_registry.dart';
import '../ffi/ble_native_notifier.dart';

/// BLE连接器
class BleConnector {
  // 当前连接的设备
  BleDevice? _connectedDevice;

  BleDevice? get connectedDevice => _connectedDevice;

  // 连接监控定时器
  Timer? _connectionMonitorTimer;

  // 添加连接状态监听
  StreamSubscription<BluetoothConnectionState>? _connectionStateSubscription;

  // 蓝牙日志管理器
  final BluetoothLogManager _logManager = BluetoothLogManager.instance;

  // 连接设备
  void connectToDevice(int deviceHandle) async {
    Log.i("尝试连接设备，句柄: $deviceHandle");

    // 记录连接尝试到蓝牙日志
    _logManager.logConnectionState("设备", "尝试连接设备 (句柄: $deviceHandle)");

    // 准备设备连接
    if (!_prepareDeviceForConnection(deviceHandle)) {
      Log.e("设备准备失败，无法连接");
      _notifyDisconnected();
      return;
    }

    // 取消旧的监听
    _connectionStateSubscription?.cancel();

    // 设置新的持续监听
    _setupConnectionStateListener();

    // 尝试连接，简化连接逻辑
    _connectWithTimeout();
  }

  /// 设置持续性的连接状态监听
  void _setupConnectionStateListener() {
    if (_connectedDevice?.flutterDevice == null) return;

    _connectionStateSubscription = _connectedDevice!
        .flutterDevice!
        .connectionState
        .skipWhile((state) => state == BluetoothConnectionState.disconnected)
        .listen((state) async {
          Log.i(
            "监听到连接状态变化: $state, 时间戳: ${DateTime.now().millisecondsSinceEpoch}",
          );

          // 记录连接状态变化到蓝牙日志
          String deviceInfo = _connectedDevice?.name ?? "未知设备";
          _logManager.logConnectionState(deviceInfo, "状态变化为 $state");

          switch (state) {
            case BluetoothConnectionState.connected:
              // 调用清理方法
              if (_connectedDevice != null) {
                DeviceDataManager().clearScanResultsExceptConnected(
                  _connectedDevice!,
                );
              } else {
                Log.e("连接设备为空，无法清理扫描结果");
              }
              final services = await _discoverServices();

              if (services.isEmpty) {
                throw ConnectionError("未能发现任何服务");
              }

              // 注册服务
              _registerServices(services);

              break;
            case BluetoothConnectionState.disconnected:
              // 只有在已经确认连接成功后才处理断开事件
              if (_connectedDevice != null) {
                _notifyDisconnected();
              }
              break;
            default:
              // 处理其他状态
              break;
          }
        });

    final subscription = _connectedDevice!.flutterDevice!.mtu.listen((int mtu) {
      // iOS: initial value is always 23, but iOS will quickly negotiate a higher value
      Log.i("mtu $mtu");
    });

    _connectedDevice!.flutterDevice?.cancelWhenDisconnected(subscription);
  }

  /// 准备设备连接 - 确保我们有有效的设备对象
  bool _prepareDeviceForConnection(int deviceHandle) {
    Log.i("准备设备连接，句柄: $deviceHandle");

    // 通过句柄查找设备
    _connectedDevice = DeviceDataManager().getDeviceByHandle(deviceHandle);

    if (_connectedDevice == null) {
      Log.e("找不到句柄为 $deviceHandle 的设备");
      return false;
    }

    // 确保设备有 Flutter 设备对象
    if (_connectedDevice!.flutterDevice == null) {
      Log.w("设备 ${_connectedDevice!.name} 没有 Flutter 设备对象，尝试查找匹配项");

      // 尝试通过 ID、名称或 MAC 地址查找匹配项
      var matchingDevice = DeviceDataManager().findDevice(
        name: _connectedDevice!.name,
        id: _connectedDevice!.id,
      );

      if (matchingDevice != null && matchingDevice.flutterDevice != null) {
        Log.i("找到匹配设备，使用其 Flutter 设备对象");
        _connectedDevice!.flutterDevice = matchingDevice.flutterDevice;
        DeviceDataManager().registerDevice(_connectedDevice!);
        return true;
      }

      // 如果找不到匹配的设备，尝试从扫描结果中查找
      Log.i("尝试从最近的扫描结果中查找匹配设备");
      var devices = DeviceDataManager().getAllDevices();
      for (var device in devices) {
        if (device.flutterDevice != null &&
            (device.name == _connectedDevice!.name ||
                device.id == _connectedDevice!.id)) {
          Log.i("在扫描结果中找到匹配设备: ${device.name}");
          _connectedDevice!.flutterDevice = device.flutterDevice;
          DeviceDataManager().registerDevice(_connectedDevice!);
          return true;
        }
      }

      Log.e("无法找到匹配的 Flutter 设备对象");
      return false;
    }

    Log.i("设备准备就绪");
    return true;
  }

  /// 使用超时的连接方法
  void _connectWithTimeout() {
    // 添加空值检查
    if (_connectedDevice == null) {
      Log.e("连接失败: 设备对象为空");
      _notifyDisconnected();
      return;
    }

    if (_connectedDevice!.flutterDevice == null) {
      Log.e("连接失败: Flutter设备对象为空，设备名称: ${_connectedDevice!.name}");
      _notifyDisconnected();
      return;
    }

    Log.i(
      "尝试连接设备: ${_connectedDevice!.name}, 时间戳: ${DateTime.now().millisecondsSinceEpoch}",
    );
    const timeout = Duration(seconds: 15);
    // 发起连接
    try {
      _connectedDevice!.flutterDevice!
          .connect(timeout: timeout, autoConnect: false)
          .catchError((error) {
            // 捕获连接过程中的异常
            Log.e("连接设备时发生错误: $error");
            _notifyDisconnected();
            return Future.error(error);
          });
    } catch (e) {
      // 捕获同步异常
      Log.e("连接设备时发生同步错误: $e");
      _notifyDisconnected();
    }
  }

  /// 发现设备服务
  Future<List<BluetoothService>> _discoverServices() async {
    try {
      final services =
          await _connectedDevice!.flutterDevice!.discoverServices();
      Log.i(
        "服务发现完成，发现 ${services.length} 个服务, 时间戳: ${DateTime.now().millisecondsSinceEpoch}",
      );
      return services;
    } catch (e) {
      // 通知断开连接
      _notifyDisconnected();
      return [];
    }
  }

  /// 注册服务
  void _registerServices(List<BluetoothService> services) {
    Log.i("注册 ${services.length} 个服务");

    // 先清空现有服务注册表，避免旧服务存在
    ServiceRegistry().clear();

    for (var service in services) {
      final serviceHandle = service.hashCode;
      ServiceRegistry().registerService(serviceHandle, service);
    }

    // 校验注册结果
    final _ = ServiceRegistry().getServices();

    // 通知原生层
    BleNativeNotifier().notifyConnectionStateChange(
      FfiBluetoothProfileState.stateConnected,
    );
    BleNativeNotifier().notifyServicesDiscovered();

    // 启用通知特征
    _enableNotifications();
  }

  /// 启用通知特征
  void _enableNotifications() {
    // 在异步上下文中启用通知
    Future(() async {
      await BleNotificationHelper.enableNotifications(
        deviceType: _connectedDevice!.deviceType,
      );
      Log.i("设备通知启用成功");
    });
  }

  // 通知断开连接
  void _notifyDisconnected() {
    Log.i("通知断开连接状态");

    // 通知原生层
    BleNativeNotifier().notifyConnectionStateChange(
      FfiBluetoothProfileState.stateDisconnected,
    );

    // 取消监控定时器
    _connectionMonitorTimer?.cancel();

    // 取消连接状态订阅
    _connectionStateSubscription?.cancel();
    _connectionStateSubscription = null;

    // 清空当前连接设备
    _connectedDevice = null;

    // 清理服务注册表
    ServiceRegistry().clear();

    Log.i("断开连接状态通知完成，所有相关状态已重置");
  }

  /// 断开设备连接
  void disconnectDevice() {
    Log.i("断开设备连接");

    if (_connectedDevice == null) {
      Log.i("断开连接：没有已连接设备，无需操作");
      return;
    }

    if (_connectedDevice!.flutterDevice == null) {
      Log.e("断开连接失败: Flutter设备对象为空，设备名称: ${_connectedDevice!.name}");
      // 即使设备对象有问题，也尝试执行断开通知
      _notifyDisconnected();
      return;
    }

    try {
      _connectedDevice!.flutterDevice!.disconnect();
      Log.i("断开连接请求已发送");
    } catch (e) {
      Log.e("断开连接时发生错误: $e");
      // 即使发生错误，也执行断开通知
      _notifyDisconnected();
    }
  }

  /// 释放资源
  void dispose() {
    Log.i("释放 BleConnector 资源");
    disconnectDevice();
    _connectionMonitorTimer?.cancel();
    _connectionStateSubscription?.cancel();
  }
}
