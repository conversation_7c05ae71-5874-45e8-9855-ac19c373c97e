abstract class RwcpListener {
  /// 向连接的服务器发送RWCP段的字节。
  ///
  /// 参数：
  ///   bytes - 要发送的字节。
  ///
  /// 返回：
  ///   如果发送可以被处理则返回true。
  bool sendRwcpSegment(List<int> bytes);

  /// 当RWCP传输失败时调用。传输在以下情况下失败：
  /// <ul>
  ///     <li>在传输层发送段失败。</li>
  ///     <li>服务器发送了RST段。</li>
  /// </ul>
  void onTransferFailed();

  /// 当给该客户端的所有数据都已成功发送并得到确认时调用。
  void onTransferFinished();

  /// 当一些新的段已被确认时调用，用以通知监听器。
  ///
  /// 参数：
  ///   acknowledged - 已被确认的段数量。
  void onTransferProgress(int acknowledged);
}
