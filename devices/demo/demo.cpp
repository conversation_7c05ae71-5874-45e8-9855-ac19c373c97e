#include "BluetoothManager.h"
#include "FileAdapter.h"
#include "DemoDevice.h"
#include <stdio.h>
#include <string.h>
int main()
{
    Topping::FileAdapter fileAdapter;
    Topping::BluetoothManager &bluetoothManager = Topping::BluetoothManager::getInstance();
    bluetoothManager.registerBluetoothLeAdapter(&fileAdapter);

    Topping::DemoDevice demoDevice;
    printf("输入quit按回车结束。\n");
    char buff[256];
    for (;;)
    {
        scanf("%s", buff);
        if (strcmp(buff, "quit") == 0)
        {
            break;
        }
        else if (strcmp(buff, "scan") == 0)
        {
            printf("scan\n");
            demoDevice.startScan();
        }
        else if (strcmp(buff, "connect") == 0)
        {
            printf("connect\n");
            demoDevice.connect();
        }
        else if (strcmp(buff, "verify") == 0)
        {
            printf("verify\n");
            demoDevice.verify();
        }
        else if (buff[0] != '\0')
        {
            demoDevice.sendMsg(buff);
        }
        fflush(stdin);
        buff[0] = '\0';
    }
    return 0;
}