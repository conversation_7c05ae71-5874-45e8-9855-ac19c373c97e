import 'dart:async';

import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:rxdart/rxdart.dart';
import 'package:topping_ble_control/bluetooth/ble_operation_manager.dart';
import 'package:topping_ble_control/event/connection_state_event.dart';
import 'package:topping_ble_control/model/base/topping_verify_result_type.dart';
import 'package:topping_ble_control/model/bluetooth/ble_device.dart';
import 'package:topping_ble_control/model/enums/device_mode_type.dart';
import 'package:topping_ble_control/utils/log_util.dart';

import '../model/enums/ble_connection_state.dart';
import '../registry/current_connecting_device.dart';
import '../registry/device_data_manager.dart';
import 'device_factory.dart';

/// Topping设备管理器基类
/// 提取D900和DX5共同的功能和接口
abstract class ToppingDeviceManager implements DeviceManager {
  // 扫描和连接状态控制器
  late final StreamController<List<BleDevice>> scanResultsController;
  late final StreamController<ConnectionStateEvent> deviceStateController;
  late final StreamController<bool> isScanningController;

  // 设备状态控制器
  late final StreamController<ToppingVerifyResultType> verifyResultController;
  late final StreamController<bool> powerStateController;
  late final StreamController<String> deviceNameController;
  late final StreamController<int> volumeController;
  late final StreamController<bool> muteController;
  late final StreamController<int> inputTypeController;
  late final StreamController<int> outputTypeController;
  late final StreamController<bool> headphoneEnabledController;
  late final StreamController<int> headphoneGainController;
  late final StreamController<int> displayModeController;
  late final StreamController<int> themeController;
  late final StreamController<int> powerTriggerController;
  late final StreamController<int> balanceController;
  late final StreamController<int> filterController;
  late final StreamController<int> decodeModeController;
  late final StreamController<bool> audioBluetoothController;
  late final StreamController<bool> bluetoothAptxController;
  late final StreamController<bool> relayController;
  late final StreamController<int> multifunctionKeyController;
  late final StreamController<int> usbModeController;
  late final StreamController<int> screenBrightnessController;
  late final StreamController<int> languageController;
  late final StreamController<int> samplingRateController;
  late final StreamController<dynamic> settingsController;

  // BLE操作管理器
  final BleOperationManager _bleManager = BleOperationManager();

  // 蓝牙扫描订阅
  StreamSubscription? _isScanningBlueSubscription;
  StreamSubscription? _scanResultsBlueSubscription;

  // 公开的扫描和连接状态流
  @override
  Stream<List<BleDevice>> get scanResults => scanResultsController.stream;

  @override
  Stream<bool> get isScanning => isScanningController.stream;

  @override
  Stream<ConnectionStateEvent> get deviceState => deviceStateController.stream;

  // 设备状态相关的Stream
  @override
  Stream<ToppingVerifyResultType> get verifyResult =>
      verifyResultController.stream;

  @override
  Stream<bool> get powerState => powerStateController.stream;

  @override
  Stream<String> get deviceName => deviceNameController.stream;

  @override
  Stream<int> get volume => volumeController.stream;

  @override
  Stream<bool> get mute => muteController.stream;

  @override
  Stream<int> get inputType => inputTypeController.stream;

  @override
  Stream<int> get outputType => outputTypeController.stream;

  @override
  Stream<bool> get headphoneEnabled => headphoneEnabledController.stream;

  @override
  Stream<int> get headphoneGain => headphoneGainController.stream;

  @override
  Stream<int> get displayMode => displayModeController.stream;

  @override
  Stream<int> get theme => themeController.stream;

  @override
  Stream<int> get powerTrigger => powerTriggerController.stream;

  @override
  Stream<int> get balance => balanceController.stream;

  @override
  Stream<int> get filter => filterController.stream;

  @override
  Stream<int> get decodeMode => decodeModeController.stream;

  @override
  Stream<bool> get audioBluetooth => audioBluetoothController.stream;

  @override
  Stream<bool> get bluetoothAptx => bluetoothAptxController.stream;

  @override
  Stream<bool> get relay => relayController.stream;

  @override
  Stream<int> get multifunctionKey => multifunctionKeyController.stream;

  @override
  Stream<int> get usbMode => usbModeController.stream;

  @override
  Stream<int> get screenBrightness => screenBrightnessController.stream;

  @override
  Stream<int> get language => languageController.stream;

  @override
  Stream<int> get samplingRate => samplingRateController.stream;

  @override
  Stream<dynamic>? get settings => settingsController.stream;

  // 特殊事件流
  final _resetSettingsSubject = PublishSubject<void>();
  final _restoreFactorySettingsSubject = PublishSubject<void>();

  @override
  Stream<void> get onResetSettings => _resetSettingsSubject.stream;

  @override
  Stream<void> get onRestoreFactorySettings =>
      _restoreFactorySettingsSubject.stream;

  /// 初始化控制器
  /// 子类构造函数应调用此方法
  void initializeControllers() {
    scanResultsController = StreamController<List<BleDevice>>.broadcast();
    deviceStateController = StreamController<ConnectionStateEvent>.broadcast();
    isScanningController = StreamController<bool>.broadcast();

    // 初始化设备状态控制器
    verifyResultController =
        StreamController<ToppingVerifyResultType>.broadcast();
    powerStateController = StreamController<bool>.broadcast();
    deviceNameController = StreamController<String>.broadcast();
    volumeController = StreamController<int>.broadcast();
    muteController = StreamController<bool>.broadcast();
    inputTypeController = StreamController<int>.broadcast();
    outputTypeController = StreamController<int>.broadcast();
    headphoneEnabledController = StreamController<bool>.broadcast();
    headphoneGainController = StreamController<int>.broadcast();
    displayModeController = StreamController<int>.broadcast();
    themeController = StreamController<int>.broadcast();
    powerTriggerController = StreamController<int>.broadcast();
    balanceController = StreamController<int>.broadcast();
    filterController = StreamController<int>.broadcast();
    decodeModeController = StreamController<int>.broadcast();
    audioBluetoothController = StreamController<bool>.broadcast();
    bluetoothAptxController = StreamController<bool>.broadcast();
    relayController = StreamController<bool>.broadcast();
    multifunctionKeyController = StreamController<int>.broadcast();
    usbModeController = StreamController<int>.broadcast();
    screenBrightnessController = StreamController<int>.broadcast();
    languageController = StreamController<int>.broadcast();
    samplingRateController = StreamController<int>.broadcast();
    settingsController = StreamController<dynamic>.broadcast();
  }

  /// 初始化蓝牙监听
  /// 子类构造函数应调用此方法
  void initializeBleListeners() {
    _scanResultsBlueSubscription = _bleManager.scanner.scanResults.listen((
      results,
    ) {
      if (!scanResultsController.isClosed) scanResultsController.add(results);
    });

    _isScanningBlueSubscription = FlutterBluePlus.isScanning.listen((state) {
      if (!isScanningController.isClosed) isScanningController.add(state);
    });
  }

  /// 触发设置重置事件
  void triggerResetSettings() {
    _resetSettingsSubject.add(null);
  }

  /// 触发恢复出厂设置事件
  void triggerRestoreFactorySettings() {
    _restoreFactorySettingsSubject.add(null);
  }

  /// 关闭所有流
  void closeAllStreams() {
    scanResultsController.close();
    deviceStateController.close();
    isScanningController.close();

    verifyResultController.close();
    powerStateController.close();
    deviceNameController.close();
    volumeController.close();
    muteController.close();
    inputTypeController.close();
    outputTypeController.close();
    headphoneEnabledController.close();
    headphoneGainController.close();
    displayModeController.close();
    themeController.close();
    powerTriggerController.close();
    balanceController.close();
    filterController.close();
    decodeModeController.close();
    audioBluetoothController.close();
    bluetoothAptxController.close();
    relayController.close();
    multifunctionKeyController.close();
    usbModeController.close();
    screenBrightnessController.close();
    languageController.close();
    samplingRateController.close();
    settingsController.close();

    _resetSettingsSubject.close();
    _restoreFactorySettingsSubject.close();
  }

  /// 获取蓝牙管理器实例
  BleOperationManager get bleManager => _bleManager;

  /// 扫描设备实现
  @override
  void startScan({
    List<DeviceModeType> deviceTypes = const [DeviceModeType.dx5],
  }) {
    Log.i('开始扫描设备');
    _bleManager.startScan(deviceTypes: deviceTypes);
  }

  /// 停止扫描实现
  @override
  void stopScan() {
    Log.i("停止扫描设备");
    _bleManager.stopScan();
  }

  /// 获取验证结果类型的通用方法
  ToppingVerifyResultType mapVerifyTypeToEnum(int type) {
    return ToppingVerifyResultTypeExtension.fromValue(type);
  }

  /// 安全地添加事件到流控制器
  void safeAddEvent<T>(StreamController<T> controller, T event) {
    try {
      if (!controller.isClosed) {
        controller.add(event);
      }
    } catch (e, s) {
      Log.e('添加事件到控制器错误: $e\n$s');
    }
  }

  /// 实现D900特有方法的默认空实现
  @override
  void enableUsbDsdPassthrough(bool enable) {
    // 默认空实现
    Log.i("enableUsbDsdPassthrough方法未实现");
  }

  @override
  void setIisPhase(int phase) {
    // 默认空实现
    Log.i("setIisPhase方法未实现");
  }

  @override
  void setIisDsdChannel(int channel) {
    // 默认空实现
    Log.i("setIisDsdChannel方法未实现");
  }

  @override
  void setUsbSelect(int type) {
    // 默认空实现
    Log.i("setUsbSelect方法未实现");
  }

  @override
  void enableUsbDsd(bool enable) {
    // 默认空实现
    Log.i("enableUsbDsd方法未实现");
  }

  @override
  void setIisChannel(int channel) {
    // 默认空实现
    Log.i("setIisChannel方法未实现");
  }

  /// 获取设备绑定实例（模板方法）
  /// 子类需要重写此方法，提供具体的设备绑定实例
  dynamic get deviceBindings {
    throw UnimplementedError('子类必须提供设备绑定实例');
  }

  /// 获取设备类型名称（如"D900"、"DX5"）
  /// 子类需要重写此方法，提供设备类型名
  String get deviceTypeName {
    return "通用";
  }

  /// 模板方法 - 连接设备
  /// 提供通用连接实现，子类可以覆盖它来提供额外的特定逻辑
  Future<void> connectDevice(int deviceHandle) async {
    Log.i(
      "$deviceTypeName 连接时间打印 --- 准备连接设备，句柄: $deviceHandle, 时间戳: ${DateTime.now().millisecondsSinceEpoch}",
    );

    // 设置当前连接设备句柄
    CurrentConnectingDevice().handle = deviceHandle;

    // 在调用原生连接前，先主动发送一个连接中状态事件
    String? deviceName;
    BleDevice? device = DeviceDataManager().getDeviceByHandle(deviceHandle);

    // 如果找不到设备，尝试使用其他方式查找
    if (device == null) {
      Log.w("$deviceTypeName 通过句柄找不到设备，尝试使用其他方式查找");

      // 获取所有设备并尝试找到一个可用的
      var allDevices = DeviceDataManager().getAllDevices();
      if (allDevices.isNotEmpty) {
        // 使用第一个设备
        device = allDevices.first;
        Log.i(
          "$deviceTypeName 使用可用设备: ${device.name}, 句柄: ${device.nativeHandle}",
        );

        // 更新句柄
        deviceHandle = device.nativeHandle;
        CurrentConnectingDevice().handle = deviceHandle;
      } else {
        Log.e("$deviceTypeName 找不到任何可用设备，无法连接");
        return; // 如果没有设备，直接返回
      }
    }

    deviceName = device.name;

    // 确保设备有Flutter设备对象
    if (device.flutterDevice == null) {
      Log.w("$deviceTypeName 设备没有Flutter设备对象，尝试查找匹配项");

      // 尝试从所有设备中找到一个有Flutter设备对象的
      var allDevices = DeviceDataManager().getAllDevices();
      for (var d in allDevices) {
        if (d.flutterDevice != null) {
          Log.i("$deviceTypeName 找到带Flutter设备对象的设备: ${d.name}");
          device.flutterDevice = d.flutterDevice;
          DeviceDataManager().registerDevice(device); // 更新设备注册
          break;
        }
      }
    }

    // 创建并广播连接中状态
    final connectingEvent = ConnectionStateEvent(
      BleConnectionState.connecting,
      deviceHandle,
      deviceName: deviceName,
    );
    if (!deviceStateController.isClosed) {
      deviceStateController.add(connectingEvent);
    }

    Log.i(
      "$deviceTypeName 发送连接中事件完成, 时间戳: ${DateTime.now().millisecondsSinceEpoch}",
    );
    Log.i(
      "$deviceTypeName 调用底层连接函数, 时间戳: ${DateTime.now().millisecondsSinceEpoch}",
    );

    // 发起连接
    try {
      // 先尝试使用BLE管理器连接
      await bleManager.connect(deviceHandle: deviceHandle).catchError((error) {
        Log.e("$deviceTypeName 连接设备时发生错误: $error");
        // 发送断开连接状态
        final disconnectedEvent = ConnectionStateEvent(
          BleConnectionState.disconnected,
          deviceHandle,
          deviceName: deviceName,
        );
        if (!deviceStateController.isClosed) {
          deviceStateController.add(disconnectedEvent);
        }
      });

      // 然后调用原生连接（子类应该覆盖此方法以提供特定的连接逻辑）
      connectNative(deviceHandle);
      Log.i(
        "$deviceTypeName 底层连接函数调用完成, 时间戳: ${DateTime.now().millisecondsSinceEpoch}",
      );
    } catch (e) {
      Log.e("$deviceTypeName 连接设备时发生错误: $e");

      // 发送断开连接状态
      final disconnectedEvent = ConnectionStateEvent(
        BleConnectionState.disconnected,
        deviceHandle,
        deviceName: deviceName,
      );
      if (!deviceStateController.isClosed) {
        deviceStateController.add(disconnectedEvent);
      }
    }
  }

  /// 模板方法 - 原生连接实现
  /// 子类需要实现此方法以提供特定设备的连接逻辑
  void connectNative(int deviceHandle) {
    throw UnimplementedError('子类必须实现原生连接方法');
  }

  /// 释放资源
  @override
  void dispose() {
    Log.i("释放ToppingDeviceManager资源");
    _isScanningBlueSubscription?.cancel();
    _scanResultsBlueSubscription?.cancel();
    closeAllStreams();
  }

  /// 启用/禁用耳机功能（DX5II特有）
  /// 默认空实现仅记录警告
  @override
  void enableHeadphone(bool enable) {
    Log.i("enableHeadphone方法是DX5II特有的，当前设备不支持");
  }

  /// 设置耳机增益（DX5II特有）
  /// 默认空实现仅记录警告
  @override
  void setHeadphoneGain(int gainType) {
    Log.i("setHeadphoneGain方法是DX5II特有的，当前设备不支持");
  }
}
