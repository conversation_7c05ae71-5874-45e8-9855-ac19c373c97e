import 'package:topping_ble_control/bluetooth/ble_operation_manager.dart';
import 'package:topping_ble_control/config/ble_config.dart';
import 'package:topping_ble_control/utils/log_util.dart';

import '../model/enums/device_mode_type.dart';

/// 蓝牙通知助手类
/// 用于简化蓝牙通知的设置
class BleNotificationHelper {
  /// 启用通知特征
  /// [deviceType] 设备类型，默认为DX5 II
  static Future<void> enableNotifications({
    required DeviceModeType deviceType,
  }) async {
    try {
      // 获取BLE管理器实例
      final manager = BleOperationManager();

      // 获取对应设备类型的配置
      final config = BleConfig().getConfig(deviceType);

      // 尝试启用通知特征
      await manager.setCharacteristicNotification(
        config.notifyCharacteristicUuid,
        true,
      );

      Log.i('已启用 ${config.deviceName} 的通知特征');
    } catch (e) {
      Log.e('启用通知特征失败: $e');
      rethrow;
    }
  }

  /// 禁用通知特征
  /// [deviceType] 设备类型，默认为DX5 II
  static Future<void> disableNotifications({
    DeviceModeType deviceType = DeviceModeType.dx5,
  }) async {
    try {
      // 获取BLE管理器实例
      final manager = BleOperationManager();

      // 获取对应设备类型的配置
      final config = BleConfig().getConfig(deviceType);

      // 尝试禁用通知特征
      await manager.setCharacteristicNotification(
        config.notifyCharacteristicUuid,
        false,
      );

      Log.i('已禁用 ${config.deviceName} 的通知特征');
    } catch (e) {
      Log.e('禁用通知特征失败: $e');
      rethrow;
    }
  }
}
