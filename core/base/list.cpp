#include "list.h"
#include <stdio.h>
void insert_to_single_list(single_list_node_t **handle, single_list_node_t *node)
{
	if (NULL == *handle)
	{
		node->next = node;
	}
	else
	{
		node->next = *handle;
	}
	*handle = node;
}

void remove_from_single_list(single_list_node_t **handle)
{
	single_list_node_t *node = *handle;
	if (node->next == node)
	{
		*handle = NULL;
	}
	else
	{
		*handle = node->next;
	}
}

void insert_to_front(list_node_t **handle, list_node_t *node)
{
	if (NULL == *handle)
	{
		node->pre = node;
		node->next = node;
	}
	else
	{
		node->pre = (*handle)->pre;
		node->next = *handle;
		(*handle)->pre = node;
		node->pre->next = node;
	}
	*handle = node;
}

void insert_to_back(list_node_t **handle, list_node_t *node)
{
	if (NULL == *handle)
	{
		node->pre = node;
		node->next = node;
		*handle = node;
	}
	else
	{
		node->pre = (*handle)->pre;
		node->next = *handle;
		(*handle)->pre = node;
		node->pre->next = node;
	}
}

void insert_to_middle(list_node_t **handle, list_node_t *node, int n)
{
	if (NULL == *handle)
	{
		node->pre = node;
		node->next = node;
		*handle = node;
	}
	else
	{
		list_node_t *cur = *handle;
		for (int i = 0; i < n; i++)
		{
			cur = cur->next;
		}
		node->pre = cur->pre;
		node->next = cur;
		cur->pre = node;
		node->pre->next = node;
		if (0 == n)
		{
			*handle = node;
		}
	}
}

void remove_from_list(list_node_t **handle, list_node_t *node)
{
	if (node->pre == node && node->next == node)
	{
		*handle = NULL;
	}
	else
	{
		node->pre->next =
			node->next;
		node->next->pre =
			node->pre;
		if (*handle == node)
		{
			*handle = node->next;
		}
	}
}

list_node_t *get_back_from_list(list_node_t **handle)
{
	if (*handle != NULL)
	{
		return (*handle)->pre;
	}
	return NULL;
}

