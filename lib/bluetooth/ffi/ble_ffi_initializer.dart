import 'dart:async';
import 'dart:ffi';

import 'package:ffi/ffi.dart';

import '../../config/ble_config.dart';
import '../../model/enums/device_mode_type.dart';
import '../../model/ffi/ffi_bluetooth_profile_state.dart';
import '../../model/ffi/ffi_gatt_characteristic.dart';
import '../../registry/current_connecting_device.dart';
import '../../utils/log_util.dart';
import '../ble_bindings.dart';
import '../ble_operation_manager.dart';
import 'ble_ffi_registry.dart';
import 'ble_native_notifier.dart';

/// BLE FFI 初始化器
class BleFfiInitializer {
  static final BleFfiInitializer _instance = BleFfiInitializer._internal();

  factory BleFfiInitializer() => _instance;

  BleFfiInitializer._internal();

  static bool _isInitialized = false;
  static bool get isInitialized => _isInitialized;

  static final Completer<void> _initCompleter = Completer<void>();
  static Future<void> get initializationComplete => _initCompleter.future;

  // 初始化所有FFI功能
  Future<void> initialize() async {
    if (_isInitialized) {
      Log.i("BLE FFI系统已初始化");
      if (!_initCompleter.isCompleted) {
        _initCompleter.complete();
      }
      return;
    }

    try {
      Log.i("开始初始化BLE FFI系统");

      // 先注册函数
      _registerBleScannerFunctions();
      _registerGattFunctions();

      // 等待一段时间，确保注册完成
      await Future.delayed(Duration(milliseconds: 100));

      // 然后注册适配器
      Log.i("注册默认蓝牙适配器");
      BleBindings.instance.registerDefaultAdapter();

      // 等待一段时间，确保适配器注册完成
      await Future.delayed(Duration(milliseconds: 100));

      _isInitialized = true;
      if (!_initCompleter.isCompleted) {
        _initCompleter.complete();
      }
      Log.i("BLE FFI系统初始化完成");
    } catch (e) {
      Log.e("初始化BLE FFI系统失败: $e");
      if (!_initCompleter.isCompleted) {
        _initCompleter.completeError(e);
      }
      // Rethrow the error so it can be caught by the caller if needed
      rethrow;
    }
  }

  Future<bool> ensureInitialized({
    Duration timeout = const Duration(seconds: 5),
  }) async {
    if (_isInitialized) return true;

    Log.i("等待BLE FFI初始化...");
    try {
      // 如果initialize()还没有被调用，先调用它
      if (!_initCompleter.isCompleted && !_isInitialized) {
        // 注意：这里不应该await，因为initialize本身会完成completer
        // 如果这里await，而initialize内部又complete了，可能会导致问题
        // 或者，initialize可以返回Future，这里await initialize()
        // 但当前的initialize设计是void，依赖completer
        // 更好的方式是让 initialize 返回 Future<void>
        // 但为了最小改动，我们假设 initialize 会被其他地方调用，或者这里是非阻塞的启动
        // 此处改为直接调用 initialize，并依赖 completer
        initialize().catchError((e) {
          // 确保即使初始化失败，completer也能被完成
          if (!_initCompleter.isCompleted) {
            _initCompleter.completeError(e);
          }
          Log.e("ensureInitialized 中调用 initialize 失败: $e");
        });
      }
      await initializationComplete.timeout(timeout);
      Log.i("BLE FFI初始化已完成 (通过ensureInitialized检查)");
      return _isInitialized; // 再次检查状态
    } catch (e) {
      Log.e("等待BLE FFI初始化超时或失败: $e");
      return false;
    }
  }

  // 注册扫描器函数
  void _registerBleScannerFunctions() {
    BleBindings.instance.registerBleScannerFunctions(
      init: (nativeObject) {
        Log.i("C++调用init, bleScanner nativeObject: $nativeObject");
        return 0;
      },
      uninit: (flutterObject) {
        Log.i("C++调用uninit,bleScanner flutterObject: $flutterObject");
      },
      startScan: (flutterObject) {
        Log.i("C++调用startScan, bleScanner flutterObject: $flutterObject");
      },
      stopScan: (flutterObject) {
        Log.i("C++调用stopScan, bleScanner flutterObject: $flutterObject");
      },
    );
  }

  // 注册GATT函数
  void _registerGattFunctions() {
    BleBindings.instance.registerGattFunctions(
      init: (nativeObject) {
        Log.i("C++调用init, gatt nativeObject: $nativeObject");
        final flutterObjectId = DateTime.now().millisecondsSinceEpoch;
        BleFfiRegistry().registerGattMapping(nativeObject, flutterObjectId);
        return flutterObjectId;
      },
      uninit: (flutterObject) {
        Log.i("C++调用uninit, gatt flutterObject: $flutterObject");
        BleFfiRegistry().removeGattMapping(flutterObject);
      },
      close: (flutterObject) {
        Log.i("C++调用close, gatt flutterObject: $flutterObject");
      },
      connect: (flutterObject) {
        Log.i("C++调用connect, gatt flutterObject: $flutterObject");
        _handleConnect();
      },
      disconnect: (flutterObject) {
        Log.i("C++调用disconnect, gatt flutterObject: $flutterObject");
        BleOperationManager().disconnect();
      },
      requestMtu: (flutterObject, mtu) => 1,
      // 简化版本
      writeCharacteristic: (flutterObject, characteristic) {
        Log.i("C++调用writeCharacteristic, flutterObject: $flutterObject");
        _handleWriteCharacteristic(characteristic);
        return 1;
      },
      setCharacteristicNotification: (flutterObject, characteristic, enable) {
        Log.i(
          "C++调用setCharacteristicNotification, flutterObject: $flutterObject, enable: $enable",
        );
        // _handleSetNotification(characteristic, enable != 0);
        return 1;
      },
      getService: (flutterObject, uuid) {
        try {
          final uuidStr = uuid.cast<Utf8>().toDartString();
          Log.i(
            "C++调用getService: flutterObject: $flutterObject, uuid: $uuidStr",
          );

          // 尝试从服务注册表直接获取服务ID
          final manager = BleOperationManager();

          final serviceId = manager.characteristicHandler.getService(uuidStr);
          return serviceId;
        } catch (e) {
          Log.e("获取服务错误: $e");
          return 0;
        }
      },
    );
  }

  // 处理连接请求
  void _handleConnect() {
    int handle = CurrentConnectingDevice().handle ?? 0;
    Log.i("连接请求，句柄: $handle");
    if (handle == 0) {
      BleNativeNotifier().notifyConnectionStateChange(
        FfiBluetoothProfileState.stateDisconnected,
      );
      return;
    }

    Future(() async {
      BleOperationManager().connect(deviceHandle: handle);
    });
  }

  // 处理写特征值请求
  void _handleWriteCharacteristic(
    Pointer<FFIGattCharacteristic> characteristic,
  ) {
    try {
      // 获取当前设备类型的配置
      // 默认使用DX5 II的配置
      final config = BleConfig().getConfig(DeviceModeType.dx5);

      // 写特征
      String writeUUID = config.writeCharacteristicUuid;

      // 获取值
      List<int> value = [];
      for (int i = 0; i < characteristic.ref.value_len; i++) {
        value.add(characteristic.ref.value[i]);
      }

      // 写入
      Future(() async {
        BleOperationManager().writeCharacteristic(writeUUID, value);
      });
    } catch (e) {
      Log.e('处理写特征值请求失败: $e');
    }
  }
}
