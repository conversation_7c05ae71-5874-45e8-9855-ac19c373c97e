import 'package:flutter_blue_plus/flutter_blue_plus.dart';

import '../model/enums/device_mode_type.dart';
import '../utils/log_util.dart';

/// 基础蓝牙配置类
abstract class BaseBleConfig {
  // 扫描配置 - 所有设备共享
  final Duration scanTimeout = const Duration(seconds: 15);
  final Duration scanThrottle = const Duration(milliseconds: 500);

  // 连接配置 - 所有设备共享
  final int connectionRetryCount = 3;
  final Duration connectionTimeout = const Duration(seconds: 15);
  final Duration connectionMonitorInterval = const Duration(seconds: 30);
  final int defaultMtu = 512;
  final Duration writeTimeout = const Duration(seconds: 5);

  // 日志配置 - 所有设备共享
  final bool verboseLogging = false; // 设置为false以减少日志输出，提高性能

  // 设备特定配置 - 子类必须实现
  String get serviceUuid;
  String get writeCharacteristicUuid;
  String get notifyCharacteristicUuid;
  int get vendorId;
  int get productId;
  List<MsdFilter> get manufacturerFilters;
  String get deviceName;
}

/// DX5 II 设备配置
class DX5IIBleConfig extends BaseBleConfig {
  @override
  String get serviceUuid => "000090FB-0000-1000-8000-00805F9B34FB";

  @override
  String get writeCharacteristicUuid => "00008efa-0000-1000-8000-00805f9b34fb";

  @override
  String get notifyCharacteristicUuid => "00009CF1-0000-1000-8000-00805F9B34FB";

  @override
  int get vendorId => 19295; // 0xf4fb

  @override
  int get productId => 0x6ac0;

  @override
  List<MsdFilter> get manufacturerFilters => [
    MsdFilter(vendorId, data: [0x6a, 0xc0]),
  ];

  @override
  String get deviceName => "DX5 II";
}

/// DX9 设备配置 (示例，需要根据实际参数调整)
class DX9BleConfig extends BaseBleConfig {
  @override
  String get serviceUuid => "000090FB-0000-1000-8000-00805F9B34FB"; // 假设与DX5 II相同

  @override
  String get writeCharacteristicUuid => "00008efa-0000-1000-8000-00805f9b34fb"; // 假设与DX5 II相同

  @override
  String get notifyCharacteristicUuid => "00009CF1-0000-1000-8000-00805F9B34FB"; // 假设与DX5 II相同

  @override
  int get vendorId => 24232; // 假设与DX5 II相同

  @override
  int get productId => 0x561d; // 假设产品ID不同

  @override
  List<MsdFilter> get manufacturerFilters => [
    MsdFilter(vendorId, data: [0x56, 0x1d]),
  ];

  @override
  String get deviceName => "DX9";
}

/// 蓝牙配置工厂类
class BleConfig {
  // 单例实例
  static final BleConfig _instance = BleConfig._internal();

  // 私有构造函数
  BleConfig._internal();

  // 工厂构造函数
  factory BleConfig() => _instance;

  // 根据设备类型获取配置
  BaseBleConfig getConfig(DeviceModeType deviceType) {
    switch (deviceType) {
      case DeviceModeType.dx5:
        return DX5IIBleConfig();
      case DeviceModeType.dx9:
        return DX9BleConfig();
      case DeviceModeType.unknown:
        // 默认返回DX5 II配置，或者可以抛出异常
        return DX5IIBleConfig();
    }
  }

  // 根据产品ID识别设备类型
  DeviceModeType identifyDeviceType(int vendorId, int productId) {
    Log.i("identifyDeviceType: vendorId: $vendorId, productId: $productId");
    if (vendorId == 19295) {
      if (productId == 0x6ac0) {
        return DeviceModeType.dx5;
      }
    } else if (vendorId == 24232) {
      if (productId == 0x561d) {
        return DeviceModeType.dx9;
      }
    }
    return DeviceModeType.unknown;
  }

  // 为了向后兼容，提供静态访问方法
  static BaseBleConfig get dx5II => _instance.getConfig(DeviceModeType.dx5);
  static BaseBleConfig get dx9 => _instance.getConfig(DeviceModeType.dx9);
}
