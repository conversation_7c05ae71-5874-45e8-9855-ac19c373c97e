import 'dart:ffi';
import 'dart:convert'; // 用于utf8解码
import 'dart:typed_data'; // 用于Uint8List

/// 用于与C++交互的D900设备设置结构体声明
final class FFID900Settings extends Struct {
  /// 设备电源状态 (1=开，0=关)
  @Int32()
  external int is_on;

  /// 设备名称，固定32字节长度
  @Array<Uint8>(32)
  external Array<Uint8> device_name;

  /// 设备音量
  @Int32()
  external int volume;

  /// 静音状态 (1=静音，0=非静音)
  @Int32()
  external int is_mute;

  /// 输入类型
  @Int32()
  external int input_type;

  /// 输出类型
  @Int32()
  external int output_type;

  /// 显示模式
  @Int32()
  external int display_mode;

  /// 主题
  @Int32()
  external int theme;

  /// 电源触发器
  @Int32()
  external int power_trigger;

  /// USB选择类型（D900特有）
  @Int32()
  external int usb_select;

  /// 声道平衡
  @Int32()
  external int balance;

  /// 蓝牙音频启用状态 (1=启用，0=禁用)
  @Int32()
  external int audio_bt_enable;

  /// 蓝牙aptX启用状态 (1=启用，0=禁用)
  @Int32()
  external int aptx_enable;

  /// 遥控启用状态 (1=启用，0=禁用)
  @Int32()
  external int remote_enable;

  /// 多功能键设置
  @Int32()
  external int multifunction_key;

  /// USB DSD启用状态（D900特有）(1=启用，0=禁用)
  @Int32()
  external int usb_dsd_enable;

  /// USB模式
  @Int32()
  external int usb_mode;

  /// IIS相位（D900特有）
  @Int32()
  external int iis_phase;

  /// IIS通道（D900特有）
  @Int32()
  external int iis_channel;

  /// 屏幕亮度
  @Int32()
  external int screen_brightness;

  /// 语言设置
  @Int32()
  external int language;

  /// 采样率
  @Int32()
  external int sampling;

  // --- 新增字段 (D900新版功能) ---
  @Int32()
  external int vu_meter_level;              // 新增: VU表0dDB幅值

  @Int32()
  external int vu_meter_display_mode;       // 新增: VU条显示模式

  @Int32()
  external int input_options;               // 新增: 输入选项位掩码

  @Int32()
  external int output_options;              // 新增: 输出选项位掩码

  @Int32()
  external int usb_port_select;             // 新增: USB接口选择

  @Int32()
  external int dsd_mute;                    // 新增: DSD MUTE模式

  @Int32()
  external int output_level;                // 新增: 输出幅值

  @Int32()
  external int volume_step;                 // 新增: 音量步进

  @Int32()
  external int polarity;                    // 新增: 极性设置

  @Int32()
  external int peq_enable;                  // 新增: PEQ启用状态

  @Int32()
  external int peq_preset;                  // 新增: PEQ预设选择

  @Int32()
  external int dsd_direct_enable;           // 新增: DSD直通启用状态

  @Int32()
  external int volume_memory_mode;          // 新增: 音量记忆方式

  @Int32()
  external int peq_memory_mode;             // 新增: PEQ记忆方式

  @Int32()
  external int main_key_function;           // 新增: 主按键功能

  @Int32()
  external int remote_a_key_function;       // 新增: 遥控A键功能

  @Int32()
  external int remote_b_key_function;       // 新增: 遥控B键功能

  /// toString
  @override
  String toString() {
    // 正确处理设备名称
    String deviceNameStr = "解码错误";
    try {
      // 将Array<Uint8>转换为Uint8List以便处理
      final nameBytes = Uint8List(32);
      for (int i = 0; i < 32; i++) {
        nameBytes[i] = device_name[i];
      }

      // 找到C字符串的结束符'\0' (ASCII 0)
      int end = nameBytes.indexOf(0);
      if (end == -1) {
        end = nameBytes.length; // 如果没有'\0'，则取全部字节
      }

      // 解码 (假设是UTF-8)
      deviceNameStr = utf8.decode(
        nameBytes.sublist(0, end),
        allowMalformed: true,
      );
    } catch (e) {
      deviceNameStr = "解码错误: $e";
    }

    return 'FFID900Settings{'
        'is_on: $is_on, '
        'volume: $volume, '
        'is_mute: $is_mute, '
        'input_type: $input_type, '
        'output_type: $output_type, '
        'display_mode: $display_mode, '
        'theme: $theme, '
        'power_trigger: $power_trigger, '
        'usb_select: $usb_select, '
        'balance: $balance, '
        'audio_bt_enable: $audio_bt_enable, '
        'aptx_enable: $aptx_enable, '
        'remote_enable: $remote_enable, '
        'multifunction_key: $multifunction_key, '
        'usb_dsd_enable: $usb_dsd_enable, '
        'usb_mode: $usb_mode, '
        'iis_phase: $iis_phase, '
        'iis_channel: $iis_channel, '
        'screen_brightness: $screen_brightness, '
        'language: $language, '
        'sampling: $sampling, '
        'vu_meter_level: $vu_meter_level, '
        'vu_meter_display_mode: $vu_meter_display_mode, '
        'input_options: $input_options, '
        'output_options: $output_options, '
        'usb_port_select: $usb_port_select, '
        'dsd_mute: $dsd_mute, '
        'output_level: $output_level, '
        'volume_step: $volume_step, '
        'polarity: $polarity, '
        'peq_enable: $peq_enable, '
        'peq_preset: $peq_preset, '
        'dsd_direct_enable: $dsd_direct_enable, '
        'volume_memory_mode: $volume_memory_mode, '
        'peq_memory_mode: $peq_memory_mode, '
        'main_key_function: $main_key_function, '
        'remote_a_key_function: $remote_a_key_function, '
        'remote_b_key_function: $remote_b_key_function, '
        'device_name: "$deviceNameStr"'
        '}';
  }
}
