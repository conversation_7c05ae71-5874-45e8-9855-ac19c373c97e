class Gaia {
  /// <p>表示命令的掩码。</p>
  /// <p>用于从数据包中检索命令的掩码。</p>
  ///
  /// @see #acknowledgmentMask <code>acknowledgmentMask</code> 用于判断命令是否为确认
  static const int commandMask = 0x7FFF;

  /// <p>表示确认的掩码。</p>
  /// <ul>
  ///     <li><code>COMMAND & acknowledgmentMask > 0</code> 用于判断命令是否为确认。</li>
  ///     <li><code>COMMAND | acknowledgmentMask</code> 用于构建命令的确认命令。</li>
  /// </ul>
  ///
  /// @see #commandMask <code>commandMask</code> 用于了解如何检索命令编号
  static const int acknowledgmentMask = 0x8000;

  /// <p>协议为 "none" 供应商定义的默认值。</p>
  static const int vendorNone = 0x7FFE;

  /// <p>协议为 Qualcomm 供应商定义的供应商默认值。</p>
  static const int vendorQualcomm = 0x000A;

  // -------------------------------------------------------------------
  // |                  配置命令 0x01nn                  |
  // -------------------------------------------------------------------
  /// <p>用于判断命令是否为配置命令的掩码。</p>
  static const int commandsConfigurationMask = 0x0100;

  /// @deprecated
  static const int commandSetRawConfiguration = 0x0100;

  /// <p>检索配置集的版本。</p>
  static const int commandGetConfigurationVersion = 0x0180;

  /// <p>配置 LED 指示灯。确定在给定状态和事件中显示的模式，并配置在事件发生时应用的过滤器。</p>
  static const int commandSetLedConfiguration = 0x0101;

  /// <p>检索当前的 LED 配置。</p>
  static const int commandGetLedConfiguration = 0x0181;

  /// <p>配置设备上的提示音。</p>
  static const int commandSetToneConfiguration = 0x0102;

  /// <p>检索当前配置的提示音配置。</p>
  static const int commandGetToneConfiguration = 0x0182;

  /// <p>设置提示音和音频的默认音量。</p>
  static const int commandSetDefaultVolume = 0x0103;

  /// <p>请求提示音和音频的默认音量设置。</p>
  static const int commandGetDefaultVolume = 0x0183;

  /// <p>重置所有覆盖出厂默认设置的设置（删除 PS 键）。</p>
  static const int commandFactoryDefaultReset = 0x0104;

  /// @deprecated
  static const int commandGetConfigurationId = 0x0184;

  /// <p>配置每个事件的振动器模式。</p>
  static const int commandSetVibratorConfiguration = 0x0105;

  /// <p>检索当前配置的振动器配置。</p>
  static const int commandGetVibratorConfiguration = 0x0185;

  /// <p>配置语音提示以选择不同的语言、语音等。</p>
  static const int commandSetVoicePromptConfiguration = 0x0106;

  /// <p>检索当前配置的语音提示配置。</p>
  static const int commandGetVoicePromptConfiguration = 0x0186;

  /// <p>配置设备功能。功能标识符取决于应用程序，并将在应用程序文档中说明。</p>
  static const int commandSetFeatureConfiguration = 0x0107;

  /// <p>检索设备功能的设置。</p>
  static const int commandGetFeatureConfiguration = 0x0187;

  /// <p>设置用户事件配置。</p>
  static const int commandSetUserEventConfiguration = 0x0108;

  /// <p>获取用户事件配置。</p>
  static const int commandGetUserEventConfiguration = 0x0188;

  /// <p>配置设备上的各种计时器。此命令有长格式（其中有效负载包含每个计时器的值）和短格式（其中有效负载包含计时器编号和该计时器的值）。</p>
  static const int commandSetTimerConfiguration = 0x0109;

  /// <p>检索设备上各种计时器的配置。此命令有长格式（其中响应包含每个计时器的值）和短格式（其中命令有效负载包含计时器编号，响应包含该计时器的编号和值）。</p>
  static const int commandGetTimerConfiguration = 0x0189;

  /// <p>为 16 个音量级别中的每一个配置设备音量控制。</p>
  static const int commandSetAudioGainConfiguration = 0x010A;

  /// <p>请求 16 个音量级别中每一个的设备音量控制配置。</p>
  static const int commandGetAudioGainConfiguration = 0x018A;

  /// <p>设置音量配置。</p>
  static const int commandSetVolumeConfiguration = 0x010B;

  /// <p>获取音量配置。</p>
  static const int commandGetVolumeConfiguration = 0x018B;

  /// <p>设置电源配置。</p>
  static const int commandSetPowerConfiguration = 0x010C;

  /// <p>获取电源配置。</p>
  static const int commandGetPowerConfiguration = 0x018C;

  /// <p>设置用户提示音配置。</p>
  static const int commandSetUserToneConfiguration = 0x010E;

  /// <p>获取用户提示音配置。</p>
  static const int commandGetUserToneConfiguration = 0x018E;

  /// <p>设置设备名称。</p>
  static const int commandSetDeviceName = 0x010F;

  /// <p>获取设备名称。</p>
  static const int commandGetDeviceName = 0x018F;

  /// <p>设置访问 Wi-Fi 接入点的凭据。</p>
  static const int commandSetWlanCredentials = 0x0110;

  /// <p>检索访问 Wi-Fi 接入点的凭据。</p>
  static const int commandGetWlanCredentials = 0x0190;

  /// <p>设置对等设备允许的路由。</p>
  static const int commandSetPeerPermittedRouting = 0x0111;

  /// <p>获取对等设备允许的路由。</p>
  static const int commandGetPeerPermittedRouting = 0x0191;

  /// <p>设置允许的下一个音频源。</p>
  static const int commandSetPermittedNextAudioSource = 0x0112;

  /// <p>获取允许的下一个音频源。</p>
  static const int commandGetPermittedNextAudioSource = 0x0192;

  /// <p>设置在使用一键拨号功能时要发送到 AG（音频网关）进行拨号的字符串。</p>
  static const int commandSetOneTouchDialString = 0x0116;

  /// <p>返回在使用一键拨号功能时要发送到 AG 进行拨号的字符串。</p>
  static const int commandGetOneTouchDialString = 0x0196;

  /// <p>获取已挂载的分区。</p>
  static const int commandGetMountedPartitions = 0x01A0;

  /// <p>配置用于 DFU（设备固件升级）操作的 SQIF 分区。</p>
  static const int commandSetDfuPartition = 0x0121;

  /// <p>检索已配置的 DFU 分区的索引和大小。</p>
  static const int commandGetDfuPartition = 0x01A1;

  // --------------------------------------------------------------
  // |                  控制命令 0x02nn                  |
  // --------------------------------------------------------------
  /// <p>用于判断命令是否为配置命令的掩码。</p>
  static const int commandsControlsMask = 0x0200;

  /// <p>主机可以使用此命令提高/降低当前音量或静音/取消静音音频。</p>
  static const int commandChangeVolume = 0x0201;

  /// <p>主机可以使用此命令使设备执行热复位。设备将发送确认，然后执行热复位。</p>
  static const int commandDeviceReset = 0x0202;

  /// <p>请求设备的当前启动模式。</p>
  static const int commandGetBootMode = 0x0282;

  /// <p>设置设备 PIO 引脚的状态。</p>
  static const int commandSetPioControl = 0x0203;

  /// <p>获取设备 PIO 的状态。</p>
  static const int commandGetPioControl = 0x0283;

  /// <p>主机可以通过发送 <code>setPowerState</code> 命令请求设备物理上开机或关机。设备将响应主机的请求发送确认，如果接受，设备也应物理上开机/关机。</p>
  static const int commandSetPowerState = 0x0204;

  /// <p>主机可以请求检索设备的当前电源状态。设备将发送确认，如果成功，还应指示其当前电源状态。</p>
  static const int commandGetPowerState = 0x0284;

  /// <p>设置设备上音量控制按钮的方向。</p>
  static const int commandSetVolumeOrientation = 0x0205;

  /// <p>请求设备上音量控制按钮的当前方向。</p>
  static const int commandGetVolumeOrientation = 0x0285;

  /// <p>启用或禁用耳机中的振动器（如果存在）。</p>
  static const int commandSetVibratorControl = 0x0206;

  /// <p>请求振动器的当前设置。</p>
  static const int commandGetVibratorControl = 0x0286;

  /// <p>启用或禁用耳机上的 LED（或等效指示灯）。</p>
  static const int commandSetLedControl = 0x0207;

  /// <p>确定 LED 指示灯是否已启用。</p>
  static const int commandGetLedControl = 0x0287;

  /// <p>从耳机发送以控制手机上的 FM 接收器，或从手机发送以控制耳机中的接收器。</p>
  static const int commandFmControl = 0x0208;

  /// <p>播放提示音。</p>
  static const int commandPlayTone = 0x0209;

  /// <p>启用或禁用耳机上的语音提示。</p>
  static const int commandSetVoicePromptControl = 0x020A;

  /// <p>确定语音提示是否已启用。</p>
  static const int commandGetVoicePromptControl = 0x028A;

  /// <p>为文本转语音功能选择下一个可用语言。</p>
  static const int commandChangeAudioPromptLanguage = 0x020B;

  /// <p>启用或禁用耳机上的简单语音识别。</p>
  static const int commandSetSpeechRecognitionControl = 0x020C;

  /// <p>确定语音识别是否已启用。</p>
  static const int commandGetSpeechRecognitionControl = 0x028C;

  /// <p>LED 警报。</p>
  static const int commandAlertLeds = 0x020D;

  /// <p>提示音警报。</p>
  static const int commandAlertTone = 0x020E;

  /// <p>通过 LED 模式、提示音或振动提醒设备用户。每种警报的方法和含义取决于应用程序，并使用适当的 LED、提示音或振动器事件配置进行配置。</p>
  static const int commandAlertEvent = 0x0210;

  /// <p>语音警报。</p>
  static const int commandAlertVoice = 0x0211;

  /// <p>设置音频提示语言。</p>
  static const int commandSetAudioPromptLanguage = 0x0212;

  /// <p>获取音频提示语言。</p>
  static const int commandGetAudioPromptLanguage = 0x0292;

  /// <p>启动设备上的简单语音识别引擎。成功的确认表示语音识别已启动；实际的语音识别结果稍后将通过 <code>speechRecognition</code> 通知进行中继。</p>
  static const int commandStartSpeechRecognition = 0x0213;

  /// <p>选择一个音频均衡器预设。</p>
  static const int commandSetEqControl = 0x0214;

  /// <p>获取当前选择的音频均衡器预设。</p>
  static const int commandGetEqControl = 0x0294;

  /// <p>启用或禁用耳机上的低音增强。</p>
  static const int commandSetBassBoostControl = 0x0215;

  /// <p>确定低音增强是否已启用。</p>
  static const int commandGetBassBoostControl = 0x0295;

  /// <p>启用或禁用耳机上的 3D 声音增强。</p>
  static const int commandSet3dEnhancementControl = 0x0216;

  /// <p>确定 3D 增强是否已启用。</p>
  static const int commandGet3dEnhancementControl = 0x0296;

  /// 切换到下一个可用的均衡器预设。如果在选择了最后一个可用预设时发出此命令，则切换到第一个预设。</p>
  static const int commandSwitchEqControl = 0x0217;

  /// <p>如果低音增强效果已关闭，则将其打开；如果已打开，则将其关闭。</p>
  static const int commandToggleBassBoostControl = 0x0218;

  /// <p>如果 3D 增强效果已关闭，则将其打开；如果已打开，则将其关闭。</p>
  static const int commandToggle3dEnhancementControl = 0x0219;

  /// <p>设置参数均衡器的一个参数，并可选地重新计算滤波器系数。</p>
  static const int commandSetEqParameter = 0x021A;

  /// <p>获取参数均衡器的一个参数。</p>
  static const int commandGetEqParameter = 0x029A;

  /// <p>设置参数均衡器的一组参数。</p>
  static const int commandSetEqGroupParameter = 0x021B;

  /// <p>获取参数均衡器的一组参数。</p>
  static const int commandGetEqGroupParameter = 0x029B;

  /// <p>显示控制。</p>
  static const int commandDisplayControl = 0x021C;

  /// <p>将蓝牙设备置于配对模式，使其可被发现和连接。</p>
  static const int commandEnterBluetoothPairingMode = 0x021D;

  /// <p>设置设备音频源。</p>
  static const int commandSetAudioSource = 0x021E;

  /// <p>获取当前选择的音频源。</p>
  static const int commandGetAudioSource = 0x029E;

  /// <p>向设备发送 AVRC 命令。</p>
  static const int commandAvRemoteControl = 0x021F;

  /// <p>启用或禁用设备上用户配置的参数均衡器（与设置 EQ 控制比较）。</p>
  static const int commandSetUserEqControl = 0x0220;

  /// <p>确定用户 EQ 是否已启用。</p>
  static const int commandGetUserEqControl = 0x02A0;

  /// <p>如果用户 EQ 已关闭，则将其打开；如果已打开，则将其关闭。</p>
  static const int commandToggleUserEqControl = 0x0221;

  /// <p>启用或禁用设备上的扬声器均衡器。</p>
  static const int commandSetSpeakerEqControl = 0x0222;

  /// <p>确定扬声器 EQ 是否已启用。</p>
  static const int commandGetSpeakerEqControl = 0x02A2;

  /// <p>如果扬声器 EQ 已关闭，则将其打开；如果已打开，则将其关闭。</p>
  static const int commandToggleSpeakerEqControl = 0x0223;

  /// <p>控制真无线立体声 (TWS) 声道的路由。</p>
  static const int commandSetTwsAudioRouting = 0x0224;

  /// <p>返回真无线立体声 (TWS) 声道的当前路由。</p>
  static const int commandGetTwsAudioRouting = 0x02A4;

  /// <p>控制真无线立体声 (TWS) 输出的音量。</p>
  static const int commandSetTwsVolume = 0x0225;

  /// <p>返回真无线立体声 (TWS) 的当前音量设置。</p>
  static const int commandGetTwsVolume = 0x02A5;

  /// <p>微调真无线立体声 (TWS) 输出的音量。</p>
  static const int commandTrimTwsVolume = 0x0226;

  /// <p>启用或禁用为对等设备保留一个链路。</p>
  static const int commandSetPeerLinkReserved = 0x0227;

  /// <p>确定是否为对等设备保留了一个链路。</p>
  static const int commandGetPeerLinkReserved = 0x02A7;

  /// <p>请求真无线立体声 (TWS) 会话中的对等设备开始广播。如果未指定目标地址，则命令有效负载长度为 1；如果指定了类型化的蓝牙设备地址，则为 8。</p>
  static const int commandTwsPeerStartAdvertising = 0x022A;

  /// <p>请求设备向连接到它的 HID 遥控器发送“查找我”请求。</p>
  static const int commandFindMyRemote = 0x022B;

  /// <p>设置编解码器。</p>
  static const int commandSetCodec = 0x0240;

  /// <p>获取编解码器。</p>
  static const int commandGetCodec = 0x02C0;

  /// <p>主机用于设置通知事件所支持功能的命令。每个功能对应一个掩码，如下所示：</p>
  /// <ul>
  ///     <li>0x0001: 时间</li>
  ///     <li>0x0002: 未接来电</li>
  ///     <li>0x0004: 短信</li>
  ///     <li>0x0008: 来电</li>
  /// </ul>
  static const int commandSetSupportedFeatures = 0x022C;

  /// <p>用于通知设备主机将断开与设备的 GAIA 连接的命令。</p>
  static const int commandDisconnect = 0x022D;

  /// <p>设置数据端点使用的传输模式。已知的传输模式有：</p>
  /// <ul>
  ///     <li>0x00: 禁用数据端点。</li>
  ///     <li>0x01: RWCP 传输模式 - 可靠写命令协议。</li>
  /// </ul>
  static const int commandSetDataEndpointMode = 0x022E;

  /// <p>获取当前为数据端点设置的传输模式。已知的传输模式有：</p>
  /// <ul>
  ///     <li>0x00: 禁用数据端点。</li>
  ///     <li>0x01: RWCP 传输模式 - 可靠写命令协议。</li>
  /// </ul>
  static const int commandGetDataEndpointMode = 0x02AE;

  // -------------------------------------------------------------------
  // |                  轮询状态命令 0x03nn                  |
  // -------------------------------------------------------------------
  /// <p>用于判断命令是否为轮询状态命令的掩码。</p>
  static const int commandsPolledStatusMask = 0x0300;

  /// <p>从设备获取 Gaia 协议和 API 版本号。</p>
  static const int commandGetApiVersion = 0x0300;

  /// <p>从设备获取蓝牙链路的当前 RSSI 值。RSSI 以 dBm 为单位，使用 2 的补码表示，例如 <code>-20 = 0xEC</code>。</p>
  static const int commandGetCurrentRssi = 0x0301;

  /// <p>从设备获取当前电池电量。电池电量以 mV 为单位，存储为 <code>uint16</code>，例如 <code>3,300mV = 0x0CE4</code>。</p>
  static const int commandGetCurrentBatteryLevel = 0x0302;

  /// <p>请求 BlueCore 硬件、设计和模块标识。</p>
  static const int commandGetModuleId = 0x0303;

  /// <p>请求应用程序软件标识自身。确认有效负载包含八个字节的应用程序标识，可选地后跟以 null 结尾的人类可读文本。标识信息取决于应用程序；耳机从蓝牙设备 ID 复制字段。</p>
  static const int commandGetApplicationVersion = 0x0304;

  /// <p>请求芯片 PIO 的逻辑状态。</p>
  static const int commandGetPioState = 0x0306;

  /// <p>请求由给定模数转换器读取的值。</p>
  static const int commandReadAdc = 0x0307;

  /// <p>请求对等设备的蓝牙设备地址。</p>
  static const int commandGetPeerAddress = 0x030A;

  /// @deprecated
  ///          使用 commandDfuGetResult。
  ///
  /// @see #commandDfuGetResult
  static const int commandGetDfuStatus = 0x0310;

  /// <p>请求主机提供某些信息的状态。这里指的是系统通知类信息，例如收到的短信、未接来电信息等。</p>
  static const int commandGetHostFeatureInformation = 0x0320;

  // ---------------------------------------------------------------------
  // |                  功能控制命令 0x05nn                  |
  // ---------------------------------------------------------------------
  /// <p>用于判断命令是否为轮询状态命令的掩码。</p>
  static const int commandsFeatureControlMask = 0x0500;

  /// <p>获取认证位图。</p>
  static const int commandGetAuthBitmaps = 0x0580;

  /// <p>发起 Gaia 认证交换。</p>
  static const int commandAuthenticateRequest = 0x0501;

  /// <p>提供认证凭据。</p>
  static const int commandAuthenticateResponse = 0x0502;

  /// <p>主机可以使用此命令启用或禁用其已通过认证可使用的功能。</p>
  static const int commandSetFeature = 0x0503;

  /// <p>主机可以使用此命令请求功能的状态。</p>
  static const int commandGetFeature = 0x0583;

  /// <p>主机使用此命令启用与默认未启用会话的设备的 GAIA 会话。</p>
  static const int commandSetSessionEnable = 0x0504;

  /// <p>检索会话启用状态。</p>
  static const int commandGetSessionEnable = 0x0584;

  // -------------------------------------------------------------------
  // |                  数据传输命令 0x06nn                  |
  // -------------------------------------------------------------------
  /// <p>用于判断命令是否为数据传输命令的掩码。</p>
  static const int commandsDataTransferMask = 0x0600;

  /// <p>初始化数据传输会话。</p>
  static const int commandDataTransferSetup = 0x0601;

  /// <p>主机使用此命令指示数据传输会话的关闭，并在数据包有效负载中提供会话 ID。此时设备可以释放维持数据传输会话所需的任何资源，因为主机在发送更多数据之前必须执行另一次数据传输设置。</p>
  static const int commandDataTransferClose = 0x0602;

  /// <p>主机可以使用此命令将数据传输到设备。</p>
  static const int commandHostToDeviceData = 0x0603;

  /// <p>设备可以使用此命令将数据传输到主机。</p>
  static const int commandDeviceToHostData = 0x0604;

  /// <p>发起 I2C 传输（写入和/或读取）。</p>
  static const int commandI2cTransfer = 0x0608;

  /// <p>检索存储分区上的信息。</p>
  static const int commandGetStoragePartitionStatus = 0x0610;

  /// <p>准备设备存储分区以供主机访问。</p>
  static const int commandOpenStoragePartition = 0x0611;

  /// <p>准备 UART 以供主机访问。</p>
  static const int commandOpenUart = 0x0612;

  /// <p>将原始数据写入打开的存储分区。</p>
  static const int commandWriteStoragePartition = 0x0615;

  /// <p>将数据写入打开的流。</p>
  static const int commandWriteStream = 0x0617;

  /// <p>关闭存储分区。</p>
  static const int commandCloseStoragePartition = 0x0618;

  /// <p>挂载设备存储分区以供设备访问。</p>
  static const int commandMountStoragePartition = 0x061A;

  /// <p>获取文件状态。</p>
  static const int commandGetFileStatus = 0x0620;

  /// <p>准备文件以供主机访问。</p>
  static const int commandOpenFile = 0x0621;

  /// <p>从打开的文件读取数据。</p>
  static const int commandReadFile = 0x0624;

  /// <p>关闭文件。</p>
  static const int commandCloseFile = 0x0628;

  /// <p>向主机指示设备希望接收设备固件升级 (DFU) 映像。</p>
  static const int commandDfuRequest = 0x0630;

  /// <p>准备设备以接收设备固件升级 (DFU) 映像。有效负载将为 8 或 136 个字节，具体取决于消息摘要类型。</p>
  static const int commandDfuBegin = 0x0631;

  /// <p>DFU 写入。</p>
  static const int commandDfuWrite = 0x0632;

  /// <p>命令设备安装 DFU 映像并重新启动。</p>
  static const int commandDfuCommit = 0x0633;

  /// <p>请求上次完成的 DFU 操作的状态。</p>
  static const int commandDfuGetResult = 0x0634;

  /// <p>通过 GAIA 开始 VM 升级会话，允许使用 VM 升级控制和 VM 升级数据命令发送 VM 升级协议数据包。</p>
  static const int commandVmUpgradeConnect = 0x0640;

  /// <p>结束通过 GAIA 进行的 VM 升级会话。</p>R
  static const int commandVmUpgradeDisconnect = 0x0641;

  /// <p>隧道传输 VM 升级协议数据包。</p>
  static const int commandVmUpgradeControl = 0x0642;

  /// <p>引入 VM 升级协议数据。</p>
  static const int commandVmUpgradeData = 0x0643;

  // ---------------------------------------------------------------
  // |                  调试命令 0x07nn                  |
  // ---------------------------------------------------------------
  /// <p>用于判断命令是否为轮询状态命令的掩码。</p>
  static const int commandsDebuggingMask = 0x0700;

  /// <p>请求设备不执行任何操作；用于确认 Gaia 协议处理程序处于活动状态。</p>
  static const int commandNoOperation = 0x0700;

  /// <p>请求设备调试标志的值。</p>
  static const int commandGetDebugFlags = 0x0701;

  /// <p>设置设备调试标志的值。</p>
  static const int commandSetDebugFlags = 0x0702;

  /// <p>检索指定 PS 键的值。</p>
  static const int commandRetrievePsKey = 0x0710;

  /// <p>检索指定 PS 键的值。</p>
  static const int commandRetrieveFullPsKey = 0x0711;

  /// <p>设置指定 PS 键的值。</p>
  static const int commandStorePsKey = 0x0712;

  /// <p>填充存储区以强制在下次启动时进行碎片整理。</p>
  static const int commandFloodPs = 0x0713;

  /// <p>设置指定 PS 键的值。</p>
  static const int commandStoreFullPsKey = 0x0714;

  /// <p>导致从 Gaia 库向应用程序任务发送 <code>GAIA_DEBUG_MESSAGE</code>。其解释完全由用户定义。</p>
  static const int commandSendDebugMessage = 0x0720;

  /// <p>向芯片上的应用程序发送任意消息。</p>
  static const int commandSendApplicationMessage = 0x0721;

  /// <p>向 Kalimba DSP 发送任意消息。</p>
  static const int commandSendKalimbaMessage = 0x0722;

  /// <p>检索可用的 malloc() 插槽数量和 PS 键的可用空间。</p>
  static const int commandGetMemorySlots = 0x0730;

  /// <p>检索指定的 16 位调试变量的值。</p>
  static const int commandGetDebugVariable = 0x0740;

  /// <p>设置指定的 16 位调试变量的值。</p>
  static const int commandSetDebugVariable = 0x0741;

  /// <p>从配对设备列表中删除所有已认证的设备以及任何关联的属性数据。</p>
  static const int commandDeletePdl = 0x0750;

  /// <p>发送到 BLE 从设备，使其请求一组新的连接参数。</p>
  static const int commandSetBleConnectionParameters = 0x0752;

  // ------------------------------------------------------------------
  // |                      IVOR 命令 0x10nn                      |
  // ------------------------------------------------------------------
  /// <p>用于判断命令是否为 IVOR 状态命令的掩码。</p>
  static const int commandsIvorMask = 0x1000;

  /// <p>设备用于请求启动语音助手会话。</p>
  static const int commandIvorStart = 0x1000;

  /// <p>主机用于请求设备流式传输语音助手会话的语音请求。</p>
  static const int commandIvorVoiceDataRequest = 0x1001;

  /// <p>设备用于流式传输语音。</p>
  /// <p>警告：此命令不发送确认。</p>
  static const int commandIvorVoiceData = 0x1002;

  /// <p>主机用于指示设备停止流式传输语音。</p>
  static const int commandIvorVoiceEnd = 0x1003;

  /// <p>主机或设备用于取消语音助手会话。</p>
  static const int commandIvorCancel = 0x1004;

  /// <p>设备用于检查主机是否支持其 IVOR 版本。</p>
  static const int commandIvorCheckVersion = 0x1005;

  /// <p>主机用于向设备指示将播放语音助手响应。</p>
  static const int commandIvorAnswerStart = 0x1006;

  /// <p>主机用于向设备指示语音助手响应已播放完毕。</p>
  static const int commandIvorAnswerEnd = 0x1007;

  /// <p></p>
  static const int commandIvorPing = 0x10F0;

  // ------------------------------------------------------------------
  // |                  通知命令 0x40nn                  |
  // ------------------------------------------------------------------
  /// <p>用于判断命令是否为通知命令的掩码。</p>
  static const int commandsNotificationMask = 0x4000;

  /// <p>主机使用 <code>REGISTER_NOTIFICATION</code> 命令注册通知，将下表中的事件类型指定为有效负载的第一个字节，并根据每个事件在后续有效负载字节中定义可选参数。</p>
  static const int commandRegisterNotification = 0x4001;

  /// <p>请求事件类型的当前状态。对于可以注册多个级别的阈值类型事件，响应指示已注册的通知数量。对于只能简单注册或不注册的事件，数量将为 <code>1</code> 或 <code>0</code>。</p>
  static const int commandGetNotification = 0x4081;

  /// <p>主机可以通过发送 <code>CANCEL_NOTIFICATION</code> 命令取消事件通知，有效负载的第一个字节将是要取消的事件类型。</p>
  static const int commandCancelNotification = 0x4002;

  /// <p>假设注册成功，主机将异步接收一个或多个 <code>EVENT_NOTIFICATION</code> 命令（命令 ID <code>0x4003</code>）。事件通知命令有效负载的第一个字节将是事件类型代码，指示通知类型。例如，<code>0x03</code> 表示电池电量低阈值事件通知。事件通知有效负载中的进一步数据取决于通知类型，并在下面按每个通知进行定义。</p>
  static const int commandEventNotification = 0x4003;

  static int notStatus = -1;

  /// <p>请求成功完成。</p>
  static int success = 0;

  /// <p>发送了无效的命令 ID 或设备不支持该命令 ID。</p>
  static int notSupported = 1;

  /// <p>主机未通过认证，无法使用命令 ID 或控制功能类型。</p>
  static int notAuthenticated = 2;

  /// <p>使用的命令 ID 有效，但 GAIA 设备无法成功完成它。</p>
  static int insufficientResources = 3;

  /// <p>GAIA 设备正在认证主机。</p>
  static int authenticating = 4;

  /// <p>发送的参数无效：缺少参数、参数过多、范围错误等。</p>
  static int invalidParameter = 5;

  /// <p>GAIA 设备未处于处理命令的正确状态：需要流式传输音乐、使用特定源等。</p>
  static int incorrectState = 6;

  /// <p>命令正在进行中。</p> <p>在处理耗时操作期间，可能会一次或定期发送状态为 <code>IN_PROGRESS</code> 的确认，以指示操作未停止。</p>
  static int inProgress = 7;

  /// <p>这不是通知 - <code>0x00</code></p>
  static int notNotification = 0;

  /// <p>此事件提供了一种方式，让主机接收设备与主机之间蓝牙链路 RSSI 变化的通知 - <code>0x01</code></p>
  static int rssiLowThreshold = 0x01;

  /// <p>此命令提供了一种方式，让主机接收设备与主机之间蓝牙链路 RSSI 变化的通知 - <code>0x02</code></p>
  static int rssiHighThreshold = 0x02;

  /// <p>此命令提供了一种方式，让主机接收设备电池电量变化的通知 - <code>0x03</code></p>
  static int batteryLowThreshold = 0x03;

  /// <p>此命令提供了一种方式，让主机接收设备电池电量变化的通知 - <code>0x04</code></p>
  static int batteryHighThreshold = 0x04;

  /// <p>主机可以注册以接收设备状态变化的通知 - <code>0x05</code></p>
  static int deviceStateChanged = 0x05;

  /// <p>主机可以注册以接收 PIO 状态变化的通知。主机提供一个 uint32 位图，其中包含它希望接收状态变化通知的 PIO 引脚 - <code>0x06</code></p>
  static int pioChanged = 0x06;

  /// <p>主机可以注册以接收来自设备的调试消息 - <code>0x07</code></p>
  static int debugMessage = 0x07;

  /// <p>主机可以注册以在设备电池充满电时接收通知 - <code>0x08</code></p>
  static int batteryCharged = 0x08;

  /// <p>主机可以注册以在电池充电器连接或断开时接收通知 - <code>0x09</code></p>
  static int chargerConnection = 0x09;

  /// <p>主机可以注册以在电容式触摸传感器的状态发生变化时接收通知。已从 API V1.0 中移除，但听起来很有用 - <code>0x0A</code></p>
  static int capsenseUpdate = 0x0A;

  /// <p>主机可以注册以在发生特定于应用程序的用户操作（例如长按按钮）时接收通知。与 PIO 更改不同。已从 API V1.0 中移除，但听起来很有用 - <code>0x0B</code></p>
  static int userAction = 0x0B;

  /// <p>主机可以注册以在语音识别系统认为听到某些内容时接收通知 - <code>0x0C</code></p>
  static int speechRecognition = 0x0C;

  /// <p>未知 - <code>0x0D</code></p>
  static int avCommand = 0x0D;

  /// <p>未知 - <code>0x0E</code></p>
  static int remoteBatteryLevel = 0x0E;

  /// <p>未知 - <code>0x0F</code></p>
  static int key = 0x0F;

  /// <p>此通知事件指示设备固件升级 (DFU) 操作的进度 - <code>0x10</code>。</p>
  static int dfuState = 0x10;

  /// <p>此通知事件指示 UART 已接收到数据 - <code>0x11</code></p>
  static int uartReceivedData = 0x11;

  /// <p>此通知事件封装了一个 VM 升级协议数据包 - <code>0x12</code></p>
  static int vmuPacket = 0x12;

  /// <p>此通知事件封装了来自主机的所有系统通知，例如来电。</p>
  static int hostNotification = 0x13;

  /// 低功耗蓝牙 (BLE)。
  static int ble = 0;

  /// 经典蓝牙 (BR/EDR)。
  static int brEdr = 1;
}
