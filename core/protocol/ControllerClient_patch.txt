修改 core/protocol/ControllerClient.cpp 文件，添加对CRC32校验失败的错误回调处理：

1. 在文件开头的include部分添加：
```cpp
#include "ControllerClient_extended.h"
#include "ble_error_codes.h"
```

2. 修改mergeCharacteristicPackage方法中CRC32校验失败的处理部分：

将：
```cpp
else
{
    TPPRINTF("[BLE日志] 错误: 数据损坏，CRC32校验失败\n");
}
```

修改为：
```cpp
else
{
    TPPRINTF("[BLE日志] 错误: 数据损坏，CRC32校验失败\n");
    if (mCallback != nullptr)
    {
        // 尝试将回调转换为扩展接口
        ControllerClientCallbackExtended* extendedCallback = 
            dynamic_cast<ControllerClientCallbackExtended*>(mCallback);
        
        if (extendedCallback != nullptr)
        {
            // 调用扩展接口的错误回调
            extendedCallback->onCommunicationError(
                BLE_ERROR_CRC32_FAILED, 
                "数据损坏，CRC32校验失败"
            );
        }
    }
}
```

3. 同样修改其他错误处理部分，例如在数据包太小、魔数无效等情况下也添加错误回调。
