{"output": {"filePath": "compressed-output.txt", "style": "plain", "parsableStyle": false, "fileSummary": true, "directoryStructure": true, "removeComments": true, "removeEmptyLines": true, "compress": true, "topFilesLength": 5, "showLineNumbers": false, "copyToClipboard": false, "git": {"sortByChanges": true}}, "include": ["lib/**/*.dart", "pubspec.*", "android/app/src/**/*.kt", "ios/Runner/**/*.swift", "test/**/*.dart"], "ignore": {"useGitignore": true, "useDefaultPatterns": true, "customPatterns": ["**/build/**", "**/ios/.symlinks/**", "**/Pods/**", "**/*.xcworkspace", "**/.dart_tool/**", "**/.idea/**", "**/*.g.dart", "**/*.freezed.dart", "**/generated_plugins/**"]}, "security": {"enableSecurityCheck": true, "scanDependencies": true}, "tokenCount": {"encoding": "cl100k_base", "perFileAnalysis": true}}