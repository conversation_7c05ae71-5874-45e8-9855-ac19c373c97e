import 'package:get/get.dart';
import '../../device/device_factory.dart';
import '../../model/enums/device_mode_type.dart';
import '../../model/enums/ble_connection_state.dart';
import '../../model/bluetooth/ble_device.dart';
import '../../utils/log_util.dart';

class DeviceSettingsLogic extends GetxController {
  final DeviceFactory _deviceFactory = DeviceFactory();

  // 当前连接的设备
  final Rx<BleDevice?> currentDevice = Rx<BleDevice?>(null);
  final Rx<DeviceModeType> deviceType = DeviceModeType.unknown.obs;

  // === 显示设置 ===
  // 主题设置
  final RxString selectedTheme = 'Aurora'.obs;
  final List<String> themeOptions = [
    'Aurora',
    'Orange',
    'Peru',
    'Pea Green',
    'Dark Khaki',
    'Rose Brown',
    'Blue',
    'Magic Purple',
    'White',
  ];

  // 主页设置
  final RxString selectedHomePage = 'Normal'.obs;
  final List<String> homePageOptions = ['Normal', 'VU', 'FFT'];

  // 亮度设置
  final RxString selectedBrightness = 'Medium'.obs;
  final List<String> brightnessOptions = ['Low', 'Medium', 'High', 'Auto'];

  // VU表设置 (D900特有)
  final RxString selectedVuLevel = '+4dBu'.obs;
  final List<String> vuLevelOptions = ['+4dBu', '+10dBu'];

  final RxString selectedVuDisplay = 'All On'.obs;
  final List<String> vuDisplayOptions = [
    'All On',
    'Normal Page',
    'FFT Page',
    'All Off',
  ];

  // === 输入设置 ===
  // 输入选择
  final RxString selectedInput = 'USB'.obs;
  final RxList<String> enabledInputs = <String>[].obs;

  // 通用输入选项
  final List<String> commonInputOptions = [
    'USB',
    'Optical',
    'Coaxial',
    'Bluetooth',
  ];

  // D900特有输入选项
  final List<String> d900InputOptions = [
    'USB',
    'Optical1',
    'Optical2',
    'Coaxial1',
    'Coaxial2',
    'AES',
    'IIS',
    'Bluetooth',
  ];

  // USB设置
  final RxString selectedUsbClass = '2.0'.obs;
  final List<String> usbClassOptions = ['2.0', '1.0'];

  // D900特有USB设置
  final RxString selectedUsbPort = 'Auto'.obs;
  final List<String> usbPortOptions = ['Type C', 'Type B', 'Auto'];

  // 蓝牙设置
  final RxBool bluetoothEnabled = true.obs;
  final RxBool bluetoothAptxEnabled = true.obs;

  // D900特有IIS设置
  final RxString selectedIisPhase = 'Standard'.obs;
  final List<String> iisPhaseOptions = ['Standard', 'Reverse'];

  final RxString selectedIisDsdChannel = 'Standard'.obs;
  final List<String> iisDsdChannelOptions = ['Standard', 'Swap'];

  // D900特有DSD MUTE设置
  final RxString selectedDsdMute = 'High Active'.obs;
  final List<String> dsdMuteOptions = ['High Active', 'Low Active', 'Off'];

  // === 输出设置 ===
  // 输出选择
  final RxString selectedOutput = 'All'.obs;
  final RxList<String> enabledOutputs = <String>[].obs;

  // 通用输出选项
  final List<String> commonOutputOptions = ['All', 'Line', 'Preamp'];

  // DX5II特有输出选项
  final List<String> dx5iiOutputOptions = [
    'All',
    'Headphone All',
    'Line All',
    'Headphone SE',
    'Headphone Balanced',
    'Line SE',
    'Line Balanced',
  ];

  // 音量和平衡
  final RxInt volume = 0.obs;
  final RxBool muted = false.obs;
  final RxDouble balance = 0.0.obs; // -9.5 到 ****

  // 音量步进
  final RxString selectedVolumeStep = '0.5dB'.obs;
  final List<String> volumeStepOptions = ['0.5dB', '1dB'];

  // 极性设置
  final RxString selectedPolarity = 'Standard'.obs;
  final List<String> polarityOptions = ['Standard', 'Inverted'];

  // DX5II特有设置
  final RxString selectedPcmFilter = 'F-1'.obs;
  final List<String> pcmFilterOptions = [
    'F-1',
    'F-2',
    'F-3',
    'F-4',
    'F-5',
    'F-6',
    'F-7',
    'F-8',
  ];

  final RxString selectedHeadphoneGain = 'Low'.obs;
  final List<String> headphoneGainOptions = ['Low', 'High'];

  final RxString selectedLineOutMode = 'Preamp'.obs;
  final List<String> lineOutModeOptions = ['Preamp', 'DAC'];

  // D900特有设置
  final RxString selectedOutputLevel = '5V'.obs;
  final List<String> outputLevelOptions = ['5V', '4V'];

  // === PEQ配置 ===
  final RxBool peqEnabled = false.obs;
  final RxString selectedPeqPreset = 'Airy'.obs;
  final List<String> peqPresetOptions = [
    'Bass1',
    'Bass2',
    'Airy',
    'Warm',
    'Dynamic',
  ];

  // === 高级设置 ===
  // 电源触发
  final RxString selectedPowerTrigger = 'Signal'.obs;
  final List<String> powerTriggerOptions = ['Signal', '12V', 'Off'];

  // DSD直通 (D900特有)
  final RxBool dsdDirectEnabled = true.obs;

  // 记忆方式
  final RxString selectedVolumeMemory = 'Follow Output'.obs;
  final RxString selectedPeqMemory = 'Follow Output'.obs;
  final List<String> memoryOptions = ['Follow Input', 'Follow Output', 'None'];

  // 遥控器
  final RxBool remoteEnabled = true.obs;

  // 按键自定义
  final RxString selectedMainKeyFunction = 'Output Select'.obs;
  final RxString selectedRemoteAKeyFunction = 'Output Select'.obs;
  final RxString selectedRemoteBKeyFunction = 'Output Select'.obs;

  final List<String> keyFunctionOptions = [
    'Input Select',
    'Output Select',
    'Home Page Select',
    'Brightness Select',
    'Screen Off',
    'Mute',
    'EQ Select',
    'Power Trigger Select',
  ];

  // DX5II特有按键功能
  final List<String> dx5iiKeyFunctionOptions = [
    'Input Select',
    'Output Select',
    'Home Page Select',
    'Brightness Select',
    'Screen Off',
    'Mute',
    'EQ Select',
    'Power Trigger Select',
    'PCM Filter Select',
    'Headphone Gain',
  ];

  // === 系统设置 ===
  final RxString selectedLanguage = 'Chinese'.obs;
  final List<String> languageOptions = ['Chinese', 'English'];

  @override
  void onInit() {
    super.onInit();
    _loadCurrentDevice();
    _setupDeviceListeners();
  }

  void _loadCurrentDevice() {
    currentDevice.value = _deviceFactory.getConnectedDevice();
    if (currentDevice.value != null) {
      deviceType.value = currentDevice.value!.deviceType;
      _requestDeviceSettings();
    }
  }

  void _setupDeviceListeners() {
    // 监听设备连接状态
    _deviceFactory.connectionState.listen((event) {
      if (event.state == BleConnectionState.connected ||
          event.state == BleConnectionState.connectedUnsafe) {
        _loadCurrentDevice();
      } else {
        currentDevice.value = null;
        deviceType.value = DeviceModeType.unknown;
      }
    });

    // 监听设备设置变化
    final deviceManager = _deviceFactory.currentDeviceManager;
    if (deviceManager != null) {
      _setupDeviceSettingsListeners(deviceManager);
    }
  }

  void _setupDeviceSettingsListeners(DeviceManager deviceManager) {
    // 监听各种设备状态变化
    deviceManager.volume.listen((value) => volume.value = value);
    deviceManager.mute.listen((value) => muted.value = value);
    deviceManager.inputType.listen((value) => _updateInputFromValue(value));
    deviceManager.outputType.listen((value) => _updateOutputFromValue(value));
    deviceManager.theme.listen((value) => _updateThemeFromValue(value));
    deviceManager.displayMode.listen(
      (value) => _updateDisplayModeFromValue(value),
    );
    deviceManager.screenBrightness.listen(
      (value) => _updateBrightnessFromValue(value),
    );
    deviceManager.balance.listen((value) => balance.value = value.toDouble());
    deviceManager.powerTrigger.listen(
      (value) => _updatePowerTriggerFromValue(value),
    );
    deviceManager.audioBluetooth.listen(
      (value) => bluetoothEnabled.value = value,
    );
    deviceManager.bluetoothAptx.listen(
      (value) => bluetoothAptxEnabled.value = value,
    );
    deviceManager.language.listen((value) => _updateLanguageFromValue(value));
  }

  void _requestDeviceSettings() {
    final deviceManager = _deviceFactory.currentDeviceManager;
    deviceManager?.requestSettings();
  }

  // === 设置更新方法 ===

  // 显示设置
  void onThemeChanged(String? newTheme) {
    if (newTheme != null && newTheme != selectedTheme.value) {
      selectedTheme.value = newTheme;
      final themeIndex = themeOptions.indexOf(newTheme);
      _deviceFactory.currentDeviceManager?.setTheme(themeIndex);
      Log.i("主题已更改为: $newTheme (索引: $themeIndex)");
    }
  }

  void onHomePageChanged(String? newHomePage) {
    if (newHomePage != null && newHomePage != selectedHomePage.value) {
      selectedHomePage.value = newHomePage;
      final homePageIndex = homePageOptions.indexOf(newHomePage);
      _deviceFactory.currentDeviceManager?.setDisplayMode(homePageIndex);
      Log.i("主页已更改为: $newHomePage (索引: $homePageIndex)");
    }
  }

  void onBrightnessChanged(String? newBrightness) {
    if (newBrightness != null && newBrightness != selectedBrightness.value) {
      selectedBrightness.value = newBrightness;
      final brightnessIndex = brightnessOptions.indexOf(newBrightness);
      _deviceFactory.currentDeviceManager?.setScreenBrightness(brightnessIndex);
      Log.i("亮度已更改为: $newBrightness (索引: $brightnessIndex)");
    }
  }

  // 输入设置
  void onInputChanged(String? newInput) {
    if (newInput != null && newInput != selectedInput.value) {
      selectedInput.value = newInput;
      final inputOptions = _getInputOptions();
      final inputIndex = inputOptions.indexOf(newInput);
      _deviceFactory.currentDeviceManager?.setInputType(inputIndex);
      Log.i("输入已更改为: $newInput (索引: $inputIndex)");
    }
  }

  void onUsbClassChanged(String? newUsbClass) {
    if (newUsbClass != null && newUsbClass != selectedUsbClass.value) {
      selectedUsbClass.value = newUsbClass;
      final usbClassIndex = usbClassOptions.indexOf(newUsbClass);
      _deviceFactory.currentDeviceManager?.setUsbMode(usbClassIndex);
      Log.i("USB类已更改为: $newUsbClass (索引: $usbClassIndex)");
    }
  }

  void onBluetoothChanged(bool enabled) {
    if (enabled != bluetoothEnabled.value) {
      bluetoothEnabled.value = enabled;
      _deviceFactory.currentDeviceManager?.enableAudioBluetooth(enabled);
      Log.i("蓝牙已${enabled ? '启用' : '禁用'}");
    }
  }

  void onBluetoothAptxChanged(bool enabled) {
    if (enabled != bluetoothAptxEnabled.value) {
      bluetoothAptxEnabled.value = enabled;
      _deviceFactory.currentDeviceManager?.enableBluetoothAptx(enabled);
      Log.i("蓝牙aptX已${enabled ? '启用' : '禁用'}");
    }
  }

  // 输出设置
  void onOutputChanged(String? newOutput) {
    if (newOutput != null && newOutput != selectedOutput.value) {
      selectedOutput.value = newOutput;
      final outputOptions = _getOutputOptions();
      final outputIndex = outputOptions.indexOf(newOutput);
      _deviceFactory.currentDeviceManager?.setOutputType(outputIndex);
      Log.i("输出已更改为: $newOutput (索引: $outputIndex)");
    }
  }

  void onVolumeChanged(int newVolume) {
    if (newVolume != volume.value) {
      volume.value = newVolume;
      _deviceFactory.currentDeviceManager?.setVolume(newVolume);
      Log.i("音量已更改为: $newVolume");
    }
  }

  void onMuteChanged(bool mute) {
    if (mute != muted.value) {
      muted.value = mute;
      _deviceFactory.currentDeviceManager?.setMute(mute);
      Log.i("静音已${mute ? '启用' : '禁用'}");
    }
  }

  void onBalanceChanged(double newBalance) {
    if (newBalance != balance.value) {
      balance.value = newBalance;
      _deviceFactory.currentDeviceManager?.setBalance(newBalance.round());
      Log.i("平衡已更改为: $newBalance");
    }
  }

  // PEQ设置
  void onPeqEnabledChanged(bool enabled) {
    if (enabled != peqEnabled.value) {
      peqEnabled.value = enabled;
      // TODO: 实现PEQ启用/禁用功能
      Log.i("PEQ已${enabled ? '启用' : '禁用'}");
    }
  }

  void onPeqPresetChanged(String? newPreset) {
    if (newPreset != null && newPreset != selectedPeqPreset.value) {
      selectedPeqPreset.value = newPreset;
      final presetIndex = peqPresetOptions.indexOf(newPreset);
      // TODO: 实现PEQ预设选择功能
      Log.i("PEQ预设已更改为: $newPreset (索引: $presetIndex)");
    }
  }

  // 高级设置
  void onPowerTriggerChanged(String? newTrigger) {
    if (newTrigger != null && newTrigger != selectedPowerTrigger.value) {
      selectedPowerTrigger.value = newTrigger;
      final triggerIndex = powerTriggerOptions.indexOf(newTrigger);
      _deviceFactory.currentDeviceManager?.setPowerTrigger(triggerIndex);
      Log.i("电源触发已更改为: $newTrigger (索引: $triggerIndex)");
    }
  }

  void onRemoteEnabledChanged(bool enabled) {
    if (enabled != remoteEnabled.value) {
      remoteEnabled.value = enabled;
      _deviceFactory.currentDeviceManager?.enableRelay(enabled);
      Log.i("遥控器已${enabled ? '启用' : '禁用'}");
    }
  }

  // 系统设置
  void onLanguageChanged(String? newLanguage) {
    if (newLanguage != null && newLanguage != selectedLanguage.value) {
      selectedLanguage.value = newLanguage;
      final languageIndex = languageOptions.indexOf(newLanguage);
      _deviceFactory.currentDeviceManager?.setLanguage(languageIndex);
      Log.i("语言已更改为: $newLanguage (索引: $languageIndex)");
    }
  }

  void onFactoryReset() {
    _deviceFactory.currentDeviceManager?.restoreFactorySettings();
    Log.i("执行恢复出厂设置");
  }

  // === 辅助方法 ===

  List<String> _getInputOptions() {
    return deviceType.value == DeviceModeType.dx9
        ? d900InputOptions
        : commonInputOptions;
  }

  List<String> _getOutputOptions() {
    return deviceType.value == DeviceModeType.dx5
        ? dx5iiOutputOptions
        : commonOutputOptions;
  }

  List<String> _getKeyFunctionOptions() {
    return deviceType.value == DeviceModeType.dx5
        ? dx5iiKeyFunctionOptions
        : keyFunctionOptions;
  }

  bool get isD900 => deviceType.value == DeviceModeType.dx9;
  bool get isDx5ii => deviceType.value == DeviceModeType.dx5;
  bool get hasDevice => currentDevice.value != null;

  // 从设备值更新UI状态的方法
  void _updateInputFromValue(int value) {
    final options = _getInputOptions();
    if (value >= 0 && value < options.length) {
      selectedInput.value = options[value];
    }
  }

  void _updateOutputFromValue(int value) {
    final options = _getOutputOptions();
    if (value >= 0 && value < options.length) {
      selectedOutput.value = options[value];
    }
  }

  void _updateThemeFromValue(int value) {
    if (value >= 0 && value < themeOptions.length) {
      selectedTheme.value = themeOptions[value];
    }
  }

  void _updateDisplayModeFromValue(int value) {
    if (value >= 0 && value < homePageOptions.length) {
      selectedHomePage.value = homePageOptions[value];
    }
  }

  void _updateBrightnessFromValue(int value) {
    if (value >= 0 && value < brightnessOptions.length) {
      selectedBrightness.value = brightnessOptions[value];
    }
  }

  void _updatePowerTriggerFromValue(int value) {
    if (value >= 0 && value < powerTriggerOptions.length) {
      selectedPowerTrigger.value = powerTriggerOptions[value];
    }
  }

  void _updateLanguageFromValue(int value) {
    if (value >= 0 && value < languageOptions.length) {
      selectedLanguage.value = languageOptions[value];
    }
  }
}
