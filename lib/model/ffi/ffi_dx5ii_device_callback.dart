import 'dart:ffi';
import 'package:ffi/ffi.dart';

import 'ffi_dx5ii_scan_result.dart';
import 'ffi_dx5ii_settings.dart';

/// C回调函数结构体
base class FFIDx5iiDeviceCallback extends Struct {
  /// 扫描结果回调
  external Pointer<
    NativeFunction<Void Function(Int64, Pointer<FFIDx5iiScanResult>, Size)>
  >
  on_scan_results;

  /// 扫描失败回调
  external Pointer<NativeFunction<Void Function(Int64, Int32)>> on_scan_failed;

  /// 状态变化回调
  external Pointer<NativeFunction<Void Function(Int64, Int32)>> on_state_change;

  /// 验证结果回调
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_verify_result;

  /// 电源状态变化回调
  external Pointer<NativeFunction<Void Function(Int64, Int32)>> on_power_change;

  /// 设备名称变化回调
  external Pointer<NativeFunction<Void Function(Int64, Pointer<Utf8>)>>
  on_device_name_change;

  /// 音量变化回调
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_volume_change;

  /// 静音状态变化回调
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_mute_change;

  /// 输入类型变化回调
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_input_type_change;

  /// 输出类型变化回调
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_output_type_change;

  /// 耳机启用状态变化回调
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_enable_headphone;

  /// 耳机增益变化回调
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_headphone_gain_change;

  /// 显示模式变化回调
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_display_mode_change;

  /// 主题变化回调
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_theme_change;

  /// 电源触发器变化回调
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_power_trigger_change;

  /// 声道平衡变化回调
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_balance_change;

  /// 滤波器变化回调
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_filter_change;

  /// 解码模式变化回调
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_decode_mode_change;

  /// 蓝牙音频启用状态变化回调
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_enable_audio_bluetooth;

  /// 蓝牙APTX启用状态变化回调
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_enable_bluetooth_aptx;

  /// 继电器启用状态变化回调
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_enable_relay;

  /// 多功能键变化回调
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_multifunction_key_change;

  /// USB模式变化回调
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_usb_mode_change;

  /// 屏幕亮度变化回调
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_screen_brightness_change;

  /// 语言变化回调
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_language_change;

  /// 重置设置回调
  external Pointer<NativeFunction<Void Function(Int64)>>
  on_device_reset_settings;

  /// 恢复出厂设置回调
  external Pointer<NativeFunction<Void Function(Int64)>>
  on_device_restore_factory_settings;

  /// 设备设置响应回调
  external Pointer<
    NativeFunction<Void Function(Int64, Pointer<FFIDx5iiSettings>)>
  >
  on_device_settings_response;

  /// 采样率响应回调
  external Pointer<NativeFunction<Void Function(Int64, Int32)>>
  on_device_sampling_response;
}
