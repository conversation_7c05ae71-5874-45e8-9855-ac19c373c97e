# 命令模式迁移指南

本文档提供了从命令模式迁移到直接调用设备管理器方法的指导。

## 背景

命令模式的实现存在问题，命令被创建但实际上并没有调用底层方法。为了解决这个问题并简化代码库，我们决定完全移除命令模式。

## 迁移步骤

### 1. 从项目中移除的文件

以下文件已从项目中移除：

- `commands.dart` - 命令类定义
- `dx5_command_factory.dart` - DX5命令工厂
- `d900_command_factory.dart` - D900命令工厂

### 2. 接口变更

- 从`DeviceManager`接口中移除了`executeCommand`和`isCommandSupported`方法
- 从`DeviceFactory`类中移除了`getCommandFactoryForCurrentDevice`方法

### 3. 代码更新指南

#### 原有代码（使用命令模式）：

```dart
import 'package:topping_ble_control/device/commands.dart';

// 获取命令工厂
final factory = deviceFactory.getCommandFactoryForCurrentDevice();
if (factory == null) return;

// 创建命令
final command = factory.createSetVolumeCommand(volume: 75);

// 执行命令
deviceManager.executeCommand(command);
```

#### 迁移后代码（直接调用）：

```dart
// 直接调用设备管理器方法
deviceManager.setVolume(75);
```

### 4. 错误处理示例

#### 原有错误处理：

```dart
try {
  // 创建命令
  final command = factory.createSetVolumeCommand(volume: 75);
  
  // 执行命令
  await deviceManager.executeCommand(command).then((_) {
    Log.i("命令已发送");
  }).catchError((e) {
    Log.e("执行命令时出错: $e");
    Get.snackbar('错误', '操作失败: $e');
  });
} catch (e) {
  Log.e("执行命令时出错: $e");
  Get.snackbar('错误', '操作失败: $e');
}
```

#### 迁移后错误处理：

```dart
try {
  // 直接调用设备管理器方法
  await deviceManager.setVolume(75);
  Log.i("操作已完成");
} catch (e) {
  Log.e("执行操作时出错: $e");
  Get.snackbar('错误', '操作失败: $e');
}
```

### 5. 辅助方法推荐

为了统一处理设备操作和错误处理，建议使用辅助方法：

```dart
/// 统一执行设备操作的辅助方法
Future<void> _executeDeviceAction(String actionName, Future<void> Function(DeviceManager) action) async {
  final manager = deviceFactory.currentDeviceManager;
  if (manager == null) {
    Log.w("无法执行 $actionName：设备未连接");
    Get.snackbar('错误', '设备未连接');
    return;
  }

  try {
    Log.i("执行操作: $actionName");
    await action(manager);
    Log.i("操作 $actionName 已完成");
  } catch (e) {
    Log.e("执行操作 $actionName 时出错: $e");
    Get.snackbar('错误', '操作失败: $e');
  }
}

// 使用示例
_executeDeviceAction("设置音量为 75", (manager) => manager.setVolume(75));
```

## 特定设备功能处理

针对特定设备类型的功能，使用设备类型检查来调用相应方法：

```dart
// 检查设备类型
final isD900Device = deviceType == DeviceModeType.dx9;

// 调用特定设备方法
if (isD900Device) {
  _executeDeviceAction("设置USB模式", (manager) => (manager as D900DeviceManager).setUsbType(usbMode));
} else {
  // DX5设备的相应处理
  _executeDeviceAction("设置USB模式", (manager) => manager.setUsbMode(usbMode));
}
```

## 常见问题解答

### Q: 如何处理设备特定功能？
**A:** 对于设备特定功能，先检查设备类型，然后调用相应的方法。在必要时使用类型转换，但要确保进行类型检查。

### Q: 如何处理参数转换？
**A:** 直接在调用前进行必要的参数转换，例如从枚举值到整数的转换：

```dart
// 从枚举转换到整数值
int inputValue = inputEnumValue.index;
_executeDeviceAction("设置输入为 $inputValue", (manager) => manager.setInputType(inputValue));
```

如有任何迁移问题，请联系项目维护人员。 