#ifndef __LIST_H__
#define __LIST_H__
typedef struct list_node_t
{
	struct list_node_t *pre;
	struct list_node_t *next;
} list_node_t;

typedef struct single_list_node_t
{
	struct single_list_node_t *next;
} single_list_node_t;
/*********************************************************************************************************************
* 从前面插入节点
* handle 链表头
* node 新节点
*********************************************************************************************************************/
void insert_to_single_list(single_list_node_t **handle, single_list_node_t *node);
/*********************************************************************************************************************
* 从前面删除节点
* handle 链表头
*********************************************************************************************************************/
void remove_from_single_list(single_list_node_t **handle);
/*********************************************************************************************************************
* 从前面插入节点
* handle 链表头
* node 新节点
*********************************************************************************************************************/
void insert_to_front(list_node_t **handle, list_node_t *node);
/*********************************************************************************************************************
* 从后面插入节点
* handle 链表头
* node 新节点
*********************************************************************************************************************/
void insert_to_back(list_node_t **handle, list_node_t *node);
/*********************************************************************************************************************
* 从中间插入节点
* handle 链表头
* node 新节点
* n 插入位置
*********************************************************************************************************************/
void insert_to_middle(list_node_t **handle, list_node_t *node, int n);
/*********************************************************************************************************************
* 删除一个节点
* handle 链表头
* node 要删除的节点
*********************************************************************************************************************/
void remove_from_list(list_node_t **handle, list_node_t *node);
/*********************************************************************************************************************
* 获取一个最后面的节点
* handle 链表头
* return 获取到的节点
*********************************************************************************************************************/
list_node_t *get_back_from_list(list_node_t **handle);
#endif