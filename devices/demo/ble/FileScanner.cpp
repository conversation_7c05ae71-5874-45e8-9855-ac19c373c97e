#include "FileScanner.h"
#include <stdio.h>
namespace Topping
{
    FileScanner::FileScanner()
    {
    }

    FileScanner::~FileScanner()
    {
    }

    void FileScanner::startScan(ScanCallback *callback)
    {
        FILE *file = ::fopen("/tmp/file_advertise", "rb");
        if (NULL == file)
        {
            return;
        }
        ScanResult scanResult;
        scanResult.rssi = -10;
        std::string name;
        unsigned char buff[64];
        size_t len = ::fread(buff, 1, 64, file);
        size_t i = 0;
        while (i < len - sizeof(int))
        {
            size_t size = buff[i];
            int cmd = buff[i + 1];
            switch (cmd)
            {
            case 0x09:
                for (size_t j = 0; j < size - 1; j++)
                {
                    name += buff[i + 2 + j];
                }
                break;
            case 0xff:
                for (size_t j = 0; j < size - 1; j++)
                {
                    scanResult.manufacturerData.push_back(buff[i + 2 + j]);
                }
                break;
            default:
                break;
            }
            i += size + 1;
        }
        long pid = *((int *)&buff[i]);
        scanResult.device = BluetoothDevice((void *)pid);
        scanResult.device.setName(name);
        std::vector<ScanResult> scanResults;
        scanResults.push_back(scanResult);
        callback->onBatchScanResults(scanResults);
        ::fclose(file);
    }

    void FileScanner::stopScan(ScanCallback *callback)
    {
    }
}