apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'

buildscript {

    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        // The Android Gradle Plugin knows how to build native code with the NDK.
        classpath("com.android.tools.build:gradle:4.2.2")
    }
}

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
        // 添加本地Maven仓库
        maven { url "${rootProject.projectDir}/maven-repo" }
    }
}

android {
    namespace = "com.topping.ble.control"

    compileSdk = 31

    ndkVersion = android.ndkVersion

    externalNativeBuild {
        cmake {
            path = "../src/CMakeLists.txt"
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    defaultConfig {
        minSdk = 21
    }

    kotlinOptions {
        jvmTarget = '1.8'  // 或者 '11'，取决于您的项目需求
    }

    dependencies {
        // 直接引入AAR文件
//        implementation fileTree(dir: 'libs', include: ['*.aar'])
        // 添加androidx注解支持
        implementation 'androidx.annotation:annotation:1.5.0'
    }
}

