#ifndef __DEMO_DEVICE_H__
#define __DEMO_DEVICE_H__
#include "ControllerScanner.h"
#include "ControllerClient.h"
#include <string>
namespace Topping
{
    class DemoDevice : public ControllerScanner::Scan<PERSON>allback, public ControllerClient::Callback
    {
    public:
        enum demoDeviceCmd
        {
            DEMO_DEVICE_CMD_SEND_MSG = 0X100,
        };

    public:
        DemoDevice();
        ~DemoDevice();

        void startScan();
        void stopScan();
        void connect();
        void disconnect();
        void verify();
        void sendMsg(const std::string &msg);

    protected:
        virtual void onBatchScanResults(const std::vector<ControllerScanner::ScanResult> &results) override;
        virtual void onScanFailed(int errorCode) override;

        virtual void onStateChange(int state) override;
        virtual void onVerifyResult(int errCode) override;
        virtual void onReceiveRequest(int session_id, int cmd, const std::string &msg) override;
        virtual void onReceiveResponse(int cmd, int errcode, const std::string &errmsg, const std::string &msg) override;

    private:
        ControllerScanner mControllerScanner;
        ControllerClient mControllerClient;
        BluetoothDevice mDevice;
        const SupportedDevice *mSupportedDevice;
    };
}
#endif