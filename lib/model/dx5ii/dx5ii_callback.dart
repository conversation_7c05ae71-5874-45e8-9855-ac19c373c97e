import 'dart:ffi'; // 需要导入 ffi

import 'package:topping_ble_control/model/base/topping_device_callback.dart';
import 'package:topping_ble_control/utils/log_util.dart';
import '../ffi/ffi_dx5ii_settings.dart';

/// DX5II设备回调类
class Dx5iiCallback extends ToppingDeviceCallback {
  /// 设备设置响应回调 - DX5II特有
  /// 接收指向 FFI 结构体的指针
  final Function(Pointer<FFIDx5iiSettings>) onDeviceSettingsResponse;

  /// 耳机启用状态变化回调 - DX5II特有
  final Function(bool) onHeadphoneEnabledChange;

  /// 耳机增益变化回调 - DX5II特有
  final Function(int) onHeadphoneGainChange;

  /// PCM滤波器变化回调 - DX5II特有
  final Function(int) onPcmFilterChange;

  /// 解码模式变化回调 - DX5II特有
  final Function(int) onDecodeModeChange;

  /// 采样率响应回调 - DX5II特有
  final Function(int) onSamplingResponse;

  /// 构造函数
  Dx5iiCallback({
    required super.onScanResults,
    required super.onScanFailed,
    required super.onStateChange,
    required super.onVerifyResult,
    super.onPowerChange,
    required this.onDeviceSettingsResponse,
    super.onVolumeChange,
    super.onMuteChange,
    super.onInputTypeChange,
    super.onOutputTypeChange,
    this.onHeadphoneEnabledChange = _defaultOnHeadphoneEnabledChange,
    this.onHeadphoneGainChange = _defaultOnHeadphoneGainChange,
    super.onDisplayModeChange,
    super.onThemeChange,
    super.onPowerTriggerChange,
    super.onBalanceChange,
    this.onPcmFilterChange = _defaultOnPcmFilterChange,
    this.onDecodeModeChange = _defaultOnDecodeModeChange,
    super.onAudioBluetoothChange,
    super.onBluetoothAptxChange,
    super.onRemoteEnabledChange,
    super.onMultifunctionKeyChange,
    super.onUsbModeChange,
    super.onScreenBrightnessChange,
    super.onLanguageChange,
    this.onSamplingResponse = _defaultOnSamplingResponse,
  });

  // --- DX5II特有的默认处理函数 ---
  static void _defaultOnHeadphoneEnabledChange(bool enabled) =>
      Log.e("未处理: 耳机启用 $enabled");
  static void _defaultOnHeadphoneGainChange(int gain) =>
      Log.e("未处理: 耳机增益 $gain");
  static void _defaultOnPcmFilterChange(int filter) =>
      Log.e("未处理: PCM滤波器 $filter");
  static void _defaultOnDecodeModeChange(int mode) => Log.e("未处理: 解码模式 $mode");
  static void _defaultOnSamplingResponse(int sampling) =>
      Log.e("未处理: 采样率响应 $sampling");
}
