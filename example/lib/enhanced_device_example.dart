import 'dart:async';

import 'package:flutter/material.dart';
import 'package:topping_ble_control/model/bluetooth/ble_device.dart';
import 'package:topping_ble_control/model/enums/ble_connection_state.dart';
import 'package:topping_ble_control/model/enums/device_mode_type.dart';
import 'package:topping_ble_control/utils/log_util.dart';
import 'package:topping_ble_control/device/device_factory.dart';

class EnhancedDeviceExample extends StatefulWidget {
  const EnhancedDeviceExample({Key? key}) : super(key: key);

  @override
  _EnhancedDeviceExampleState createState() => _EnhancedDeviceExampleState();
}

class _EnhancedDeviceExampleState extends State<EnhancedDeviceExample> {
  // 设备工厂 - 负责所有设备操作
  final _deviceFactory = DeviceFactory();

  // 扫描到的设备列表
  List<BleDevice> _devices = [];

  // 连接状态
  BleConnectionState _connectionState = BleConnectionState.disconnected;

  // 日志
  final List<String> _logs = [];

  // 事件流订阅
  StreamSubscription? _connectionStateSubscription;
  StreamSubscription? _discoverySubscription;

  // 添加一个状态来跟踪静音状态 (仅为示例)
  bool _isMuted = false;

  // 添加扫描状态变量 (由 discoveryState 更新)
  bool _isScanning = false;

  @override
  void initState() {
    super.initState();
    _setupListeners();
    // 可以在这里监听静音状态变化并更新 _isMuted
    _deviceFactory.currentDeviceManager?.mute.listen((isMute) {
      Log.i("静音状态变化: $isMute");
    });

    // 在 initState 或连接成功后也检查一次
    _checkDeviceCapabilities();
  }

  @override
  void dispose() {
    // 取消订阅
    _connectionStateSubscription?.cancel();
    _discoverySubscription?.cancel();

    // 释放设备工厂资源
    _deviceFactory.dispose();

    super.dispose();
  }

  // 设置监听器
  void _setupListeners() {
    // 新增：监听合并后的发现状态流
    _discoverySubscription = _deviceFactory.discoveryState.listen(
      (state) {
        setState(() {
          _devices = state.devices;
          _isScanning = state.isScanning;
        });
        _addLog("发现状态更新: 扫描中=${state.isScanning}, 设备数=${state.devices.length}");
      },
      onError: (error) {
        _addLog("发现状态流错误: $error"); // 添加错误处理
      },
    );

    // 监听连接状态，并在连接成功时检查设备能力
    _connectionStateSubscription = _deviceFactory.connectionState.listen((
      event,
    ) {
      setState(() => _connectionState = event.state);
      _addLog("设备状态变化: ${event.state}");

      if (event.state == BleConnectionState.connected) {
        _getDeviceInfo();
        _checkDeviceCapabilities(); // 新增：检查设备能力
      }
    });
  }

  // 获取设备信息
  void _getDeviceInfo() {
    try {
      // 获取连接的设备
      final device = _deviceFactory.getConnectedDevice();
      if (device != null) {
        _addLog("已连接设备: ${device.name} (类型: ${device.deviceType})");
      } else {
        _addLog("无法获取已连接设备信息");
      }
    } catch (e) {
      _addLog("获取设备信息出错: $e");
    }
  }

  // 添加日志
  void _addLog(String message) {
    setState(() {
      _logs.add("${DateTime.now().toString().split('.').first}: $message");
      if (_logs.length > 100) _logs.removeAt(0);
    });
    Log.i(message);
  }

  // 开始扫描
  void _startScan() {
    try {
      _deviceFactory.startScan(
        deviceTypes: [DeviceModeType.dx5, DeviceModeType.dx9],
      );
      _addLog("开始扫描所有设备");
    } catch (e) {
      _addLog("开始扫描出错: $e");
    }
  }

  // 停止扫描
  void _stopScan() {
    try {
      _deviceFactory.stopScan();
      _addLog("停止扫描");
    } catch (e) {
      _addLog("停止扫描出错: $e");
    }
  }

  // 连接设备
  void _connectToDevice(BleDevice device) async {
    try {
      _addLog(
        "正在连接到设备: ${device.name} (句柄: ${device.nativeHandle}, 类型: ${device.deviceType})",
      );

      // 连接设备 - 设备工厂会自动管理设备管理器
      await _deviceFactory.connectDevice(device);

      _addLog("设备管理器类型: ${_deviceFactory.currentDeviceManager?.runtimeType}");
    } catch (e) {
      _addLog("连接设备出错: $e");
    }
  }

  // 断开连接
  void _disconnectDevice() async {
    try {
      _addLog("正在断开连接");
      await _deviceFactory.disconnectCurrentDevice();
    } catch (e) {
      _addLog("断开连接时出错: $e");
    }
  }

  // 执行设置音量操作 (已修改为直接调用)
  void _performSetVolumeOperation() async {
    final deviceManager = _deviceFactory.currentDeviceManager;

    if (deviceManager == null) {
      _addLog("无法获取设备管理器 (音量操作)");
      return;
    }

    try {
      _addLog("准备直接设置默认音量");
      deviceManager.setVolume(20);
      _addLog("设置音量命令已发送");
    } catch (e) {
      _addLog("执行设置音量操作时出错: $e");
    }
  }

  // 新增: 执行设置输入源操作 (已修改为直接调用)
  void _performSetInputCommand() async {
    final deviceManager = _deviceFactory.currentDeviceManager;

    if (deviceManager == null) {
      _addLog("无法获取设备管理器 (输入源操作)");
      return;
    }

    try {
      _addLog("准备直接设置输入源");
      deviceManager.setInputType(1);
      _addLog("设置输入源操作已执行");
    } catch (e) {
      _addLog("执行设置输入源操作时出错: $e");
    }
  }

  // 新增: 执行切换静音操作 (已修改为直接调用)
  void _performToggleMuteCommand() async {
    final deviceManager = _deviceFactory.currentDeviceManager;

    if (deviceManager == null) {
      _addLog("无法获取设备管理器 (静音操作)");
      return;
    }

    // 切换本地状态（实际应用中应监听设备事件流更新）
    final newMuteState = !_isMuted;

    try {
      _addLog("准备直接设置静音为 $newMuteState");
      deviceManager.setMute(newMuteState);
      _addLog("设置静音操作已执行");
      // 更新本地状态 (仅示例，最好监听真实状态)
      setState(() {
        _isMuted = newMuteState;
      });
    } catch (e) {
      _addLog("执行设置静音操作时出错: $e");
    }
  }

  // 新增: 检查设备能力
  void _checkDeviceCapabilities() {
    final deviceManager = _deviceFactory.currentDeviceManager;
    if (deviceManager != null) {}
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('增强设备示例')),
      body: Column(
        children: [
          // 扫描控制 & 通用操作
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Wrap(
              // 使用 Wrap 换行
              spacing: 8.0, // 水平间距
              runSpacing: 8.0, // 垂直间距
              alignment: WrapAlignment.center,
              children: [
                ElevatedButton(
                  onPressed: _isScanning ? _stopScan : _startScan,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _isScanning ? Colors.red : Colors.green,
                  ),
                  child: Text(_isScanning ? '停止扫描' : '开始扫描'),
                ),
                ElevatedButton(
                  onPressed:
                      _connectionState == BleConnectionState.connected
                          ? _disconnectDevice
                          : null,
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                  child: const Text('断开连接'),
                ),
                ElevatedButton(
                  onPressed:
                      _connectionState == BleConnectionState.connected
                          ? _performSetVolumeOperation
                          : null,
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.blue),
                  child: const Text('设置默认音量'),
                ),
                ElevatedButton(
                  onPressed:
                      _connectionState == BleConnectionState.connected
                          ? _performSetInputCommand
                          : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                  ),
                  child: const Text('设置输入源(默认)'),
                ),
                ElevatedButton(
                  onPressed:
                      _connectionState == BleConnectionState.connected
                          ? _performToggleMuteCommand
                          : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _isMuted ? Colors.grey : Colors.purple,
                  ),
                  child: Text(_isMuted ? '取消静音' : '设置静音'),
                ),
              ],
            ),
          ),

          // 连接状态
          Container(
            padding: const EdgeInsets.all(12),
            margin: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color:
                  _connectionState == BleConnectionState.connected
                      ? Colors.green[100]
                      : Colors.grey[200],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  _connectionState == BleConnectionState.connected
                      ? Icons.bluetooth_connected
                      : Icons.bluetooth_disabled,
                  color:
                      _connectionState == BleConnectionState.connected
                          ? Colors.green
                          : Colors.grey,
                ),
                const SizedBox(width: 8),
                Text(
                  '连接状态: ${_connectionState.toString().split('.').last}',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                if (_deviceFactory.currentDeviceManager != null) ...[
                  const SizedBox(width: 16),
                  Text(
                    '设备管理器: ${_deviceFactory.currentDeviceManager.runtimeType}',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ],
              ],
            ),
          ),

          // 设备列表
          Expanded(
            flex: 2,
            child: Column(
              children: [
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text(
                    '扫描结果 (${_devices.length})',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Expanded(
                  child:
                      _devices.isEmpty
                          ? const Center(child: Text('暂无设备'))
                          : ListView.builder(
                            itemCount: _devices.length,
                            itemBuilder: (context, index) {
                              final device = _devices[index];
                              return Card(
                                margin: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                child: ListTile(
                                  leading: Icon(
                                    Icons.bluetooth,
                                    color:
                                        device.deviceType == DeviceModeType.dx5
                                            ? Colors.blue
                                            : device.deviceType ==
                                                DeviceModeType.dx9
                                            ? Colors.green
                                            : Colors.grey,
                                  ),
                                  title: Text(
                                    device.name.isEmpty ? "未知设备" : device.name,
                                  ),
                                  subtitle: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text('RSSI: ${device.rssi} dBm'),
                                      Text(
                                        '设备类型: ${device.deviceType.toString().split('.').last}',
                                      ),
                                    ],
                                  ),
                                  trailing:
                                      _connectionState ==
                                              BleConnectionState.disconnected
                                          ? ElevatedButton(
                                            onPressed:
                                                () => _connectToDevice(device),
                                            child: const Text('连接'),
                                          )
                                          : null,
                                  isThreeLine: true,
                                ),
                              );
                            },
                          ),
                ),
              ],
            ),
          ),

          // 日志
          Expanded(
            flex: 1,
            child: Column(
              children: [
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        '日志:',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      ElevatedButton(
                        onPressed: () => setState(() => _logs.clear()),
                        child: const Text('清除日志'),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Container(
                    margin: const EdgeInsets.all(8),
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child:
                        _logs.isEmpty
                            ? const Center(child: Text('暂无日志'))
                            : ListView.builder(
                              itemCount: _logs.length,
                              itemBuilder: (context, index) {
                                final reversedIndex = _logs.length - 1 - index;
                                return Text(_logs[reversedIndex]);
                              },
                            ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
