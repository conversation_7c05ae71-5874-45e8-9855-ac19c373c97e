import 'dart:async';
import 'dart:collection';

import '../../utils/log_util.dart';
import '../../utils/string_utils.dart';
import 'segment.dart';
import 'rwcp.dart';
import 'rwcp_listener.dart';

class RwcpClient {
  /// 用于日志显示的标签。
  final String tag = "RwcpClient";

  /// 与应用程序通信并发送段的监听器。
  final RwcpListener mListener;

  /// 服务器已确认的最后一个序列的序列号。
  int mLastAckSequence = 0;

  /// 将要发送的下一个序列号。
  int mNextSequence = 0;

  /// 开始传输时使用的窗口大小。
  int mInitialWindow = Rwcp.windowDefault;

  /// 调整窗口大小时使用的最大窗口大小。
  int mMaximumWindow = Rwcp.windowMax;

  /// 窗口表示可以同时发送的最大段数。
  int mWindow = Rwcp.windowDefault;

  /// 信用数表示还可以发送多少段来填充当前窗口。
  int mCredits = Rwcp.windowDefault;

  /// 当接收到GAP或操作超时时，此客户端重新发送未确认的数据并停止任何其他正在运行的操作。
  bool mIsResendingSegments = false;

  /// 客户端的状态。
  int mState = RwcpState.listen;

  /// 等待发送的数据队列。
  var mPendingData = ListQueue<List<int>>();

  /// 已发送但尚未被确认的段队列。
  var mUnacknowledgedSegments = ListQueue<Segment>();

  /// 用于知道是否有超时正在运行。
  bool isTimeOutRunning = false;

  /// 用于DATA段超时的时间。
  int mDataTimeOutMs = Rwcp.dataTimeoutMsDefault;

  /// 显示调试日志，指示何时到达方法。
  bool mShowDebugLogs = true;

  /// 用于知道连续通过DATA_ACK确认的段数。
  int mAcknowledgedSegments = 0;
  Timer? _timer;

  RwcpClient(this.mListener);

  bool isRunningASession() {
    return mState != RwcpState.listen;
  }

  void showDebugLogs(bool show) {
    mShowDebugLogs = show;
    Log.i(tag, "调试日志现在${show ? "已激活" : "已禁用"}。");
  }

  bool sendData(List<int> bytes) {
    mPendingData.add(bytes);
    if (mState == RwcpState.listen) {
      return startSession();
    } else if (mState == RwcpState.established && !isTimeOutRunning) {
      sendDataSegment();
      return true;
    }

    return true;
  }

  void cancelTransfer() {
    logState("cancelTransfer");

    if (mState == RwcpState.listen) {
      Log.i(tag, "cancelTransfer: 没有正在进行的传输可以取消。");
      return;
    }

    reset(true);

    if (!sendRSTSegment()) {
      Log.w(tag, "发送RST段失败，终止会话。");
      terminateSession();
    }
  }

  bool onReceiveRwcpSegment(List<int>? bytes) {
    if (bytes == null) {
      Log.w(tag, "onReceiveRwcpSegment使用空字节数组调用。");
      return false;
    }

    if (bytes.length < RwcpSegment.requiredInformationLength) {
      String message = "RWCP段分析失败：字节数组不包含最小所需信息。";
      if (mShowDebugLogs) {
        message += "\n\tbytes=${StringUtils.byteToHexString(bytes)}";
      }
      Log.w(tag, message);
      return false;
    }

    // 从字节中获取段信息
    Segment segment = Segment.parse(bytes);
    int code = segment.getOperationCode();
    if (code == -1) {
      Log.w(
        tag,
        "onReceivedRwcpSegment无法从给定字节获取RWCP段: $code data->${StringUtils.byteToHexString(bytes)}",
      );
      return false;
    }

    Log.d(tag, "onReceiveRwcpSegment code$code");
    // 处理段取决于操作代码。
    switch (code) {
      case RwcpOpCodeServer.synAck:
        return receiveSynAck(segment);
      case RwcpOpCodeServer.dataAck:
        return receiveDataAck(segment);
      case RwcpOpCodeServer.rst:
        /*case Rwcp.OpCode.Server.RST_ACK:*/
        return receiveRST(segment);
      case RwcpOpCodeServer.gap:
        return receiveGAP(segment);
      default:
        Log.w(tag, "收到未知操作代码: $code");
        return false;
    }
  }

  int getInitialWindowSize() {
    return mInitialWindow;
  }

  bool setInitialWindowSize(int size) {
    logState("将初始窗口大小设置为 $size");

    if (mState != RwcpState.listen) {
      Log.w(tag, "无法将初始窗口大小设置为 $size：在会话进行中时不可能。");
      return false;
    }

    if (size <= 0 || size > mMaximumWindow) {
      Log.w(tag, "无法将初始窗口设置为 $size：大小超出范围。");
      return false;
    }

    mInitialWindow = size;
    mWindow = mInitialWindow; // 不在进行中的会话中，窗口设置为初始值
    return true;
  }

  int getMaximumWindowSize() {
    return mMaximumWindow;
  }

  bool setMaximumWindowSize(int size) {
    logState("将最大窗口大小设置为 $size");

    if (mState != RwcpState.listen) {
      Log.w(tag, "无法将最大窗口大小设置为 $size：在会话进行中时不可能。");
      return false;
    }

    if (size <= 0 || size > Rwcp.windowMax) {
      Log.w(tag, "无法将最大窗口设置为 $size：大小超出范围。");
      return false;
    }

    if (mInitialWindow > mMaximumWindow) {
      Log.w(tag, "无法将最大窗口设置为 $size：初始窗口是 $mInitialWindow。");
      return false;
    }

    mMaximumWindow = size;
    if (mWindow > mMaximumWindow) {
      Log.i(tag, "窗口已更新为小于最大窗口大小 ($mInitialWindow)。");
      mWindow = mMaximumWindow;
    }
    return true;
  }

  bool receiveRST(Segment segment) {
    if (mShowDebugLogs) {
      Log.d(tag, "接收RST或RST_ACK，序列号为 ${segment.getSequenceNumber()}");
    }

    switch (mState) {
      case RwcpState.synSent:
        Log.i(
          tag,
          "在SYN_SENT状态下接收到RST（序列 ${segment.getSequenceNumber()}），忽略段。",
        );
        return true;

      case RwcpState.established:
        // 收到RST
        Log.w(
          tag,
          "在ESTABLISHED状态下接收到RST（序列 ${segment.getSequenceNumber()}），终止会话，传输失败。",
        );
        terminateSession();
        mListener.onTransferFailed();
        return true;

      case RwcpState.closing:
        // 收到RST_ACK
        cancelTimeOut();
        validateAckSequence(RwcpOpCodeClient.rst, segment.getSequenceNumber());
        reset(false);
        if (mPendingData.isNotEmpty) {
          // 预期在启动会话时：RST发送在SYN之前，发送SYN启动会话
          if (!sendSYNSegment()) {
            Log.w(tag, "RWCP数据传输会话启动失败：SYN发送失败。");
            terminateSession();
            mListener.onTransferFailed();
          }
        } else {
          // RST已确认：传输完成
          mListener.onTransferFinished();
        }
        return true;

      case RwcpState.listen:
      default:
        Log.w(
          tag,
          "在状态 ${Rwcp.getStateLabel(mState)} 下接收到意外的RST段，序列=${segment.getSequenceNumber()}",
        );
        return false;
    }
  }

  bool sendSYNSegment() {
    bool done = false;
    mState = RwcpState.synSent;
    Segment segment = Segment.get(RwcpOpCodeClient.syn, mNextSequence);
    done = sendSegment(segment, Rwcp.synTimeoutMs);
    if (done) {
      mUnacknowledgedSegments.add(segment);
      mNextSequence = increaseSequenceNumber(mNextSequence);
      mCredits--;
      logState("发送SYN段");
    }
    return done;
  }

  void logState(String label) {
    if (mShowDebugLogs) {
      String message =
          "$label\t\t\tstate=${Rwcp.getStateLabel(mState)}\n\tWindow: \tcurrent = $mWindow \t\tdefault = $mInitialWindow \t\tcredits = $mCredits\n\tSequence: \tlast = $mLastAckSequence \t\tnext = $mNextSequence\n\tPending: \tPSegments = ${mUnacknowledgedSegments.length} \t\tPData = ${mPendingData.length}";
      Log.d(tag, message);
    }
  }

  bool startSession() {
    logState("startSession");

    if (mState != RwcpState.listen) {
      Log.w(tag, "启动RWCP会话失败：已有正在进行的会话。");
      return false;
    }

    // 建议发送RST然后发送SYN，以确保服务器端处于正确的状态。
    // 此客户端首先发送RST段，等待获取RST_ACK段，然后发送SYN段。
    // 如果有一些待发送的数据，则发送SYN。
    if (sendRSTSegment()) {
      return true;
      // 等待调用receiveRST。
    } else {
      Log.w(tag, "启动RWCP会话失败：发送RST段失败。");
      terminateSession();
      return false;
    }
  }

  void terminateSession() {
    logState("terminateSession");
    reset(true);
  }

  bool sendRSTSegment() {
    if (mState == RwcpState.closing) {
      // RST已发送等待确认
      return true;
    }

    bool done = false;
    reset(false);
    mState = RwcpState.closing;
    Segment segment = Segment.get(RwcpOpCodeClient.rst, mNextSequence);
    done = sendSegment(segment, Rwcp.rstTimeoutMs);
    if (done) {
      mUnacknowledgedSegments.add(segment);
      mNextSequence = increaseSequenceNumber(mNextSequence);
      mCredits--;
      logState("发送RST段");
    }
    return done;
  }

  bool sendSegment(Segment segment, int timeout) {
    List<int> bytes = segment.getBytes();
    if (mListener.sendRwcpSegment(bytes)) {
      startTimeOut(timeout);
      return true;
    }

    return false;
  }

  void startTimeOut(int delay) {
    if (isTimeOutRunning) {
      _timer?.cancel();
    }

    isTimeOutRunning = true;
    _timer = Timer(Duration(milliseconds: delay), () {
      onTimeOut();
    });
  }

  void onTimeOut() {
    if (isTimeOutRunning) {
      isTimeOutRunning = false;
      mIsResendingSegments = true;
      mAcknowledgedSegments = 0;

      if (mShowDebugLogs) {
        Log.i(tag, "超时 > 重新发送段");
      }

      if (mState == RwcpState.established) {
        // 超时的段是DATA段：增加数据超时值
        mDataTimeOutMs *= 2;
        if (mDataTimeOutMs > Rwcp.dataTimeoutMsMax) {
          mDataTimeOutMs = Rwcp.dataTimeoutMsMax;
        }

        resendDataSegment();
      } else {
        // SYN或RST段超时
        resendSegment();
      }
    }
  }

  void resendSegment() {
    if (mState == RwcpState.established) {
      Log.w(tag, "尝试在ESTABLISHED状态下重新发送非数据段。");
      return;
    }

    mIsResendingSegments = true;
    mCredits = mWindow;

    // 重新发送与窗口对应的未确认段
    for (Segment segment in mUnacknowledgedSegments) {
      int delay =
          (segment.getOperationCode() == RwcpOpCodeClient.syn)
              ? Rwcp.synTimeoutMs
              : (segment.getOperationCode() == RwcpOpCodeClient.rst)
              ? Rwcp.rstTimeoutMs
              : mDataTimeOutMs;
      sendSegment(segment, delay);
      mCredits--;
    }
    logState("重新发送段");

    mIsResendingSegments = false;
  }

  void resendDataSegment() {
    if (mState != RwcpState.established) {
      Log.w(tag, "尝试在非ESTABLISHED状态下重新发送数据段。");
      return;
    }

    mIsResendingSegments = true;
    mCredits = mWindow;
    logState("重置信用");

    // 如果未确认的段多于可用的信用，则这些额外的段不再是未确认的，而是待处理的
    int moved = 0;
    while (mUnacknowledgedSegments.length > mCredits) {
      Segment segment = mUnacknowledgedSegments.last;
      if (segment.getOperationCode() == RwcpOpCodeClient.data) {
        mUnacknowledgedSegments.removeLast();
        mPendingData.addFirst(segment.getPayload());
        moved++;
      } else {
        Log.w(tag, "段 $segment 在待处理段中，但不是DATA段。");
        break;
      }
    }

    // 如果一些段已移至待处理状态，则下一个序列号已更改。
    mNextSequence = decreaseSequenceNumber(mNextSequence, moved);

    // 重新发送与窗口对应的未确认段
    for (var segment in mUnacknowledgedSegments) {
      sendSegment(segment, mDataTimeOutMs);
      mCredits--;
    }

    logState("重新发送DATA段");

    mIsResendingSegments = false;

    if (mCredits > 0) {
      sendDataSegment();
    }
  }

  void sendDataSegment() {
    while (mCredits > 0 &&
        mPendingData.isNotEmpty &&
        !mIsResendingSegments &&
        mState == RwcpState.established) {
      List<int> data = mPendingData.removeFirst();
      Segment segment = Segment.get(
        RwcpOpCodeClient.data,
        mNextSequence,
        payload: data,
      );
      sendSegment(segment, mDataTimeOutMs);
      mUnacknowledgedSegments.add(segment);
      mNextSequence = increaseSequenceNumber(mNextSequence);
      mCredits--;
    }
    logState("发送DATA段");
  }

  int increaseSequenceNumber(int sequence) {
    return (sequence + 1) % (Rwcp.sequenceNumberMax + 1);
  }

  int decreaseSequenceNumber(int sequence, int decrease) {
    return (sequence - decrease + Rwcp.sequenceNumberMax + 1) %
        (Rwcp.sequenceNumberMax + 1);
  }

  void reset(bool complete) {
    mLastAckSequence = -1;
    mNextSequence = 0;
    mState = RwcpState.listen;
    mUnacknowledgedSegments.clear();
    mWindow = mInitialWindow;
    mAcknowledgedSegments = 0;
    mCredits = mWindow;
    cancelTimeOut();
    if (complete) {
      mPendingData.clear();
    }
    logState("重置");
  }

  void cancelTimeOut() {
    if (isTimeOutRunning) {
      _timer?.cancel();
      isTimeOutRunning = false;
    }
  }

  bool receiveSynAck(Segment segment) {
    if (mShowDebugLogs) {
      Log.d(tag, "接收序列 ${segment.getSequenceNumber()} 的SYN_ACK");
    }

    switch (mState) {
      case RwcpState.synSent:
        // 预期行为：开始发送数据
        cancelTimeOut();
        int validated = validateAckSequence(
          RwcpOpCodeClient.syn,
          segment.getSequenceNumber(),
        );
        if (validated >= 0) {
          mState = RwcpState.established;
          if (mPendingData.isNotEmpty) {
            sendDataSegment();
          }
        } else {
          Log.w(tag, "接收到意外序列号的SYN_ACK：${segment.getSequenceNumber()}");
          terminateSession();
          mListener.onTransferFailed();
          sendRSTSegment();
        }
        return true;

      case RwcpState.established:
        // DATA可能已丢失，重新发送它们
        cancelTimeOut();
        if (mUnacknowledgedSegments.isNotEmpty) {
          resendDataSegment();
        }
        return true;

      case RwcpState.closing:
      case RwcpState.listen:
      default:
        Log.w(
          tag,
          "在状态 ${Rwcp.getStateLabel(mState)} 下接收到意外的SYN_ACK段，头部 ${segment.getHeader()}",
        );
        return false;
    }
  }

  int validateAckSequence(final int code, final int sequence) {
    final int notValidated = -1;

    if (sequence < 0) {
      Log.w(tag, "接收到的ACK序列 ($sequence) 小于0。");
      return notValidated;
    }

    if (sequence > Rwcp.sequenceNumberMax) {
      Log.w(tag, "接收到的ACK序列 ($sequence) 大于其最大值 (${Rwcp.sequenceNumberMax})。");
      return notValidated;
    }

    if (mLastAckSequence < mNextSequence &&
        (sequence < mLastAckSequence || sequence > mNextSequence)) {
      Log.w(
        tag,
        "接收到的ACK序列 ($sequence) 超出间隔：最后接收到的是 $mLastAckSequence，下一个将是 $mNextSequence",
      );
      return notValidated;
    }

    if (mLastAckSequence > mNextSequence &&
        sequence < mLastAckSequence &&
        sequence > mNextSequence) {
      Log.w(
        tag,
        "接收到的ACK序列 ($sequence) 超出间隔：最后接收到的是 $mLastAckSequence，下一个将是 $mNextSequence",
      );
      return notValidated;
    }

    int acknowledged = 0;
    int nextAckSequence = mLastAckSequence;
    while (nextAckSequence != sequence) {
      nextAckSequence = increaseSequenceNumber(nextAckSequence);
      if (removeSegmentFromQueue(code, nextAckSequence)) {
        mLastAckSequence = nextAckSequence;
        if (mCredits < mWindow) {
          mCredits++;
        }
        acknowledged++;
      } else {
        Log.w(tag, "验证序列 $nextAckSequence 出错：在待处理段中没有对应的段。");
      }
    }

    logState("$acknowledged 个段通过ACK序列验证 (code=$code seq=$sequence");

    // 如果符合条件，则增加窗口大小。
    increaseWindow(acknowledged);

    return acknowledged;
  }

  bool removeSegmentFromQueue(int code, int sequence) {
    for (Segment segment in mUnacknowledgedSegments) {
      if (segment.getOperationCode() == code &&
          segment.getSequenceNumber() == sequence) {
        mUnacknowledgedSegments.remove(segment);
        return true;
      }
    }
    Log.w(tag, "待处理段不包含已确认的段：code=$code \tsequence=$sequence");
    return false;
  }

  void increaseWindow(int acknowledged) {
    mAcknowledgedSegments += acknowledged;
    if (mAcknowledgedSegments > mWindow && mWindow < mMaximumWindow) {
      mAcknowledgedSegments = 0;
      mWindow++;
      mCredits++;
      logState("将窗口增加到 $mWindow");
    }
  }

  bool receiveDataAck(Segment segment) {
    if (mShowDebugLogs) {
      Log.d(tag, "接收序列 ${segment.getSequenceNumber()} 的DATA_ACK");
    }

    switch (mState) {
      case RwcpState.established:
        cancelTimeOut();
        int sequence = segment.getSequenceNumber();
        int validated = validateAckSequence(RwcpOpCodeClient.data, sequence);
        if (validated >= 0) {
          if (mCredits > 0 && !mPendingData.isEmpty) {
            sendDataSegment();
          } else if (mPendingData.isEmpty && mUnacknowledgedSegments.isEmpty) {
            // 没有更多数据要发送：关闭会话
            sendRSTSegment();
          } else if (mPendingData
                  .isEmpty /*&& !mUnacknowledgedSegments.isEmpty()*/ ||
              mCredits == 0 /*&& !mPendingData.isEmpty()*/ ) {
            // 没有更多数据要发送但仍有一些等待确认
            // 或者没有信用且仍有一些数据要发送
            startTimeOut(mDataTimeOutMs);
          }
          mListener.onTransferProgress(validated);
        }
        return true;

      case RwcpState.closing:
        // RST已发送，等待RST超时或RST ACK
        if (mShowDebugLogs) {
          Log.i(
            tag,
            "在CLOSING状态下接收到DATA_ACK(${segment.getSequenceNumber()})段：段被丢弃。",
          );
        }
        return true;

      case RwcpState.synSent:
      case RwcpState.listen:
      default:
        Log.w(
          tag,
          "在状态 ${Rwcp.getStateLabel(mState)} 下接收到意外的DATA_ACK段，序列 ${segment.getSequenceNumber()}",
        );
        return false;
    }
  }

  bool receiveGAP(Segment segment) {
    if (mShowDebugLogs) {
      Log.d(tag, "接收序列 ${segment.getSequenceNumber()} 的GAP");
    }

    switch (mState) {
      case RwcpState.established:
        if (mLastAckSequence > segment.getSequenceNumber()) {
          Log.i(
            tag,
            "忽略GAP（${segment.getSequenceNumber()}），因为最后一个确认序列是 $mLastAckSequence。",
          );
          return true;
        }
        if (mLastAckSequence <= segment.getSequenceNumber()) {
          // GAP中的序列号表示丢失的DATA_ACK
          // 调整窗口
          decreaseWindow();
          // 如果未知，则验证已确认的段。
          validateAckSequence(
            RwcpOpCodeClient.data,
            segment.getSequenceNumber(),
          );
        }

        cancelTimeOut();
        resendDataSegment();
        return true;

      case RwcpState.closing:
        // RST已发送，等待RST超时或RST ACK
        if (mShowDebugLogs) {
          Log.i(
            tag,
            "在CLOSING状态下接收到GAP(${segment.getSequenceNumber()})段：段被丢弃。",
          );
        }
        return true;

      case RwcpState.synSent:
      case RwcpState.listen:
      default:
        Log.w(
          tag,
          "在状态 ${Rwcp.getStateLabel(mState)} 下接收到意外的GAP段，头部 ${segment.getHeader()}",
        );
        return false;
    }
  }

  void decreaseWindow() {
    mWindow = ((mWindow - 1) ~/ 2) + 1;
    if (mWindow > mMaximumWindow || mWindow < 1) {
      mWindow = 1;
    }

    mAcknowledgedSegments = 0;
    mCredits = mWindow;

    logState("将窗口减少到 $mWindow");
  }
}
