#include "FlutterBleScanner.h"
#include <stdio.h>
#include <string.h>
namespace Topping
{
    flutter_ble_scanner_functions_t FlutterBleScanner::mFunctions;

    FlutterBleScanner::FlutterBleScanner() : mScanCallback(nullptr)
    {
        mFlutterObject = mFunctions.init((long)this);
    }

    FlutterBleScanner::~FlutterBleScanner()
    {
        mFunctions.uninit(mFlutterObject);
    }

    void FlutterBleScanner::startScan(ScanCallback *callback)
    {
        mScanCallback = callback;
        mFunctions.start_scan(mFlutterObject);
    }

    void FlutterBleScanner::stopScan(ScanCallback *callback)
    {
        mScanCallback = callback;
        mFunctions.stop_scan(mFlutterObject);
    }
    
    void FlutterBleScanner::registerFunctions(struct flutter_ble_scanner_functions_t *functions)
    {
        mFunctions = *functions;
    }
}

extern "C" {
void flutter_ble_scanner_register_functions(struct flutter_ble_scanner_functions_t *functions)
{
    Topping::FlutterBleScanner::registerFunctions(functions);
}

void flutter_ble_scanner_scan_results(long native_object, struct scan_result_t *results, size_t count)
{
    Topping::FlutterBleScanner *flutterBleScanner = (Topping::FlutterBleScanner *)native_object;
    std::vector<Topping::FlutterBleScanner::ScanResult> scan_results;
    for (size_t i = 0; i < count; i++)
    {
        Topping::FlutterBleScanner::ScanResult result;
        result.device = Topping::BluetoothDevice((void *)results[i].device);
        result.device.setName(results[i].name);
        result.rssi = results[i].rssi;
        for (size_t j = 0; j < results[i].manufacturer_data_len; j++)
        {
            result.manufacturerData.push_back(results[i].manufacturer_data[j]);
        }
        scan_results.push_back(result);
    }
    flutterBleScanner->mScanCallback->onBatchScanResults(scan_results);
}

void flutter_ble_scanner_scan_failed(long native_object, int errorCode)
{
    Topping::FlutterBleScanner *flutterBleScanner = (Topping::FlutterBleScanner *)native_object;
    flutterBleScanner->mScanCallback->onScanFailed(errorCode);
}
}