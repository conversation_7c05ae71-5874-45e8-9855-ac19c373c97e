import 'dart:ffi';
import 'package:ffi/ffi.dart';

import '../model/bluetooth/gatt_characteristic.dart';
import '../model/enums/ble_connection_state.dart';
import '../model/ffi/ffi_connection_state.dart';
import '../model/ffi/ffi_gatt_characteristic.dart';
import '../utils/log_util.dart';

/// FFI数据映射工具类
/// 负责将FFI层数据结构转换为Dart业务模型
class FFIMapper {
  /// 将FFI连接状态转换为Dart连接状态枚举
  static BleConnectionState fromFFIConnectionState(FFIConnectionState state) {
    switch (state) {
      case FFIConnectionState.stateConnectedUnsafe:
        return BleConnectionState.connectedUnsafe;
      case FFIConnectionState.stateConnected:
        return BleConnectionState.connected;
      case FFIConnectionState.stateDisconnected:
        return BleConnectionState.disconnected;
    }
  }

  /// 将FFI连接状态值转换为Dart连接状态枚举
  static BleConnectionState connectionStateFromValue(int value) {
    // 先尝试转换为FFI状态
    FFIConnectionState? ffiState;
    if (value == FFIConnectionState.stateConnectedUnsafe.value) {
      ffiState = FFIConnectionState.stateConnectedUnsafe;
    } else if (value == FFIConnectionState.stateConnected.value) {
      ffiState = FFIConnectionState.stateConnected;
    } else if (value == FFIConnectionState.stateDisconnected.value) {
      ffiState = FFIConnectionState.stateDisconnected;
    }

    // 如果是FFI状态，转换后返回
    if (ffiState != null) {
      return fromFFIConnectionState(ffiState);
    }

    // 否则尝试直接从BleConnectionState的值转换
    return BleConnectionState.fromValue(value);
  }

  /// 从FFI特征值创建GattCharacteristic
  static GattCharacteristic fromFFIGattCharacteristic(
    FFIGattCharacteristic characteristic,
  ) {
    final uuid = _safeReadUtf8String(characteristic.uuid);
    final property = characteristic.property;

    // 读取二进制值
    List<int> value = [];
    if (characteristic.value != nullptr && characteristic.value_len > 0) {
      for (int i = 0; i < characteristic.value_len; i++) {
        value.add(characteristic.value[i]);
      }
    }

    return GattCharacteristic(uuid: uuid, property: property, value: value);
  }

  /// 将GattCharacteristic转换为FFIGattCharacteristic (用于写操作)
  static Pointer<FFIGattCharacteristic> toFFIGattCharacteristic(
    GattCharacteristic characteristic,
    Allocator allocator,
  ) {
    final nativeChar = allocator<FFIGattCharacteristic>();

    try {
      // 转换UUID
      final uuidPtr = characteristic.uuid.toNativeUtf8(allocator: allocator);
      nativeChar.ref.uuid = uuidPtr.cast();
      nativeChar.ref.property = characteristic.property;

      // 转换二进制值
      if (characteristic.value.isNotEmpty) {
        final valuePtr = allocator<Uint8>(characteristic.value.length);
        for (var i = 0; i < characteristic.value.length; i++) {
          valuePtr[i] = characteristic.value[i];
        }
        nativeChar.ref.value = valuePtr;
        nativeChar.ref.value_len = characteristic.value.length;
      } else {
        nativeChar.ref.value = nullptr;
        nativeChar.ref.value_len = 0;
      }

      return nativeChar;
    } catch (e) {
      Log.e('转换特征值到FFI结构时出错: $e');

      // 清理已分配的内存
      if (nativeChar.ref.uuid != nullptr) {
        allocator.free(nativeChar.ref.uuid.cast<NativeType>());
      }
      if (nativeChar.ref.value != nullptr) {
        allocator.free(nativeChar.ref.value);
      }
      allocator.free(nativeChar);

      // 重新抛出异常
      rethrow;
    }
  }

  /// 安全地读取UTF8字符串，处理空指针等异常情况
  static String _safeReadUtf8String(Pointer<Char> charPtr) {
    if (charPtr == nullptr) {
      return '';
    }

    try {
      return charPtr.cast<Utf8>().toDartString();
    } catch (e) {
      Log.e('读取UTF8字符串失败: $e');
      return '';
    }
  }
}
