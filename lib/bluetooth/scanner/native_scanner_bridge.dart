/**
 * 此文件已不再使用。保留此文件仅作为参考。
 * 项目现在直接使用Flutter的蓝牙扫描功能，不再通过C++层进行扫描。
 */

import 'dart:ffi';

import 'package:ffi/ffi.dart';

import '../../model/bluetooth/ble_device.dart';
import '../../model/enums/device_mode_type.dart';
import '../../utils/log_util.dart';
import '../ffi/ble_ffi_initializer.dart';
import 'controller_scanner_bindings.dart';

/// 原生扫描器桥接类
/// 负责与C++层的扫描器通信
class NativeScannerBridge {
  // 原生扫描器ID
  int? _nativeScannerId;

  // 回调函数指针
  Pointer<FFIControllerScannerCallback>? _scannerCallback;

  // 实例映射表，用于回调时查找对应的实例
  static final Map<int, NativeScannerBridge> _instanceMap = {};

  // Flutter对象ID，用于C++回调时识别
  final int _flutterObjectId;

  // 扫描结果回调
  final Function(List<BleDevice>) _onScanResultsCallback;

  // 扫描失败回调
  final Function(int) _onScanFailedCallback;

  // 当前扫描的设备类型
  List<DeviceModeType> _currentDeviceTypes = [];

  // 扫描到的设备列表
  final List<BleDevice> _scannedDevices = [];

  /// 构造函数
  /// [flutterObjectId] Flutter对象的唯一标识
  /// [onScanResultsCallback] 扫描结果回调
  /// [onScanFailedCallback] 扫描失败回调
  NativeScannerBridge({
    required int flutterObjectId,
    required Function(List<BleDevice>) onScanResultsCallback,
    required Function(int) onScanFailedCallback,
  }) : _flutterObjectId = flutterObjectId,
       _onScanResultsCallback = onScanResultsCallback,
       _onScanFailedCallback = onScanFailedCallback {
    // 注册实例
    _instanceMap[_flutterObjectId] = this;

    // 初始化原生扫描器
    _initNativeScanner();
  }

  /// 初始化原生扫描器
  void _initNativeScanner() {
    try {
      // 分配回调结构体
      _scannerCallback = calloc<FFIControllerScannerCallback>();

      // 设置回调
      _setupNativeCallbacks();

      // 创建原生扫描器
      _createNativeScanner();
    } catch (e) {
      Log.e('初始化原生扫描器失败: $e');
    }
  }

  /// 设置原生回调
  void _setupNativeCallbacks() {
    if (_scannerCallback == null) return;

    // 扫描结果回调
    _scannerCallback!.ref.on_scan_results = Pointer.fromFunction<
      Void Function(Int64, Pointer<FFIControllerScanResult>, IntPtr)
    >(_onScanResults);

    // 扫描失败回调
    _scannerCallback!.ref.on_scan_failed =
        Pointer.fromFunction<Void Function(Int64, Int32)>(_onScanFailed);
  }

  /// 创建原生扫描器
  void _createNativeScanner() {
    try {
      if (_scannerCallback == null) return;

      _nativeScannerId = ControllerScannerBindings.instance.createScanner(
        _flutterObjectId,
        _scannerCallback!,
      );

      Log.i('原生扫描器创建成功，ID: $_nativeScannerId');
    } catch (e) {
      Log.e('创建原生扫描器失败: $e');
    }
  }

  /// 开始原生扫描
  /// [deviceTypes] 要扫描的设备类型列表
  Future<void> startScan(List<DeviceModeType> deviceTypes) async {
    try {
      // 保存当前扫描的设备类型
      _currentDeviceTypes = deviceTypes;

      // 确保 BleFfiInitializer 已初始化
      await BleFfiInitializer().ensureInitialized();

      if (_nativeScannerId == null) {
        Log.i('原生扫描器ID为空，尝试重新创建');
        // 如果原生扫描器ID为空，尝试重新创建
        _createNativeScanner();

        // 如果仍然为空，则抛出异常
        if (_nativeScannerId == null) {
          throw Exception('原生扫描器尚未创建或创建失败');
        }
      }

      // 清空已扫描设备列表
      _scannedDevices.clear();

      // 添加日志记录扫描器状态
      Log.i('开始原生扫描，扫描器ID: $_nativeScannerId');

      // 调用原生扫描方法
      ControllerScannerBindings.instance.startScan(_nativeScannerId!);

      // 添加延迟检查，确保扫描已启动
      await Future.delayed(Duration(seconds: 2));

      // 如果在一定时间内没有扫描到设备，可能是原生扫描器有问题
      if (_scannedDevices.isEmpty) {
        Log.i('原生扫描启动后2秒内没有发现设备，可能需要检查原生扫描器');
      }
    } catch (e) {
      // 捕获并记录所有异常
      Log.e('原生扫描器启动失败: $e');

      // 重新抛出异常，让上层处理
      rethrow;
    }
  }

  /// 停止原生扫描
  void stopScan() {
    try {
      // 停止原生扫描
      if (_nativeScannerId != null) {
        ControllerScannerBindings.instance.stopScan(_nativeScannerId!);
        Log.i("原生扫描已停止");
      }
    } catch (e) {
      Log.e("停止原生扫描时出错: $e");
      rethrow;
    }
  }

  /// 原生回调：处理扫描结果
  static void _onScanResults(
    int flutterObjectId,
    Pointer<FFIControllerScanResult> results,
    int count,
  ) {
    // 查找对应的实例
    final scanner = _instanceMap[flutterObjectId];
    if (scanner != null) {
      Log.i("Native回调扫描结果，找到对应实例，处理 $count 个设备");
      scanner._handleNativeScanResults(results, count);
    } else {
      Log.e('找不到flutterObjectId为$flutterObjectId的扫描器实例');
    }
  }

  /// 原生回调：处理扫描失败
  static void _onScanFailed(int flutterObjectId, int errorCode) {
    // 查找对应的实例
    final scanner = _instanceMap[flutterObjectId];
    if (scanner != null) {
      scanner._handleNativeScanFailed(errorCode);
    } else {
      Log.e('找不到flutterObjectId为$flutterObjectId的扫描器实例');
    }
  }

  /// 处理原生扫描结果
  void _handleNativeScanResults(
    Pointer<FFIControllerScanResult> results,
    int count,
  ) {
    Log.i("原生扫描到 $count 个设备");

    // 参数验证
    if (count <= 0) {
      Log.e("原生扫描结果无效: 数量=$count");
      return;
    }

    try {
      // 清空已扫描设备列表，准备接收过滤后的结果
      _scannedDevices.clear();

      // 将C++层的扫描结果转换为Dart对象
      for (int i = 0; i < count; i++) {
        try {
          final result = results[i];

          // 安全地获取设备名称，避免空指针异常
          String deviceName = "";
          try {
            if (result.name != nullptr) {
              deviceName = result.name.toDartString();
            }
          } catch (e) {
            Log.e("获取设备名称时出错: $e");
          }

          final deviceHandle = result.device;
          final rssi = result.rssi;

          // 检查设备类型是否在我们当前关注的类型中
          final deviceType = _determineDeviceType(deviceName);
          if (!_currentDeviceTypes.contains(deviceType)) {
            continue; // 跳过不需要的设备类型
          }

          // 创建BleDevice对象
          final device = BleDevice(
            id: deviceHandle.hashCode,
            name: deviceName,
            rssi: rssi,
            nativeHandle: deviceHandle,
            deviceType: deviceType,
          );

          _updateScannedDevicesList(device);
        } catch (e) {
          Log.e("处理第 $i 个原生扫描结果时出错: $e");
          // 跳过这个设备，继续处理其他设备
          continue;
        }
      }

      // 在循环外发送通知，确保只发送一次完整的设备列表
      if (_scannedDevices.isNotEmpty) {
        Log.i("发送Native过滤后的 ${_scannedDevices.length} 个设备到结果流");
        _onScanResultsCallback(List.from(_scannedDevices));
      } else {
        Log.i("原生扫描没有返回任何设备");
      }
    } catch (e) {
      Log.e("处理原生扫描结果时出错: $e");
    }
  }

  /// 处理原生扫描失败
  void _handleNativeScanFailed(int errorCode) {
    Log.e("原生扫描失败，错误码: $errorCode");
    _onScanFailedCallback(errorCode);
  }

  /// 更新扫描设备列表
  void _updateScannedDevicesList(BleDevice device) {
    // 检查是否已存在相同设备
    final existingIndex = _scannedDevices.indexWhere(
      (d) => d.nativeHandle == device.nativeHandle,
    );

    if (existingIndex >= 0) {
      // 更新已有设备
      _scannedDevices[existingIndex] = device;
    } else {
      // 添加新设备
      _scannedDevices.add(device);
    }
  }

  /// 根据设备名称确定设备类型
  DeviceModeType _determineDeviceType(String deviceName) {
    // 此处添加检测逻辑，例如:
    if (deviceName.contains('DX5 II') || deviceName.contains('DX5II')) {
      return DeviceModeType.dx5;
    } else if (deviceName.contains('D90') || deviceName.contains('D900')) {
      return DeviceModeType.dx9;
    }

    return DeviceModeType.unknown;
  }

  /// 释放资源
  void dispose() {
    // 销毁原生扫描器
    if (_nativeScannerId != null) {
      ControllerScannerBindings.instance.destroyScanner(_nativeScannerId!);
      _nativeScannerId = null;
    }

    // 从实例映射中移除
    _instanceMap.remove(_flutterObjectId);

    // 释放FFI分配的内存
    if (_scannerCallback != null) {
      calloc.free(_scannerCallback!);
      _scannerCallback = null;
    }
  }
}
