This file is a merged representation of the entire codebase, combined into a single document by Repomix.

<file_summary>
This section contains a summary of this file.

<purpose>
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.
</purpose>

<file_format>
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files, each consisting of:
  - File path as an attribute
  - Full contents of the file
</file_format>

<usage_guidelines>
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.
</usage_guidelines>

<notes>
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Files are sorted by Git change count (files with more changes are at the bottom)
</notes>

<additional_info>

</additional_info>

</file_summary>

<directory_structure>
gradle/
  wrapper/
    gradle-wrapper.properties
src/
  main/
    kotlin/
      com/
        topping/
          control/
            GaiaManager.kt
            OtaBridge.kt
            ToppingBleControlPlugin.kt
    AndroidManifest.xml
.gitignore
build.gradle
gradle.properties
publish-aar.gradle
settings.gradle
</directory_structure>

<files>
This section contains the contents of the repository's files.

<file path="gradle/wrapper/gradle-wrapper.properties">
distributionBase=GRADLE_USER_HOME
distributionPath=wrapper/dists
zipStoreBase=GRADLE_USER_HOME
zipStorePath=wrapper/dists
distributionUrl=https\://services.gradle.org/distributions/gradle-6.7.1-all.zip
</file>

<file path="src/main/kotlin/com/topping/control/GaiaManager.kt">
package com.topping.control

import android.util.Log
import com.qualcomm.qti.libraries.gaia.GAIA
import com.qualcomm.qti.libraries.gaia.packets.GaiaPacket
import com.qualcomm.qti.libraries.gaia.packets.GaiaPacketBLE

/**
 * GAIA协议管理器
 * 负责处理GAIA协议相关的功能
 */
class GaiaManager {
    private val TAG = "GaiaManager"
    
    /**
     * 检查数据是否为GAIA数据包
     */
    fun isGAIAPacket(data: ByteArray): Boolean {
        return data.size >= 8 && 
               ((data[0].toInt() and 0xFF) == 0x10 || (data[0].toInt() and 0xFF) == 0x11) && 
               ((data[1].toInt() and 0xFF) == 0x02)
    }
    
    /**
     * 从字节数组创建GAIA数据包
     */
    fun getGAIAPacketFromBytes(data: ByteArray): GaiaPacket {
        return GaiaPacketBLE(data)
    }
    
    /**
     * 创建GAIA数据包
     */
    fun createGaiaPacket(vendor: Int, command: Int, payload: ByteArray, transport: GAIA.Transport): ByteArray {
        val packet = GaiaPacketBLE(vendor, command, payload)
        return packet.getBytes()
    }
    
    /**
     * 检查是否为VMU数据包
     */
    fun isVMUPacket(data: ByteArray): Boolean {
        if (!isGAIAPacket(data)) return false
        
        val packet = GaiaPacketBLE(data)
        return packet.command == GAIA.COMMAND_VM_UPGRADE_RESPONSE
    }
    
    /**
     * 提取VMU数据包
     */
    fun extractVMUPacket(data: ByteArray): ByteArray {
        val packet = GaiaPacketBLE(data)
        return packet.payload
    }
    
    /**
     * 创建VMU数据包
     */
    fun createVMUPacket(payload: ByteArray): ByteArray {
        val packet = GaiaPacketBLE(GAIA.VENDOR_QUALCOMM, GAIA.COMMAND_VM_UPGRADE_CONTROL, payload)
        return packet.getBytes()
    }
    
    /**
     * 注册GAIA通知
     */
    fun registerGaiaNotification(event: Byte) {
        Log.d(TAG, "注册GAIA通知: $event")
        // 实际实现可能需要发送注册通知的GAIA命令
    }
}
</file>

<file path="src/main/kotlin/com/topping/control/OtaBridge.kt">
package com.topping.control

import android.bluetooth.BluetoothGattCharacteristic
import android.util.Log
import com.qualcomm.qti.libraries.gaia.GAIA
import com.qualcomm.qti.libraries.gaia.GaiaManager
import com.qualcomm.qti.libraries.vmupgrade.UpgradeError
import com.qualcomm.qti.libraries.vmupgrade.UpgradeManager
import com.qualcomm.qti.libraries.vmupgrade.UpgradeManager.UpgradeManagerListener
import com.qualcomm.qti.libraries.vmupgrade.codes.ResumePoints
import io.flutter.plugin.common.MethodChannel
import java.io.File

/**
 * OTA升级桥接类，负责连接Flutter与高通OTA库
 */
class OtaBridge(private val methodChannel: MethodChannel) : UpgradeManagerListener {
    private val TAG = "OtaBridge"
    private var gaiaManager = com.topping.control.GaiaManager()
    private var upgradeManager = UpgradeManager(this)

    // 是否已初始化
    private var isInitialized = false

    // GAIA服务和特征UUID
    companion object {
        // GAIA服务UUID
        const val GAIA_SERVICE_UUID = "00001100-d102-11e1-9b23-00025b00a5a5"
        // GAIA命令特征UUID
        const val GAIA_COMMAND_CHARACTERISTIC_UUID = "00001101-d102-11e1-9b23-00025b00a5a5"
        // GAIA响应特征UUID
        const val GAIA_RESPONSE_CHARACTERISTIC_UUID = "00001102-d102-11e1-9b23-00025b00a5a5"
    }

    /**
     * 初始化OTA功能
     */
    fun initialize(): Boolean {
        try {
            Log.i(TAG, "初始化OTA功能")
            upgradeManager.showDebugLogs(true)

            // 注册GAIA VMU数据包通知
            gaiaManager.registerGaiaNotification(GAIA.NotificationEvents.VMU_PACKET.toByte())

            isInitialized = true
            return true
        } catch (e: Exception) {
            Log.e(TAG, "初始化OTA功能失败", e)
            methodChannel.invokeMethod("onError", mapOf(
                "message" to "初始化OTA失败: ${e.message}"
            ))
            return false
        }
    }

    /**
     * 开始升级过程
     */
    fun startUpgrade(filePath: String, maxLength: Int, sendMultiplePackets: Boolean): Boolean {
        try {
            if (!isInitialized) {
                Log.e(TAG, "OTA功能未初始化")
                methodChannel.invokeMethod("onError", mapOf(
                    "message" to "OTA功能未初始化"
                ))
                return false
            }

            val file = File(filePath)
            if (!file.exists()) {
                Log.e(TAG, "升级文件不存在: $filePath")
                methodChannel.invokeMethod("onError", mapOf(
                    "message" to "升级文件不存在: $filePath"
                ))
                return false
            }

            Log.i(TAG, "设置升级文件: $filePath")
            upgradeManager.setFile(file)

            // 发送VM升级连接命令
            Log.i(TAG, "发送VM_UPGRADE_CONNECT命令")
            sendVmUpgradeConnect()

            // 启动升级流程
            Log.i(TAG, "启动升级流程，最大数据长度: $maxLength, 多数据包模式: $sendMultiplePackets")
            upgradeManager.startUpgrade(maxLength, sendMultiplePackets)

            return true
        } catch (e: Exception) {
            Log.e(TAG, "开始升级失败", e)
            methodChannel.invokeMethod("onError", mapOf(
                "message" to "开始升级失败: ${e.message}"
            ))
            return false
        }
    }

    /**
     * 发送VM升级连接命令
     */
    private fun sendVmUpgradeConnect() {
        try {
            // 创建GAIA数据包
            val commandBytes = gaiaManager.createGaiaPacket(
                GAIA.VENDOR_QUALCOMM,
                GAIA.COMMAND_VM_UPGRADE_CONNECT,
                ByteArray(0),
                GAIA.Transport.BLE
            )

            // 将命令发送回Flutter层进行写入
            methodChannel.invokeMethod("writeCharacteristic", mapOf(
                "uuid" to GAIA_COMMAND_CHARACTERISTIC_UUID,
                "data" to commandBytes
            ))
        } catch (e: Exception) {
            Log.e(TAG, "发送VM_UPGRADE_CONNECT命令失败", e)
            methodChannel.invokeMethod("onError", mapOf(
                "message" to "发送连接命令失败: ${e.message}"
            ))
        }
    }

    /**
     * 中止升级
     */
    fun abortUpgrade(): Boolean {
        try {
            if (!isInitialized) {
                return false
            }

            Log.i(TAG, "中止升级流程")
            upgradeManager.abortUpgrade()
            return true
        } catch (e: Exception) {
            Log.e(TAG, "中止升级失败", e)
            methodChannel.invokeMethod("onError", mapOf(
                "message" to "中止升级失败: ${e.message}"
            ))
            return false
        }
    }

    /**
     * 发送确认
     */
    fun sendConfirmation(type: Int, confirm: Boolean): Boolean {
        try {
            if (!isInitialized) {
                return false
            }

            Log.i(TAG, "发送确认: 类型=$type, 确认=$confirm")
            upgradeManager.sendConfirmation(type, confirm)
            return true
        } catch (e: Exception) {
            Log.e(TAG, "发送确认失败", e)
            methodChannel.invokeMethod("onError", mapOf(
                "message" to "发送确认失败: ${e.message}"
            ))
            return false
        }
    }

    /**
     * 处理收到的GAIA数据
     */
    fun handleReceivedData(data: ByteArray) {
        try {
            if (gaiaManager.isGAIAPacket(data)) {
                val gaiaPacket = gaiaManager.getGAIAPacketFromBytes(data)

                // 检查命令类型
                val command = gaiaPacket.command and GAIA.COMMAND_MASK
                val isAck = (gaiaPacket.command and GAIA.ACKNOWLEDGMENT_MASK) != 0

                Log.d(TAG, "收到GAIA数据包: 命令=0x${command.toString(16)}, 是否ACK=$isAck, 状态=${gaiaPacket.status}")

                // 检查是否为VMU数据包
                if (gaiaManager.isVMUPacket(data)) {
                    Log.d(TAG, "识别为VMU数据包，传递给UpgradeManager")
                    val vmuPacket = gaiaManager.extractVMUPacket(data)
                    upgradeManager.receiveVMUPacket(vmuPacket)
                }
                // 处理VM_UPGRADE_CONNECT响应
                else if (command == GAIA.COMMAND_VM_UPGRADE_CONNECT && isAck) {
                    if (gaiaPacket.status == GAIA.Status.SUCCESS) {
                        Log.i(TAG, "VM_UPGRADE_CONNECT成功")
                    } else {
                        Log.e(TAG, "VM_UPGRADE_CONNECT失败: ${gaiaPacket.status}")
                        methodChannel.invokeMethod("onError", mapOf(
                            "message" to "VM升级连接失败，状态: ${gaiaPacket.status}"
                        ))
                    }
                }
                // 处理VM_UPGRADE_CONTROL响应
                else if (command == GAIA.COMMAND_VM_UPGRADE_CONTROL && isAck) {
                    if (gaiaPacket.status == GAIA.Status.SUCCESS) {
                        Log.i(TAG, "VM_UPGRADE_CONTROL成功")
                        upgradeManager.receiveVMControlSucceed()
                    } else {
                        Log.e(TAG, "VM_UPGRADE_CONTROL失败: ${gaiaPacket.status}")
                    }
                }
            } else {
                Log.w(TAG, "收到非GAIA数据包")
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理接收数据失败", e)
            methodChannel.invokeMethod("onError", mapOf(
                "message" to "处理接收数据失败: ${e.message}"
            ))
        }
    }

    // =========== UpgradeManagerListener接口实现 ===========

    /**
     * 发送升级数据包
     */
    override fun sendUpgradePacket(bytes: ByteArray?, isTransferringData: Boolean) {
        if (bytes == null) return

        try {
            // 通过GAIA协议封装VMU数据包
            val gaiaPacket = gaiaManager.createVMUPacket(bytes)

            // 将GAIA数据包发送回Flutter端进行写入
            methodChannel.invokeMethod("writeCharacteristic", mapOf(
                "uuid" to GAIA_COMMAND_CHARACTERISTIC_UUID,
                "data" to gaiaPacket
            ))
        } catch (e: Exception) {
            Log.e(TAG, "发送升级数据包失败", e)
            methodChannel.invokeMethod("onError", mapOf(
                "message" to "发送升级数据包失败: ${e.message}"
            ))
        }
    }

    /**
     * 升级过程错误
     */
    override fun onUpgradeProcessError(error: UpgradeError) {
        Log.e(TAG, "升级过程错误: ${error.getString()}")
        methodChannel.invokeMethod("onError", mapOf(
            "message" to error.getString()
        ))
    }

    /**
     * 恢复点变化
     */
    override fun onResumePointChanged(point: Int) {
        Log.i(TAG, "恢复点变化: $point")
        methodChannel.invokeMethod("onResumePointChanged", point)
    }

    /**
     * 升级完成
     */
    override fun onUpgradeFinished() {
        Log.i(TAG, "升级完成")
        methodChannel.invokeMethod("onUpgradeFinished", null)
    }

    /**
     * 文件上传进度
     */
    override fun onFileUploadProgress(percentage: Double) {
        // 仅在整数百分比变化时记录日志，减少日志量
        if (percentage % 5 < 0.1) {
            Log.i(TAG, "文件上传进度: ${percentage.toInt()}%")
        }
        methodChannel.invokeMethod("onProgress", percentage)
    }

    /**
     * 请求确认
     */
    override fun askConfirmationFor(type: Int) {
        Log.i(TAG, "请求确认: $type")
        methodChannel.invokeMethod("askConfirmation", type)
    }

    /**
     * 断开升级连接
     */
    override fun disconnectUpgrade() {
        Log.i(TAG, "断开升级连接")

        try {
            // 发送VM_UPGRADE_DISCONNECT命令
            val commandBytes = gaiaManager.createGaiaPacket(
                GAIA.VENDOR_QUALCOMM,
                GAIA.COMMAND_VM_UPGRADE_DISCONNECT,
                ByteArray(0),
                GAIA.Transport.BLE
            )

            methodChannel.invokeMethod("writeCharacteristic", mapOf(
                "uuid" to GAIA_COMMAND_CHARACTERISTIC_UUID,
                "data" to commandBytes
            ))
        } catch (e: Exception) {
            Log.e(TAG, "发送断开命令失败", e)
        }

        methodChannel.invokeMethod("onDisconnect", null)
    }
}
</file>

<file path="src/main/kotlin/com/topping/control/ToppingBleControlPlugin.kt">
package com.topping.control

import android.content.Context
import android.util.Log
import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import io.flutter.plugin.common.MethodChannel.Result

/** ToppingBleControlPlugin */
class ToppingBleControlPlugin: FlutterPlugin, MethodCallHandler {
  private val TAG = "ToppingBleControlPlugin"

  private lateinit var channel: MethodChannel
  private lateinit var context: Context
  private lateinit var otaBridge: OtaBridge

  override fun onAttachedToEngine(flutterPluginBinding: FlutterPlugin.FlutterPluginBinding) {
    Log.i(TAG, "Plugin attached to engine")
    context = flutterPluginBinding.applicationContext

    // 在主线程上创建MethodChannel
    android.os.Handler(android.os.Looper.getMainLooper()).post {
      channel = MethodChannel(flutterPluginBinding.binaryMessenger, "com.topping.ble_control/method_channel")
      channel.setMethodCallHandler(this)

      // 初始化OTA桥接对象
      otaBridge = OtaBridge(channel)
      otaBridge.initialize()

      Log.i(TAG, "Method channel registered and OTA bridge initialized")
    }
  }

  override fun onMethodCall(call: MethodCall, result: Result) {
    Log.d(TAG, "收到方法调用: ${call.method}")

    // 确保在主线程上执行方法调用
    if (android.os.Looper.myLooper() != android.os.Looper.getMainLooper()) {
      android.os.Handler(android.os.Looper.getMainLooper()).post {
        handleMethodCall(call, result)
      }
      return
    }

    handleMethodCall(call, result)
  }

  private fun handleMethodCall(call: MethodCall, result: Result) {
    when (call.method) {
      "startOtaUpgrade" -> {
        try {
          val filePath = call.argument<String>("filePath") ?: run {
            result.error("INVALID_ARGUMENT", "缺少升级文件路径", null)
            return
          }

          val maxLength = call.argument<Int>("maxLength") ?: 200
          val sendMultiplePackets = call.argument<Boolean>("sendMultiplePackets") ?: true

          Log.i(TAG, "调用OtaBridge.startUpgrade: $filePath, $maxLength, $sendMultiplePackets")
          val success = otaBridge.startUpgrade(filePath, maxLength, sendMultiplePackets)
          Log.i(TAG, "调用OtaBridge.startUpgrade结果: $success")
          result.success(success)
        } catch (e: Exception) {
          Log.e(TAG, "启动OTA升级失败", e)
          result.error("OTA_START_ERROR", "启动OTA升级失败: ${e.message}", null)
        }
      }

      "abortOtaUpgrade" -> {
        try {
          val success = otaBridge.abortUpgrade()
          result.success(success)
        } catch (e: Exception) {
          Log.e(TAG, "中止OTA升级失败", e)
          result.error("OTA_ABORT_ERROR", "中止OTA升级失败: ${e.message}", null)
        }
      }

      "sendOtaConfirmation" -> {
        try {
          val type = call.argument<Int>("type") ?: run {
            result.error("INVALID_ARGUMENT", "缺少确认类型", null)
            return
          }

          val confirm = call.argument<Boolean>("confirm") ?: true
          val success = otaBridge.sendConfirmation(type, confirm)
          result.success(success)
        } catch (e: Exception) {
          Log.e(TAG, "发送OTA确认失败", e)
          result.error("OTA_CONFIRMATION_ERROR", "发送OTA确认失败: ${e.message}", null)
        }
      }

      "handleGaiaData" -> {
        try {
          val data = call.argument<ByteArray>("data") ?: run {
            result.error("INVALID_ARGUMENT", "缺少数据", null)
            return
          }

          otaBridge.handleReceivedData(data)
          result.success(true)
        } catch (e: Exception) {
          Log.e(TAG, "处理GAIA数据失败", e)
          result.error("GAIA_DATA_ERROR", "处理GAIA数据失败: ${e.message}", null)
        }
      }

      else -> result.notImplemented()
    }
  }

  override fun onDetachedFromEngine(binding: FlutterPlugin.FlutterPluginBinding) {
    Log.i(TAG, "Plugin detached from engine")
    channel.setMethodCallHandler(null)
  }
}
</file>

<file path="src/main/AndroidManifest.xml">
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.topping.ble.control">

    <!-- 基础蓝牙权限 -->
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />

    <!-- Android 12+ (API 31+) 蓝牙权限 -->
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN"
        android:usesPermissionFlags="neverForLocation"
        tools:targetApi="s" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />

    <!-- 定位权限（蓝牙扫描可能需要） -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />

    <!-- 声明使用蓝牙功能 -->
    <uses-feature android:name="android.hardware.bluetooth" android:required="true" />
    <uses-feature android:name="android.hardware.bluetooth_le" android:required="true" />
</manifest>
</file>

<file path=".gitignore">
*.iml
.gradle
/local.properties
/.idea/workspace.xml
/.idea/libraries
.DS_Store
/build
/captures
.cxx
</file>

<file path="build.gradle">
group = "com.topping.ble.control"
version = "1.0"

buildscript {
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        // The Android Gradle Plugin knows how to build native code with the NDK.
        classpath("com.android.tools.build:gradle:4.2.2")
    }
}

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
        // 添加本地Maven仓库
        maven { url "${rootProject.projectDir}/maven-repo" }
    }
}

apply plugin: "com.android.library"

// 不使用发布脚本

android {
    namespace = "com.topping.ble.control"

    // Bumping the plugin compileSdk version requires all clients of this plugin
    // to bump the version in their app.
    compileSdk = 31

    // Use the NDK version
    // declared in /android/app/build.gradle file of the Flutter project.
    // Replace it with a version number if this plugin requires a specific NDK version.
    // (e.g. ndkVersion "23.1.7779620")
    ndkVersion = android.ndkVersion

    // Invoke the shared CMake build with the Android Gradle Plugin.
    externalNativeBuild {
        cmake {
            path = "../src/CMakeLists.txt"

            // The default CMake version for the Android Gradle Plugin is 3.10.2.
            // https://developer.android.com/studio/projects/install-ndk#vanilla_cmake
            //
            // The Flutter tooling requires that developers have CMake 3.10 or later
            // installed. You should not increase this version, as doing so will cause
            // the plugin to fail to compile for some customers of the plugin.
            // version "3.10.2"
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    defaultConfig {
        minSdk = 21
    }
   dependencies {
       // 使用libs中的jar包
       implementation fileTree(dir: 'libs', include: ['*.jar'])

       // 使用compileOnly引入aar包
       compileOnly fileTree(dir: 'libs', include: ['*.aar'])
   }

}
</file>

<file path="gradle.properties">
android.useAndroidX=true
android.enableJetifier=true
org.gradle.jvmargs=-Xmx1536M
</file>

<file path="publish-aar.gradle">
apply plugin: 'maven-publish'

// 定义发布任务
task publishGaiaLibrary(type: Exec) {
    workingDir "${rootProject.projectDir}"
    commandLine 'bash', '-c', """
        mvn install:install-file \\
        -Dfile=libs/gaialibrary-debug.aar \\
        -DgroupId=com.topping \\
        -DartifactId=gaialibrary \\
        -Dversion=1.0.0 \\
        -Dpackaging=aar \\
        -DlocalRepositoryPath=maven-repo
    """
}

task publishVmUpgradeLibrary(type: Exec) {
    workingDir "${rootProject.projectDir}"
    commandLine 'bash', '-c', """
        mvn install:install-file \\
        -Dfile=libs/vmupgradelibrary-debug.aar \\
        -DgroupId=com.topping \\
        -DartifactId=vmupgradelibrary \\
        -Dversion=1.0.0 \\
        -Dpackaging=aar \\
        -DlocalRepositoryPath=maven-repo
    """
}

// 定义一个任务来发布所有AAR
task publishAllAars {
    dependsOn publishGaiaLibrary
    dependsOn publishVmUpgradeLibrary
}
</file>

<file path="settings.gradle">
rootProject.name = 'topping_ble_control'

// 注释掉项目引用，改用AAR文件方式
// include ':gaialibrary'
// project(':gaialibrary').projectDir = new File(rootProject.projectDir, 'libs/gaialibrary')

// include ':vmupgradelibrary'
// project(':vmupgradelibrary').projectDir = new File(rootProject.projectDir, 'libs/vmupgradelibrary')
</file>

</files>
