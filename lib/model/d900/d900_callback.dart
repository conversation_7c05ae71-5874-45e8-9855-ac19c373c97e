import 'dart:ffi'; // 需要导入 ffi

import 'package:topping_ble_control/model/base/topping_device_callback.dart';
import 'package:topping_ble_control/model/ffi/ffi_d900_settings.dart';
import 'package:topping_ble_control/utils/log_util.dart';

/// D900设备回调类
class D900Callback extends ToppingDeviceCallback {
  /// 设备设置响应回调 - D900特有
  /// 接收指向FFI结构体的指针
  final Function(Pointer<FFID900Settings>) onDeviceSettingsResponse;

  /// USB选择类型变化回调 - D900特有
  final Function(int) onUsbSelectChange;

  /// USB DSD状态变化回调 - D900特有
  final Function(bool) onUsbDsdEnabledChange;

  /// IIS相位变化回调 - D900特有
  final Function(int) onIisPhaseChange;

  /// IIS通道变化回调 - D900特有
  final Function(int) onIisChannelChange;

  /// 构造函数
  D900Callback({
    required super.onScanResults,
    required super.onScanFailed,
    required super.onStateChange,
    required super.onVerifyResult,
    super.onPowerChange,
    required this.onDeviceSettingsResponse,
    super.onVolumeChange,
    super.onMuteChange,
    super.onInputTypeChange,
    super.onOutputTypeChange,
    super.onDisplayModeChange,
    super.onThemeChange,
    super.onPowerTriggerChange,
    super.onBalanceChange,
    super.onAudioBluetoothChange,
    super.onBluetoothAptxChange,
    super.onRemoteEnabledChange,
    super.onMultifunctionKeyChange,
    super.onUsbModeChange,
    this.onUsbSelectChange = _defaultOnUsbSelectChange,
    this.onUsbDsdEnabledChange = _defaultOnUsbDsdEnabledChange,
    this.onIisPhaseChange = _defaultOnIisPhaseChange,
    this.onIisChannelChange = _defaultOnIisChannelChange,
    super.onScreenBrightnessChange,
    super.onLanguageChange,
  });

  // --- D900特有的默认处理函数 ---
  static void _defaultOnUsbSelectChange(int type) =>
      Log.e("未处理: USB选择类型 $type");
  static void _defaultOnUsbDsdEnabledChange(bool enabled) =>
      Log.e("未处理: USB DSD状态 $enabled");
  static void _defaultOnIisPhaseChange(int phase) => Log.e("未处理: IIS相位 $phase");
  static void _defaultOnIisChannelChange(int channel) =>
      Log.e("未处理: IIS通道 $channel");
}
