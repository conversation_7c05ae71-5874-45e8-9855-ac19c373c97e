import 'dart:ffi';
import 'dart:convert';
import 'dart:typed_data';

import 'package:ffi/ffi.dart';
import 'package:topping_ble_control/model/base/topping_device_settings.dart';
import 'package:topping_ble_control/model/ffi/ffi_d900_settings.dart';
import 'package:topping_ble_control/utils/log_util.dart';

/// Flutter友好的D900设备设置模型类
class D900Settings extends ToppingDeviceSettings {
  /// USB选择类型，D900特有
  final int usbSelect;

  /// USB DSD启用状态，D900特有
  final bool usbDsdEnabled;

  /// IIS相位设置，D900特有
  final int iisPhase;

  /// IIS通道设置，D900特有
  final int iisChannel;

  // --- 新增字段 (D900新版功能) ---
  /// VU表0dDB幅值
  final int vuMeterLevel;

  /// VU条显示模式
  final int vuMeterDisplayMode;

  /// 输入选项位掩码
  final int inputOptions;

  /// 输出选项位掩码
  final int outputOptions;

  /// USB接口选择
  final int usbPortSelect;

  /// DSD MUTE模式
  final int dsdMute;

  /// 输出幅值
  final int outputLevel;

  /// 音量步进
  final int volumeStep;

  /// 极性设置
  final int polarity;

  /// PEQ启用状态
  final bool peqEnabled;

  /// PEQ预设选择
  final int peqPreset;

  /// DSD直通启用状态
  final bool dsdDirectEnabled;

  /// 音量记忆方式
  final int volumeMemoryMode;

  /// PEQ记忆方式
  final int peqMemoryMode;

  /// 主按键功能
  final int mainKeyFunction;

  /// 遥控A键功能
  final int remoteAKeyFunction;

  /// 遥控B键功能
  final int remoteBKeyFunction;

  /// 构造函数
  D900Settings({
    required super.isOn,
    required super.deviceName,
    required super.volume,
    required super.mute,
    required super.inputType,
    required super.outputType,
    required super.displayMode,
    required super.theme,
    required super.powerTrigger,
    required this.usbSelect,
    required super.balance,
    required super.audioBluetooth,
    required super.bluetoothAptx,
    required super.remoteEnabled,
    required super.multifunctionKey,
    required this.usbDsdEnabled,
    required super.usbMode,
    required this.iisPhase,
    required this.iisChannel,
    required super.screenBrightness,
    required super.language,
    required super.sampling,
    // --- 新增参数 (D900新版功能) ---
    required this.vuMeterLevel,
    required this.vuMeterDisplayMode,
    required this.inputOptions,
    required this.outputOptions,
    required this.usbPortSelect,
    required this.dsdMute,
    required this.outputLevel,
    required this.volumeStep,
    required this.polarity,
    required this.peqEnabled,
    required this.peqPreset,
    required this.dsdDirectEnabled,
    required this.volumeMemoryMode,
    required this.peqMemoryMode,
    required this.mainKeyFunction,
    required this.remoteAKeyFunction,
    required this.remoteBKeyFunction,
  });

  /// 根据采样率枚举值获取实际频率（Hz）
  int getSamplingRateHz() {
    return convertSamplingRateToHz(sampling);
  }

  /// 根据采样率枚举值获取用户友好的显示文本
  String getSamplingRateDisplayText() {
    return convertSamplingRateToDisplayText(sampling);
  }

  /// 将采样率枚举值转换为实际频率值（Hz）
  static int convertSamplingRateToHz(int samplingEnum) {
    Log.i("D900Settings: 将采样率枚举值 $samplingEnum 转换为Hz值");
    int result = 0;
    switch (samplingEnum) {
      case 0:
        result = 0;
        break; // 错误状态
      case 1:
        result = 44100;
        break;
      case 2:
        result = 48000;
        break;
      case 3:
        result = 88200;
        break;
      case 4:
        result = 96000;
        break;
      case 5:
        result = 176400;
        break;
      case 6:
        result = 192000;
        break;
      case 7:
        result = 352800;
        break;
      case 8:
        result = 384000;
        break;
      case 9:
        result = 705600;
        break;
      case 10:
        result = 768000;
        break;
      case 11:
        result = 2822400;
        break; // DSD64
      case 12:
        result = 5644800;
        break; // DSD128
      case 13:
        result = 11289600;
        break; // DSD256
      case 14:
        result = 22579200;
        break; // DSD512
      case 15:
        result = 45158400;
        break; // DSD1024
      case 16:
        result = 49152000;
        break;
      default:
        result = 0;
        break;
    }
    Log.i("D900Settings: 采样率 $samplingEnum 转换为Hz: $result");
    return result;
  }

  /// 将采样率枚举值转换为用户友好的显示字符串
  static String convertSamplingRateToDisplayText(int samplingEnum) {
    Log.i("D900Settings: 转换采样率枚举值 $samplingEnum 为显示文本");
    String result = "";
    switch (samplingEnum) {
      case 0:
        result = "--";
        break; // 错误状态或未初始化，显示为"--"而非"错误"
      case 1:
        result = "44.1kHz";
        break;
      case 2:
        result = "48kHz";
        break;
      case 3:
        result = "88.2kHz";
        break;
      case 4:
        result = "96kHz";
        break;
      case 5:
        result = "176.4kHz";
        break;
      case 6:
        result = "192kHz";
        break;
      case 7:
        result = "352.8kHz";
        break;
      case 8:
        result = "384kHz";
        break;
      case 9:
        result = "705.6kHz";
        break;
      case 10:
        result = "768kHz";
        break;
      case 11:
        result = "2.82MHz (DSD64)";
        break;
      case 12:
        result = "5.64MHz (DSD128)";
        break;
      case 13:
        result = "11.28MHz (DSD256)";
        break;
      case 14:
        result = "22.57MHz (DSD512)";
        break;
      case 15:
        result = "45.15MHz (DSD1024)";
        break;
      case 16:
        result = "49.15MHz";
        break;
      default:
        result = "未知采样率";
        break;
    }
    Log.i("D900Settings: 采样率 $samplingEnum 转换结果: '$result'");
    return result;
  }

  /// 从FFI设置构造
  factory D900Settings.fromFFI(Pointer<FFID900Settings> ffiSettingsPtr) {
    // 检查指针是否为空
    if (ffiSettingsPtr == nullptr) {
      Log.e("从FFI构造D900Settings失败：传入的指针为空");
      // 返回一个安全的默认值
      return _defaultSettings("空指针错误");
    }

    // 解引用指针获取引用
    final ffiSettings = ffiSettingsPtr.ref;

    try {
      Log.i("开始从FFI设置构造: ${ffiSettingsPtr.address} ffiSettings: $ffiSettings");

      // --- 读取deviceName (char[32]) ---
      final nameBytes = Uint8List(32);
      for (int i = 0; i < 32; i++) {
        nameBytes[i] = ffiSettings.device_name[i];
      }

      // 打印设备名称字节数组的十六进制表示
      String hexBytes = nameBytes
          .map((b) => b.toRadixString(16).padLeft(2, '0'))
          .join(' ');
      Log.i("设备名称原始字节(D900Settings): $hexBytes");

      // 使用默认设备名称"D900"，因为C++结构体中没有device_name字段
      String name = ToppingDeviceSettings.readDeviceNameFromFFI(
        nameBytes,
        defaultDeviceName: "D900 TEST",
      );
      Log.i("D900Settings中解码后的deviceName: '$name'");

      // --- 读取其他字段 ---
      // 使用try-catch包裹每个字段读取，增加健壮性
      int readVolume = 0;
      try {
        readVolume = ffiSettings.volume;
      } catch (e) {
        Log.e("读取volume失败: $e");
      }
      bool readMute = false;
      try {
        readMute = ffiSettings.is_mute != 0;
      } catch (e) {
        Log.e("读取is_mute失败: $e");
      }
      int readInputType = 0;
      try {
        readInputType = ffiSettings.input_type;
      } catch (e) {
        Log.e("读取input_type失败: $e");
      }
      int readOutputType = 0;
      try {
        readOutputType = ffiSettings.output_type;
      } catch (e) {
        Log.e("读取output_type失败: $e");
      }
      int readDisplayMode = 0;
      try {
        readDisplayMode = ffiSettings.display_mode;
      } catch (e) {
        Log.e("读取display_mode失败: $e");
      }
      int readTheme = 0;
      try {
        readTheme = ffiSettings.theme;
      } catch (e) {
        Log.e("读取theme失败: $e");
      }
      int readPowerTrigger = 0;
      try {
        readPowerTrigger = ffiSettings.power_trigger;
      } catch (e) {
        Log.e("读取power_trigger失败: $e");
      }
      int readUsbSelect = 0;
      try {
        readUsbSelect = ffiSettings.usb_select;
      } catch (e) {
        Log.e("读取usb_select失败: $e");
      }
      int readBalance = 0;
      try {
        readBalance = ffiSettings.balance;
      } catch (e) {
        Log.e("读取balance失败: $e");
      }
      bool readAudioBluetooth = false;
      try {
        readAudioBluetooth = ffiSettings.audio_bt_enable != 0;
      } catch (e) {
        Log.e("读取audio_bt_enable失败: $e");
      }
      bool readBluetoothAptx = false;
      try {
        readBluetoothAptx = ffiSettings.aptx_enable != 0;
      } catch (e) {
        Log.e("读取aptx_enable失败: $e");
      }
      bool readRemoteEnabled = false;
      try {
        readRemoteEnabled = ffiSettings.remote_enable != 0;
      } catch (e) {
        Log.e("读取remote_enable失败: $e");
      }
      int readMultifunctionKey = 0;
      try {
        readMultifunctionKey = ffiSettings.multifunction_key;
      } catch (e) {
        Log.e("读取multifunction_key失败: $e");
      }
      bool readUsbDsdEnabled = false;
      try {
        readUsbDsdEnabled = ffiSettings.usb_dsd_enable != 0;
      } catch (e) {
        Log.e("读取usb_dsd_enable失败: $e");
      }
      int readUsbMode = 0;
      try {
        readUsbMode = ffiSettings.usb_mode;
      } catch (e) {
        Log.e("读取usb_mode失败: $e");
      }
      int readIisPhase = 0;
      try {
        readIisPhase = ffiSettings.iis_phase;
      } catch (e) {
        Log.e("读取iis_phase失败: $e");
      }
      int readIisChannel = 0;
      try {
        readIisChannel = ffiSettings.iis_channel;
      } catch (e) {
        Log.e("读取iis_channel失败: $e");
      }
      int readScreenBrightness = 0;
      try {
        readScreenBrightness = ffiSettings.screen_brightness;
      } catch (e) {
        Log.e("读取screen_brightness失败: $e");
      }
      int readLanguage = 0;
      try {
        readLanguage = ffiSettings.language;
      } catch (e) {
        Log.e("读取language失败: $e");
      }
      int readSampling = 0;
      try {
        readSampling = ffiSettings.sampling;
        Log.i("D900Settings.fromFFI: 读取到采样率枚举值: $readSampling");
      } catch (e) {
        Log.e("读取sampling失败: $e");
      }
      bool readIsOn = false;
      try {
        readIsOn = ffiSettings.is_on != 0;
      } catch (e) {
        Log.e("读取is_on失败: $e");
      }

      // --- 读取新增字段 (D900新版功能) ---
      int readVuMeterLevel = 0;
      try {
        readVuMeterLevel = ffiSettings.vu_meter_level;
      } catch (e) {
        Log.e("读取vu_meter_level失败: $e");
      }
      int readVuMeterDisplayMode = 0;
      try {
        readVuMeterDisplayMode = ffiSettings.vu_meter_display_mode;
      } catch (e) {
        Log.e("读取vu_meter_display_mode失败: $e");
      }
      int readInputOptions = 0;
      try {
        readInputOptions = ffiSettings.input_options;
      } catch (e) {
        Log.e("读取input_options失败: $e");
      }
      int readOutputOptions = 0;
      try {
        readOutputOptions = ffiSettings.output_options;
      } catch (e) {
        Log.e("读取output_options失败: $e");
      }
      int readUsbPortSelect = 0;
      try {
        readUsbPortSelect = ffiSettings.usb_port_select;
      } catch (e) {
        Log.e("读取usb_port_select失败: $e");
      }
      int readDsdMute = 0;
      try {
        readDsdMute = ffiSettings.dsd_mute;
      } catch (e) {
        Log.e("读取dsd_mute失败: $e");
      }
      int readOutputLevel = 0;
      try {
        readOutputLevel = ffiSettings.output_level;
      } catch (e) {
        Log.e("读取output_level失败: $e");
      }
      int readVolumeStep = 0;
      try {
        readVolumeStep = ffiSettings.volume_step;
      } catch (e) {
        Log.e("读取volume_step失败: $e");
      }
      int readPolarity = 0;
      try {
        readPolarity = ffiSettings.polarity;
      } catch (e) {
        Log.e("读取polarity失败: $e");
      }
      bool readPeqEnabled = false;
      try {
        readPeqEnabled = ffiSettings.peq_enable != 0;
      } catch (e) {
        Log.e("读取peq_enable失败: $e");
      }
      int readPeqPreset = 0;
      try {
        readPeqPreset = ffiSettings.peq_preset;
      } catch (e) {
        Log.e("读取peq_preset失败: $e");
      }
      bool readDsdDirectEnabled = false;
      try {
        readDsdDirectEnabled = ffiSettings.dsd_direct_enable != 0;
      } catch (e) {
        Log.e("读取dsd_direct_enable失败: $e");
      }
      int readVolumeMemoryMode = 0;
      try {
        readVolumeMemoryMode = ffiSettings.volume_memory_mode;
      } catch (e) {
        Log.e("读取volume_memory_mode失败: $e");
      }
      int readPeqMemoryMode = 0;
      try {
        readPeqMemoryMode = ffiSettings.peq_memory_mode;
      } catch (e) {
        Log.e("读取peq_memory_mode失败: $e");
      }
      int readMainKeyFunction = 0;
      try {
        readMainKeyFunction = ffiSettings.main_key_function;
      } catch (e) {
        Log.e("读取main_key_function失败: $e");
      }
      int readRemoteAKeyFunction = 0;
      try {
        readRemoteAKeyFunction = ffiSettings.remote_a_key_function;
      } catch (e) {
        Log.e("读取remote_a_key_function失败: $e");
      }
      int readRemoteBKeyFunction = 0;
      try {
        readRemoteBKeyFunction = ffiSettings.remote_b_key_function;
      } catch (e) {
        Log.e("读取remote_b_key_function失败: $e");
      }

      return D900Settings(
        isOn: readIsOn,
        deviceName: name,
        volume: readVolume,
        mute: readMute,
        inputType: readInputType,
        outputType: readOutputType,
        displayMode: readDisplayMode,
        theme: readTheme,
        powerTrigger: readPowerTrigger,
        usbSelect: readUsbSelect,
        balance: readBalance,
        audioBluetooth: readAudioBluetooth,
        bluetoothAptx: readBluetoothAptx,
        remoteEnabled: readRemoteEnabled,
        multifunctionKey: readMultifunctionKey,
        usbDsdEnabled: readUsbDsdEnabled,
        usbMode: readUsbMode,
        iisPhase: readIisPhase,
        iisChannel: readIisChannel,
        screenBrightness: readScreenBrightness,
        language: readLanguage,
        sampling: readSampling,
        // --- 新增字段 (D900新版功能) ---
        vuMeterLevel: readVuMeterLevel,
        vuMeterDisplayMode: readVuMeterDisplayMode,
        inputOptions: readInputOptions,
        outputOptions: readOutputOptions,
        usbPortSelect: readUsbPortSelect,
        dsdMute: readDsdMute,
        outputLevel: readOutputLevel,
        volumeStep: readVolumeStep,
        polarity: readPolarity,
        peqEnabled: readPeqEnabled,
        peqPreset: readPeqPreset,
        dsdDirectEnabled: readDsdDirectEnabled,
        volumeMemoryMode: readVolumeMemoryMode,
        peqMemoryMode: readPeqMemoryMode,
        mainKeyFunction: readMainKeyFunction,
        remoteAKeyFunction: readRemoteAKeyFunction,
        remoteBKeyFunction: readRemoteBKeyFunction,
      );
    } catch (e, s) {
      Log.e("从FFI创建D900Settings时发生严重错误: $e\nStack trace:\n$s");
      return _defaultSettings("构造函数错误");
    }
  }

  /// 转换为FFI设置
  Pointer<FFID900Settings> toFFI() {
    final ffiSettingsPtr = calloc<FFID900Settings>();
    try {
      final ffiSettings = ffiSettingsPtr.ref;

      // 写入deviceName，直接使用循环写入而不用cast
      final nameBytes = utf8.encode(deviceName);
      final List<int> nameBytesPadded = List.filled(32, 0); // 用0填充
      final lengthToCopy =
          nameBytes.length < 32 ? nameBytes.length : 31; // 确保'\0'

      for (int i = 0; i < lengthToCopy; i++) {
        nameBytesPadded[i] = nameBytes[i];
      }

      // 将Dart List<int>写入FFI
      for (int i = 0; i < 32; i++) {
        ffiSettings.device_name[i] = nameBytesPadded[i];
      }

      // 写入其他字段
      ffiSettings.is_on = isOn ? 1 : 0;
      ffiSettings.volume = volume;
      ffiSettings.is_mute = mute ? 1 : 0;
      ffiSettings.input_type = inputType;
      ffiSettings.output_type = outputType;
      ffiSettings.display_mode = displayMode;
      ffiSettings.theme = theme;
      ffiSettings.power_trigger = powerTrigger;
      ffiSettings.usb_select = usbSelect;
      ffiSettings.balance = balance;
      ffiSettings.audio_bt_enable = audioBluetooth ? 1 : 0;
      ffiSettings.aptx_enable = bluetoothAptx ? 1 : 0;
      ffiSettings.remote_enable = remoteEnabled ? 1 : 0;
      ffiSettings.multifunction_key = multifunctionKey;
      ffiSettings.usb_dsd_enable = usbDsdEnabled ? 1 : 0;
      ffiSettings.usb_mode = usbMode;
      ffiSettings.iis_phase = iisPhase;
      ffiSettings.iis_channel = iisChannel;
      ffiSettings.screen_brightness = screenBrightness;
      ffiSettings.language = language;
      ffiSettings.sampling = sampling;

      // --- 写入新增字段 (D900新版功能) ---
      ffiSettings.vu_meter_level = vuMeterLevel;
      ffiSettings.vu_meter_display_mode = vuMeterDisplayMode;
      ffiSettings.input_options = inputOptions;
      ffiSettings.output_options = outputOptions;
      ffiSettings.usb_port_select = usbPortSelect;
      ffiSettings.dsd_mute = dsdMute;
      ffiSettings.output_level = outputLevel;
      ffiSettings.volume_step = volumeStep;
      ffiSettings.polarity = polarity;
      ffiSettings.peq_enable = peqEnabled ? 1 : 0;
      ffiSettings.peq_preset = peqPreset;
      ffiSettings.dsd_direct_enable = dsdDirectEnabled ? 1 : 0;
      ffiSettings.volume_memory_mode = volumeMemoryMode;
      ffiSettings.peq_memory_mode = peqMemoryMode;
      ffiSettings.main_key_function = mainKeyFunction;
      ffiSettings.remote_a_key_function = remoteAKeyFunction;
      ffiSettings.remote_b_key_function = remoteBKeyFunction;

      return ffiSettingsPtr;
    } catch (e) {
      Log.e("转换为FFI设置时出错: $e");
      calloc.free(ffiSettingsPtr); // 出错时释放已分配的内存
      rethrow; // 重新抛出异常
    }
  }

  /// 释放由toFFI创建的FFI指针
  static void freeCreatedFFIPointer(Pointer<FFID900Settings> ffiSettingsPtr) {
    if (ffiSettingsPtr != nullptr) {
      calloc.free(ffiSettingsPtr);
    }
  }

  /// 重写copy方法创建副本
  @override
  D900Settings copy() {
    return D900Settings(
      isOn: isOn,
      deviceName: deviceName,
      volume: volume,
      mute: mute,
      inputType: inputType,
      outputType: outputType,
      displayMode: displayMode,
      theme: theme,
      powerTrigger: powerTrigger,
      usbSelect: usbSelect,
      balance: balance,
      audioBluetooth: audioBluetooth,
      bluetoothAptx: bluetoothAptx,
      remoteEnabled: remoteEnabled,
      multifunctionKey: multifunctionKey,
      usbDsdEnabled: usbDsdEnabled,
      usbMode: usbMode,
      iisPhase: iisPhase,
      iisChannel: iisChannel,
      screenBrightness: screenBrightness,
      language: language,
      sampling: sampling,
      // --- 新增字段 (D900新版功能) ---
      vuMeterLevel: vuMeterLevel,
      vuMeterDisplayMode: vuMeterDisplayMode,
      inputOptions: inputOptions,
      outputOptions: outputOptions,
      usbPortSelect: usbPortSelect,
      dsdMute: dsdMute,
      outputLevel: outputLevel,
      volumeStep: volumeStep,
      polarity: polarity,
      peqEnabled: peqEnabled,
      peqPreset: peqPreset,
      dsdDirectEnabled: dsdDirectEnabled,
      volumeMemoryMode: volumeMemoryMode,
      peqMemoryMode: peqMemoryMode,
      mainKeyFunction: mainKeyFunction,
      remoteAKeyFunction: remoteAKeyFunction,
      remoteBKeyFunction: remoteBKeyFunction,
    );
  }

  /// 将对象转换为字符串用于调试
  @override
  String toString() {
    return super.toString().replaceFirst(
      '}',
      ', '
          'usbSelect: $usbSelect, '
          'usbDsdEnabled: $usbDsdEnabled, '
          'iisPhase: $iisPhase, '
          'iisChannel: $iisChannel, '
          'vuMeterLevel: $vuMeterLevel, '
          'vuMeterDisplayMode: $vuMeterDisplayMode, '
          'inputOptions: $inputOptions, '
          'outputOptions: $outputOptions, '
          'usbPortSelect: $usbPortSelect, '
          'dsdMute: $dsdMute, '
          'outputLevel: $outputLevel, '
          'volumeStep: $volumeStep, '
          'polarity: $polarity, '
          'peqEnabled: $peqEnabled, '
          'peqPreset: $peqPreset, '
          'dsdDirectEnabled: $dsdDirectEnabled, '
          'volumeMemoryMode: $volumeMemoryMode, '
          'peqMemoryMode: $peqMemoryMode, '
          'mainKeyFunction: $mainKeyFunction, '
          'remoteAKeyFunction: $remoteAKeyFunction, '
          'remoteBKeyFunction: $remoteBKeyFunction}',
    );
  }

  /// 提供默认设置的私有辅助方法
  static D900Settings _defaultSettings(String errorContext) {
    Log.w("返回默认D900Settings，错误上下文: $errorContext");
    return D900Settings(
      isOn: false,
      deviceName: "默认名称 ($errorContext)",
      volume: -1, // 使用特殊值表示错误/默认
      mute: false,
      inputType: 0,
      outputType: 0,
      displayMode: 0,
      theme: 0,
      powerTrigger: 0,
      usbSelect: 0,
      balance: 0,
      audioBluetooth: false,
      bluetoothAptx: false,
      remoteEnabled: false,
      multifunctionKey: 0,
      usbDsdEnabled: false,
      usbMode: 0,
      iisPhase: 0,
      iisChannel: 0,
      screenBrightness: 0,
      language: 0,
      sampling: 0,
      // --- 新增字段默认值 (D900新版功能) ---
      vuMeterLevel: 0,
      vuMeterDisplayMode: 0,
      inputOptions: 0,
      outputOptions: 0,
      usbPortSelect: 0,
      dsdMute: 0,
      outputLevel: 0,
      volumeStep: 0,
      polarity: 0,
      peqEnabled: false,
      peqPreset: 0,
      dsdDirectEnabled: false,
      volumeMemoryMode: 0,
      peqMemoryMode: 0,
      mainKeyFunction: 0,
      remoteAKeyFunction: 0,
      remoteBKeyFunction: 0,
    );
  }
}
