import 'dart:io';
import 'dart:convert';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:topping_ble_control/utils/log_util.dart';

/// 简单的蓝牙通信日志管理器
class BluetoothLogManager {
  static final BluetoothLogManager _instance = BluetoothLogManager._internal();
  static BluetoothLogManager get instance => _instance;

  // 私有构造函数
  BluetoothLogManager._internal();

  // 日志时间格式
  static final DateFormat _timeFormatter = DateFormat(
    'yyyy-MM-dd HH:mm:ss.SSS',
  );

  // 文件名格式
  static final DateFormat _fileNameFormatter = DateFormat('yyyy-MM-dd');

  // 最大保留日志天数
  static const int _retentionDays = 7;

  // 验证会话计数器，用于确保会话ID唯一性
  int _verifySessionCounter = 0;

  /// 生成唯一的验证会话ID
  String generateVerifySessionId() {
    // 使用当前时间戳和计数器生成唯一ID
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final sessionId = "${timestamp}_${_verifySessionCounter++}";
    return sessionId;
  }

  /// 记录验证开始
  Future<void> logVerifyStart(String sessionId) async {
    final String message = '[VERIFY] 🔍 ==== 验证开始 ==== 🔍 [会话ID: $sessionId]';
    await _writeLog(message);
    await _writeVerifyLog(message);
  }

  /// 记录验证结束
  Future<void> logVerifyEnd(String sessionId, {bool? success}) async {
    String resultSymbol = success == null ? '✓' : (success ? '✅' : '❌');
    final String message =
        '[VERIFY] $resultSymbol ==== 验证结束 ==== $resultSymbol [会话ID: $sessionId]';
    await _writeLog(message);
    await _writeVerifyLog(message);
  }

  /// 记录验证数据
  Future<void> logVerifyData(
    String action,
    String sessionId,
    String additionalInfo,
  ) async {
    // 仅记录操作类型和会话ID
    // 数据内容由VerifyInterceptor直接用writeRawVerifyLog记录
    final String message =
        '[VERIFY] 🔄 验证数据($action) [会话ID: $sessionId]${additionalInfo.isNotEmpty ? ' [$additionalInfo]' : ''}';
    await _writeLog(message);
    await _writeVerifyLog(message);
  }

  /// 记录验证结果
  Future<void> logVerifyResult(
    String sessionId,
    bool success, {
    String? reason,
    int? stage,
  }) async {
    final String resultSymbol = success ? '✅' : '❌';
    final String result = success ? '成功' : '失败';
    String message = '[VERIFY] $resultSymbol 验证结果: $result [会话ID: $sessionId]';

    if (stage != null) {
      message += ' [阶段$stage]';
    }

    if (reason != null) {
      message += '\n- 原因: $reason';
    }

    await _writeLog(message);
    await _writeVerifyLog(message);
  }

  /// 直接写入验证日志数据（用于详细数据内容）
  Future<void> writeRawVerifyLog(String message) async {
    try {
      // 获取当前时间
      final String timestamp = _timeFormatter.format(DateTime.now());

      // 为长的十六进制数据添加额外的格式处理
      String formattedMessage = message;

      // 如果包含"数据内容"和十六进制字符，可能是长数据，添加格式化
      if (message.contains("数据内容") && message.contains(":")) {
        final parts = message.split(":");
        if (parts.length > 1) {
          // 保留原始消息前缀
          final prefix = parts[0] + ":";
          // 提取十六进制数据部分，去除前导空格
          final hexData = parts.sublist(1).join(":").trim();

          // 每32个字符（16个字节）添加一个换行，提高可读性
          final buffer = StringBuffer();
          buffer.write(prefix);
          buffer.write("\n  "); // 缩进换行后的内容

          // 每一小段数据添加空格，提高可读性
          int count = 0;
          for (int i = 0; i < hexData.length; i++) {
            buffer.write(hexData[i]);
            count++;
            if ((count % 48 == 0 || count % 96 == 0) &&
                i < hexData.length - 1) {
              buffer.write("\n  "); // 每24个字节一行，添加缩进
            }
          }

          formattedMessage = buffer.toString();
        }
      }

      // 确保每条日志都是完整的一行，在末尾添加额外的换行符作为分隔
      final String logEntry = '[$timestamp] $formattedMessage\n\n';

      // 获取验证日志文件
      final File logFile = await _getVerifyLogFile();

      // 安全检查：确保文件对象有效，如果无效则打印错误并返回
      if (logFile == null) {
        Log.e('验证日志文件对象为空，无法写入日志');
        return;
      }

      // 写入日志 - 使用FileMode.append确保不会覆盖已有内容，flush:true确保立即写入
      await logFile.writeAsString(logEntry, mode: FileMode.append, flush: true);
    } catch (e) {
      Log.e('写入验证日志详细数据失败: $e');
    }
  }

  /// 删除当前验证日志文件
  Future<void> deleteVerifyLogFile() async {
    try {
      final File verifyLogFile = await _getVerifyLogFile();
      if (await verifyLogFile.exists()) {
        await verifyLogFile.delete();
        Log.i('已删除旧的验证日志文件');
      }
    } catch (e) {
      Log.e('删除验证日志文件失败: $e');
    }
  }

  /// 获取验证日志文件
  Future<File> _getVerifyLogFile() async {
    final Directory appDocDir = await getApplicationDocumentsDirectory();
    final String logDirPath = '${appDocDir.path}/verify_logs';

    // 确保目录存在
    final Directory logDir = Directory(logDirPath);
    if (!await logDir.exists()) {
      await logDir.create(recursive: true);
      Log.i('创建验证日志目录: $logDirPath');
    }

    // 验证日志使用固定名称，每次会话开始时重置
    const String fileName = 'verify_session.log';
    return File('$logDirPath/$fileName');
  }

  /// 写入验证日志到专用文件
  Future<void> _writeVerifyLog(String message) async {
    try {
      // 获取当前时间
      final String timestamp = _timeFormatter.format(DateTime.now());
      // 确保每条日志都是完整的一行，在末尾明确添加换行符
      final String logEntry = '[$timestamp] $message\n';

      // 获取验证日志文件
      final File logFile = await _getVerifyLogFile();

      // 写入日志 - 使用FileMode.append确保不会覆盖已有内容，flush:true确保立即写入
      await logFile.writeAsString(logEntry, mode: FileMode.append, flush: true);
    } catch (e) {
      Log.e('写入验证日志失败: $e');
    }
  }

  /// 记录发送的数据
  Future<void> logSent(String deviceName, dynamic data) async {
    String message;

    // 如果是特征值数据且格式是复杂的，则特殊处理
    if (deviceName.contains("特征值") &&
        data is String &&
        data.contains("十六进制:")) {
      // 从数据中提取十六进制部分和数据长度部分
      final hexMatch = RegExp(r'十六进制: (.*?)\n').firstMatch(data);
      final lengthMatch = RegExp(r'数据长度: (\d+)').firstMatch(data);

      if (hexMatch != null && lengthMatch != null) {
        final hexString = hexMatch.group(1) ?? "";
        final dataLength = lengthMatch.group(1) ?? "";

        // 构建更简洁的消息
        message = '发送到[$deviceName]: $hexString - 数据长度: $dataLength 字节';
      } else {
        message = '发送到[$deviceName]: $data';
      }
    } else {
      message = '发送到[$deviceName]: $data';
    }

    await _writeLog(message);
  }

  /// 记录接收的数据
  Future<void> logReceived(String deviceName, dynamic data) async {
    String message;

    // 如果是特征值数据且格式是复杂的，则特殊处理
    if (deviceName.contains("特征值") &&
        data is String &&
        data.contains("十六进制:")) {
      // 从数据中提取十六进制部分和数据长度部分
      final hexMatch = RegExp(r'十六进制: (.*?)\n').firstMatch(data);
      final lengthMatch = RegExp(r'数据长度: (\d+)').firstMatch(data);

      if (hexMatch != null && lengthMatch != null) {
        final hexString = hexMatch.group(1) ?? "";
        final dataLength = lengthMatch.group(1) ?? "";

        // 构建更简洁的消息
        message = '接收自[$deviceName]: $hexString - 数据长度: $dataLength 字节';
      } else {
        message = '接收自[$deviceName]: $data';
      }
    } else {
      message = '接收自[$deviceName]: $data';
    }

    await _writeLog(message);
  }

  /// 记录命令执行
  Future<void> logCommand(String command) async {
    final String message = '执行命令: $command';
    await _writeLog(message);
  }

  /// 记录连接状态变化
  Future<void> logConnectionState(String deviceName, String state) async {
    final String message = '设备[$deviceName] 连接状态: $state';
    await _writeLog(message);
  }

  /// 记录错误信息
  Future<void> logError(String message, [dynamic error]) async {
    final String errorMsg = error != null ? '$message: $error' : message;
    await _writeLog('错误: $errorMsg');
  }

  /// 记录日志信息
  Future<void> logInfo(String message) async {
    await _writeLog(message);
  }

  /// 写入日志到文件
  Future<void> _writeLog(String message) async {
    try {
      // 获取当前时间
      final String timestamp = _timeFormatter.format(DateTime.now());
      final String logEntry = '[$timestamp] $message\n';

      // 记录到系统日志
      Log.i(message);

      // 获取日志文件
      final File logFile = await _getLogFile();

      // 写入日志
      await logFile.writeAsString(logEntry, mode: FileMode.append);
    } catch (e) {
      Log.e('写入日志失败: $e');
    }
  }

  /// 确保日志目录存在
  Future<void> ensureLogDirectoryExists() async {
    try {
      final Directory appDocDir = await getApplicationDocumentsDirectory();
      final String logDirPath = '${appDocDir.path}/ble_logs';

      // 确保目录存在
      final Directory logDir = Directory(logDirPath);
      if (!await logDir.exists()) {
        await logDir.create(recursive: true);
        Log.i('创建蓝牙日志目录: $logDirPath');
      }
    } catch (e) {
      Log.e('确保日志目录存在失败: $e');
    }
  }

  /// 获取当前日志文件
  Future<File> _getLogFile() async {
    final Directory appDocDir = await getApplicationDocumentsDirectory();
    final String logDirPath = '${appDocDir.path}/ble_logs';

    // 确保目录存在
    await ensureLogDirectoryExists();

    // 以当天日期命名日志文件
    final String fileName =
        'ble_log_${_fileNameFormatter.format(DateTime.now())}.txt';
    return File('$logDirPath/$fileName');
  }

  /// 获取所有日志文件
  Future<List<File>> getAllLogFiles() async {
    try {
      final Directory appDocDir = await getApplicationDocumentsDirectory();
      final String logDirPath = '${appDocDir.path}/ble_logs';

      final Directory logDir = Directory(logDirPath);
      if (!await logDir.exists()) {
        return [];
      }

      List<FileSystemEntity> entities = await logDir.list().toList();
      return entities
          .whereType<File>()
          .where((file) => file.path.endsWith('.txt'))
          .toList();
    } catch (e) {
      Log.e('获取日志文件列表失败: $e');
      return [];
    }
  }

  /// 按日期获取日志文件
  Future<Map<DateTime, List<File>>> getLogFilesByDate() async {
    try {
      final List<File> allFiles = await getAllLogFiles();
      final Map<DateTime, List<File>> filesByDate = {};

      for (final file in allFiles) {
        final fileName = file.path.split('/').last;
        // 从文件名中提取日期
        final RegExp dateRegExp = RegExp(r'ble_log_(\d{4}-\d{2}-\d{2})');
        final Match? match = dateRegExp.firstMatch(fileName);

        if (match != null) {
          final String dateStr = match.group(1)!;
          try {
            final DateTime fileDate = DateFormat('yyyy-MM-dd').parse(dateStr);
            // 将日期时间部分设为0以便按天分组
            final DateTime dateKey = DateTime(
              fileDate.year,
              fileDate.month,
              fileDate.day,
            );

            if (!filesByDate.containsKey(dateKey)) {
              filesByDate[dateKey] = [];
            }
            filesByDate[dateKey]!.add(file);
          } catch (e) {
            Log.e('解析日志文件日期失败: $e');
          }
        }
      }

      return filesByDate;
    } catch (e) {
      Log.e('按日期获取日志文件失败: $e');
      return {};
    }
  }

  /// 清除过期日志（保留最近几天）
  Future<void> cleanupOldLogs({int retentionDays = _retentionDays}) async {
    try {
      final List<File> logFiles = await getAllLogFiles();
      final DateTime now = DateTime.now();

      for (File file in logFiles) {
        final String fileName = file.path.split('/').last;
        // 从文件名中提取日期
        final RegExp dateRegExp = RegExp(r'ble_log_(\d{4}-\d{2}-\d{2})\.txt');
        final Match? match = dateRegExp.firstMatch(fileName);

        if (match != null) {
          final String dateStr = match.group(1)!;
          try {
            final DateTime fileDate = DateFormat('yyyy-MM-dd').parse(dateStr);
            final int daysDifference = now.difference(fileDate).inDays;

            if (daysDifference > retentionDays) {
              await file.delete();
              Log.i('已删除过期日志文件: $fileName');
            }
          } catch (e) {
            Log.e('解析日期失败: $e');
          }
        }
      }
    } catch (e) {
      Log.e('清理过期日志失败: $e');
    }
  }

  /// 读取日志文件内容
  Future<String> readLogFile(File file) async {
    try {
      // 尝试读取文件字节
      final bytes = await file.readAsBytes();

      try {
        // 尝试UTF-8解码
        return utf8.decode(bytes, allowMalformed: true);
      } catch (e) {
        // 如果UTF-8解码失败，显示十六进制内容
        Log.e('UTF-8解码失败: $e');

        final hexView = bytes
            .take(2048)
            .map((b) => b.toRadixString(16).padLeft(2, '0'))
            .join(' ');
        return '''
文件编码无法正确识别，显示前 2KB 的十六进制内容：

$hexView

...

(文件总大小: ${(bytes.length / 1024).toStringAsFixed(2)} KB)
''';
      }
    } catch (e) {
      Log.e('读取日志文件失败: $e');
      return '日志读取失败: $e';
    }
  }

  /// 删除日志文件
  Future<bool> deleteLogFile(File file) async {
    try {
      if (await file.exists()) {
        await file.delete();
        return true;
      }
      return false;
    } catch (e) {
      Log.e('删除日志文件失败: $e');
      return false;
    }
  }

  /// 删除所有日志文件
  Future<bool> deleteAllLogFiles() async {
    try {
      final List<File> files = await getAllLogFiles();

      for (File file in files) {
        try {
          await file.delete();
        } catch (e) {
          Log.e('删除日志文件失败: ${file.path}, 错误: $e');
          return false;
        }
      }

      return true;
    } catch (e) {
      Log.e('删除所有日志文件失败: $e');
      return false;
    }
  }
}
