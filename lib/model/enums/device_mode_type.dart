import 'package:topping_ble_control/utils/log_util.dart';

/// 设备类型枚举
enum DeviceModeType {
  dx5, // DX5 II
  dx9, // 900
  // 添加更多设备类型
  unknown;

  factory DeviceModeType.fromString(String name) {
    // 将输入字符串转换为小写
    final lowerName = name.toLowerCase();

    // 尝试匹配枚举值的名称
    var firstWhere = values.firstWhere(
      (e) => e.name == lowerName,
      orElse: () => DeviceModeType.unknown,
    );
    Log.i("尝试匹配枚举值的名称: name: $name, firstWhere: $firstWhere");
    return firstWhere;
  }
}
