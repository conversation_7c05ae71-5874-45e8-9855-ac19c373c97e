Pod::Spec.new do |s|
  s.name             = 'topping_ble_control'
  s.version          = '0.0.1'
  s.summary          = 'A Flutter plugin for Topping BLE control'
  s.description      = <<-DESC
A Flutter plugin that provides functionality for controlling Topping devices via BLE.
                       DESC
  s.homepage         = 'http://example.com'
  s.license          = { :type => 'MIT', :file => '../LICENSE' }
  s.author           = { 'Your Company' => '<EMAIL>' }

  s.source           = { :path => '.' }

  # 使用项目内的库文件
  s.source_files = [
    'Classes/**/*',
    'Libraries/GaiaLibrary/*.{h,m}',
    'Libraries/BTLibrary/*.{h,m}',
    '../src/topping_controller/core/**/*.{h,hpp,cpp}',
    '../src/topping_controller/devices/dx5ii/*.{h,cpp}',
    '../src/topping_controller/devices/d900/*.{h,cpp}',
    '../src/topping_controller/devices/flutter/*.{h,cpp}'
  ]

  # 公共头文件
  s.public_header_files = [
    'Classes/*.h',
    'Libraries/GaiaLibrary/*.h',
    'Libraries/BTLibrary/*.h',
    '../src/topping_controller/core/protocol/ControllerScanner.h',
    '../src/topping_controller/core/protocol/ControllerClient.h',
    '../src/topping_controller/devices/dx5ii/dx5ii_device.h',
    '../src/topping_controller/devices/d900/d900_device.h'
  ]

  s.dependency 'Flutter'
  s.platform = :ios, '12.0'
  s.libraries = 'c++', 'z'

  # 禁用任何Swift相关的配置
  # s.swift_version = '5.0'

  s.pod_target_xcconfig = {
    'DEFINES_MODULE' => 'YES',
    'EXCLUDED_ARCHS[sdk=iphonesimulator*]' => 'i386',
    'CLANG_CXX_LANGUAGE_STANDARD' => 'c++11',
    'CLANG_CXX_LIBRARY' => 'libc++',
    'HEADER_SEARCH_PATHS' => [
      '$(PODS_TARGET_SRCROOT)/../src',
      '$(PODS_TARGET_SRCROOT)/../src/topping_controller',
      '$(PODS_TARGET_SRCROOT)/../src/topping_controller/core',
      '$(PODS_TARGET_SRCROOT)/../src/topping_controller/core/base',
      '$(PODS_TARGET_SRCROOT)/../src/topping_controller/core/ble',
      '$(PODS_TARGET_SRCROOT)/../src/topping_controller/core/protocol',
      '$(PODS_TARGET_SRCROOT)/../src/topping_controller/devices/dx5ii',
      '$(PODS_TARGET_SRCROOT)/../src/topping_controller/devices/d900',
      '$(PODS_TARGET_SRCROOT)/../src/topping_controller/devices/flutter',
      # 添加本地库路径
      '$(PODS_TARGET_SRCROOT)/Libraries/GaiaLibrary',
      '$(PODS_TARGET_SRCROOT)/Libraries/BTLibrary'
    ].join(' ')
  }

  # 移除桥接头文件配置，我们不再需要桥接
  s.xcconfig = {
    'OTHER_LDFLAGS' => '-ObjC -lz'
  }
end
