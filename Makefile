BUILD_DIR := build
OBJ_DIR	:= $(BUILD_DIR)/obj
BIN_DIR := $(BUILD_DIR)/bin
DEMO_FILE := $(BIN_DIR)/demo
SHARED_LIB := $(BIN_DIR)/libtpcontrol.so

CXX = $(CROSS_COMPILE)g++
LD = $(CROSS_COMPILE)ld
AR = $(CROSS_COMPILE)ar
NM = $(CROSS_COMPILE)nm
OBJCOPY = $(CROSS_COMPILE)objcopy
OBJDUMP = $(CROSS_COMPILE)objdump
READELF = $(CROSS_COMPILE)readelf
STRIP = $(CROSS_COMPILE)strip

vpath %.o $(OBJ_DIR)

INCLUDES := -I.

CXXFLAGS += -Wall -Werror -std=c++11 -g -fsanitize=address -fno-omit-frame-pointer -static-libasan

SRCS := 

include core/Makefile
include devices/Makefile

BASE_OBJS := $(patsubst %.cpp, %.o, $(SRCS))
OBJS := $(addprefix $(OBJ_DIR)/,$(BASE_OBJS))

all : $(BUILD_DIR) $(OBJ_DIR) $(BIN_DIR) $(SHARED_LIB) $(DEMO_FILE)

$(DEMO_FILE) : $(BASE_OBJS)
	@echo LD $(DEMO_FILE)
	@$(CXX) $(CXXFLAGS) devices/demo/demo.cpp $(OBJS) -o $(DEMO_FILE) $(INCLUDES)
	@ls -l $(BIN_DIR)

$(SHARED_LIB) : $(BASE_OBJS)
	@echo CXX $(SHARED_LIB)
	@$(CXX) $(CXXFLAGS) -shared $(OBJS) -o $(SHARED_LIB) $(INCLUDES)
	@ls -l $(BIN_DIR)

$(BASE_OBJS) : %.o : %.cpp
	@echo CXX $<
	@$(CXX) $(CXXFLAGS) -fPIC -c $< -o $(OBJ_DIR)/$@ $(INCLUDES)

$(BUILD_DIR) :
	mkdir $(BUILD_DIR)
	
$(OBJ_DIR) :
	mkdir $(OBJ_DIR)
	
$(BIN_DIR) :
	mkdir $(BIN_DIR)
	
.PHONY : clean

clean :
	rm -rf $(BUILD_DIR)

	
