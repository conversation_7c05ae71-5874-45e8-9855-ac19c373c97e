# Topping Controller
## 描述
Topping Controller 是一个Topping control协议的app端实现，旨在用一个统一的框架去支持多个设备端去实现设备发现、连接、握手鉴权和扩充设备端业务协议等功能。
## 怎么移植并添加一个新的设备实现？
### 在topping_control.md文件添加一个新的设备信息
在topping_control.md文件添加一个新的设备信息，包括设备描述、厂商ID、产品ID、和256位的私钥，支持的命令列表等信息，并和设备端进行协商。
### 在devices文件夹新增一个文件夹
在devices文件夹新增一个文件夹用于存放新增的设备端协议的代码实现和Makefile文件
### 实现ble文件夹下的蓝牙接口
根据本地蓝牙API去实现ble文件夹下的蓝牙接口，分别要实现BluetoothLeScanner、BluetoothGatt、BluetoothLeAdapter三个接口，并使用BluetoothManager进行注册。
### 添加属于自己的控制协议
使用ControllerScanner发现设备和使用ControllerClient进行握手连接和数据传递。