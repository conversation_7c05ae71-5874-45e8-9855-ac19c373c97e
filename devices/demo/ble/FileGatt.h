#ifndef __FILE_GATT_H__
#define __FILE_GATT_H__
#include "BluetoothGatt.h"
#include "BluetoothLeAdapter.h"
#include <vector>
#include "BluetoothGattService.h"
namespace Topping
{
    class FileGatt : public BluetoothGatt
    {
    public:
        FileGatt(int pid, BluetoothLeAdapter::BluetoothGattCallback *bluetoothGattCallback);
        virtual ~FileGatt();
        virtual void close() override;
        virtual void connect() override;
        virtual void disconnect() override;
        virtual bool requestMtu(int mtu) override;
        virtual bool writeCharacteristic(BluetoothGattCharacteristic &characteristic) override;
        virtual bool setCharacteristicNotification(BluetoothGattCharacteristic &characteristic, bool enable) override;
        virtual BluetoothGattService *getService(const std::string &uuid) override;

    public:
        BluetoothLeAdapter::BluetoothGattCallback *mBluetoothGattCallback;
        int mNotifyFd;
        int mWriteFd;

    private:
        int mPid;
        BluetoothGattService mBluetoothGattService;
    };
}
#endif