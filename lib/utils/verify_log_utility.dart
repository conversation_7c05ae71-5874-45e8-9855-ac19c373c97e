import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:topping_ble_control/utils/log_util.dart';

/// 验证日志工具类，用于读取和管理验证日志文件
class VerifyLogUtility {
  /// 验证日志文件名
  static const String _verifyLogFileName = 'verify_session.log';

  /// 获取验证日志文件路径
  static Future<String> getVerifyLogFilePath() async {
    try {
      final Directory appDocDir = await getApplicationDocumentsDirectory();
      return '${appDocDir.path}/verify_logs/$_verifyLogFileName';
    } catch (e) {
      Log.e('获取验证日志文件路径失败: $e');
      return '';
    }
  }

  /// 检查验证日志文件是否存在
  static Future<bool> verifyLogFileExists() async {
    try {
      final String path = await getVerifyLogFilePath();
      final File file = File(path);
      return await file.exists();
    } catch (e) {
      Log.e('检查验证日志文件失败: $e');
      return false;
    }
  }

  /// 读取验证日志文件内容
  static Future<String> readVerifyLogFile() async {
    try {
      final String path = await getVerifyLogFilePath();
      final File file = File(path);

      if (!await file.exists()) {
        return '验证日志文件不存在';
      }

      final String content = await file.readAsString();
      return content.isEmpty ? '验证日志为空' : content;
    } catch (e) {
      Log.e('读取验证日志文件失败: $e');
      return '读取验证日志文件失败: $e';
    }
  }

  /// 分享验证日志文件 (调用者需要处理UI交互，这里只返回文件路径)
  static Future<String?> getVerifyLogFileForSharing() async {
    try {
      final String path = await getVerifyLogFilePath();
      final File file = File(path);

      if (!await file.exists()) {
        return null;
      }

      return path;
    } catch (e) {
      Log.e('准备分享验证日志文件失败: $e');
      return null;
    }
  }
}
