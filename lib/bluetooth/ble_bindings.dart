import 'dart:ffi';
import 'dart:io';

import 'package:ffi/ffi.dart';

import '../mappers/ffi_mapper.dart';
import '../model/bluetooth/ble_device.dart';
import '../model/bluetooth/gatt_characteristic.dart';
import '../model/ffi/ffi_ble_scanner_functions.dart';
import '../model/ffi/ffi_gatt_characteristic.dart';
import '../model/ffi/ffi_gatt_functions.dart';
import '../model/ffi/ffi_scan_result.dart';
import '../utils/log_util.dart';
import 'ffi/ble_ffi_registry.dart';

/// 手动创建的BLE扫描器绑定
class BleBindings {
  static const String tag = "BleBindings";

  /// 单例实例
  static BleBindings? _instance;

  /// 获取单例实例
  static BleBindings get instance {
    _instance ??= BleBindings._();
    return _instance!;
  }

  late final DynamicLibrary _dylib;

  /// 原生函数指针
  late final void Function(Pointer<FFIBleScannerFunctions>)
  _flutterBleScannerRegisterFunctions;
  late final void Function() _flutterAdapterRegisterDefault;
  late final void Function(Pointer<FFIGattFunctions>)
  _flutterGattRegisterFunctions;
  late final void Function(int, int, int) _flutterGattOnConnectionStateChange;
  late final void Function(int) _flutterGattOnServicesDiscovered;
  late final void Function(int, Pointer<FFIGattCharacteristic>)
  _flutterGattOnCharacteristicChanged;

  /// 静态回调函数存储
  static int Function(int)? _bleScannerInitCallback;
  static void Function(int)? _bleScannerUninitCallback;
  static void Function(int)? _bleScannerStartScanCallback;
  static void Function(int)? _bleScannerStopScanCallback;

  static int Function(int)? _gattInitCallback;
  static void Function(int)? _gattUninitCallback;
  static void Function(int)? _gattCloseCallback;
  static void Function(int)? _gattConnectCallback;
  static void Function(int)? _gattDisconnectCallback;
  static int Function(int, int)? _gattRequestMtuCallback;
  static int Function(int, Pointer<FFIGattCharacteristic>)?
  _gattWriteCharacteristicCallback;
  static int Function(int, Pointer<FFIGattCharacteristic>, int)?
  _gattSetCharacteristicNotificationCallback;
  static int Function(int, Pointer<Char>)? _gattGetServiceCallback;

  /// 静态的FFI回调桥接函数
  static int _bleScannerInit(int nativeObject) {
    return _bleScannerInitCallback?.call(nativeObject) ?? 0;
  }

  /// 释放扫描器
  static void _bleScannerUninit(int flutterObject) {
    _bleScannerUninitCallback?.call(flutterObject);
  }

  /// 开始扫描
  static void _bleScannerStartScan(int flutterObject) {
    Log.i("$tag: 开始扫描");
    _bleScannerStartScanCallback?.call(flutterObject);
  }

  /// 停止扫描
  static void _bleScannerStopScan(int flutterObject) {
    _bleScannerStopScanCallback?.call(flutterObject);
  }

  /// 初始化GATT
  static int _gattInit(int nativeObject) {
    return _gattInitCallback?.call(nativeObject) ?? 0;
  }

  /// 释放GATT
  static void _gattUninit(int flutterObject) {
    _gattUninitCallback?.call(flutterObject);
  }

  /// 关闭GATT
  static void _gattClose(int flutterObject) {
    _gattCloseCallback?.call(flutterObject);
  }

  /// 连接GATT
  static void _gattConnect(int flutterObject) {
    Log.i("Connecting to device");
    _gattConnectCallback?.call(flutterObject);
  }

  /// 断开GATT
  static void _gattDisconnect(int flutterObject) {
    _gattDisconnectCallback?.call(flutterObject);
  }

  /// 请求MTU
  static int _gattRequestMtu(int flutterObject, int mtu) {
    return _gattRequestMtuCallback?.call(flutterObject, mtu) ?? 0;
  }

  /// 写入特征值
  static int _gattWriteCharacteristic(
    int flutterObject,
    Pointer<FFIGattCharacteristic> characteristic,
  ) {
    return _gattWriteCharacteristicCallback?.call(
          flutterObject,
          characteristic,
        ) ??
        0;
  }

  /// 设置特征值通知
  static int _gattSetCharacteristicNotification(
    int flutterObject,
    Pointer<FFIGattCharacteristic> characteristic,
    int enable,
  ) {
    return _gattSetCharacteristicNotificationCallback?.call(
          flutterObject,
          characteristic,
          enable,
        ) ??
        0;
  }

  /// 获取服务
  static int _gattGetService(int flutterObject, Pointer<Char> uuid) {
    return _gattGetServiceCallback?.call(flutterObject, uuid) ?? 0;
  }

  /// 私有构造函数
  BleBindings._() {
    _dylib = _nativeLib;
    _loadFunctions();
  }

  /// 加载适合当前平台的动态库
  final DynamicLibrary _nativeLib =
      Platform.isAndroid
          ? DynamicLibrary.open("libtopping_controller.so")
          : DynamicLibrary.process();

  /// 加载原生函数
  void _loadFunctions() {
    _flutterBleScannerRegisterFunctions =
        _dylib
            .lookup<
              NativeFunction<Void Function(Pointer<FFIBleScannerFunctions>)>
            >('flutter_ble_scanner_register_functions')
            .asFunction();

    _flutterAdapterRegisterDefault =
        _dylib
            .lookup<NativeFunction<Void Function()>>(
              'flutter_adapter_register_default',
            )
            .asFunction();

    _flutterGattRegisterFunctions =
        _dylib
            .lookup<NativeFunction<Void Function(Pointer<FFIGattFunctions>)>>(
              'flutter_gatt_register_functions',
            )
            .asFunction();

    _flutterGattOnConnectionStateChange =
        _dylib
            .lookup<NativeFunction<Void Function(Int64, Int32, Int32)>>(
              'flutter_gatt_on_connection_state_change',
            )
            .asFunction();

    _flutterGattOnServicesDiscovered =
        _dylib
            .lookup<NativeFunction<Void Function(Int64)>>(
              'flutter_gatt_on_services_discovered',
            )
            .asFunction();

    _flutterGattOnCharacteristicChanged =
        _dylib
            .lookup<
              NativeFunction<
                Void Function(Int64, Pointer<FFIGattCharacteristic>)
              >
            >('flutter_gatt_on_characteristic_changed')
            .asFunction();
  }

  /// 注册扫描器函数
  void registerBleScannerFunctions({
    required int Function(int nativeObject) init,
    required void Function(int flutterObject) uninit,
    required void Function(int flutterObject) startScan,
    required void Function(int flutterObject) stopScan,
  }) {
    // 保存回调函数到静态变量
    _bleScannerInitCallback = init;
    _bleScannerUninitCallback = uninit;
    _bleScannerStartScanCallback = startScan;
    _bleScannerStopScanCallback = stopScan;

    final nativeFunctions = calloc<FFIBleScannerFunctions>();

    // 使用静态函数作为回调
    nativeFunctions.ref.init = Pointer.fromFunction<Int64 Function(Int64)>(
      _bleScannerInit,
      0,
    );
    nativeFunctions.ref.uninit = Pointer.fromFunction<Void Function(Int64)>(
      _bleScannerUninit,
    );
    nativeFunctions.ref.start_scan = Pointer.fromFunction<Void Function(Int64)>(
      _bleScannerStartScan,
    );
    nativeFunctions.ref.stop_scan = Pointer.fromFunction<Void Function(Int64)>(
      _bleScannerStopScan,
    );

    _flutterBleScannerRegisterFunctions(nativeFunctions);
    calloc.free(nativeFunctions);
  }

  /// 注册默认适配器
  void registerDefaultAdapter() {
    _flutterAdapterRegisterDefault();
  }

  /// 注册GATT函数
  void registerGattFunctions({
    required int Function(int nativeObject) init,
    required void Function(int flutterObject) uninit,
    required void Function(int flutterObject) close,
    required void Function(int flutterObject) connect,
    required void Function(int flutterObject) disconnect,
    required int Function(int flutterObject, int mtu) requestMtu,
    required int Function(
      int flutterObject,
      Pointer<FFIGattCharacteristic> characteristic,
    )
    writeCharacteristic,
    required int Function(
      int flutterObject,
      Pointer<FFIGattCharacteristic> characteristic,
      int enable,
    )
    setCharacteristicNotification,
    required int Function(int flutterObject, Pointer<Char> uuid) getService,
  }) {
    // 保存回调函数到静态变量
    _gattInitCallback = init;
    _gattUninitCallback = uninit;
    _gattCloseCallback = close;
    _gattConnectCallback = connect;
    _gattDisconnectCallback = disconnect;
    _gattRequestMtuCallback = requestMtu;
    _gattWriteCharacteristicCallback = writeCharacteristic;
    _gattSetCharacteristicNotificationCallback = setCharacteristicNotification;
    _gattGetServiceCallback = getService;

    final nativeFunctions = calloc<FFIGattFunctions>();

    // 使用静态函数作为回调
    nativeFunctions.ref.init = Pointer.fromFunction<Int64 Function(Int64)>(
      _gattInit,
      0,
    );
    nativeFunctions.ref.uninit = Pointer.fromFunction<Void Function(Int64)>(
      _gattUninit,
    );
    nativeFunctions.ref.close = Pointer.fromFunction<Void Function(Int64)>(
      _gattClose,
    );
    nativeFunctions.ref.connect = Pointer.fromFunction<Void Function(Int64)>(
      _gattConnect,
    );
    nativeFunctions.ref.disconnect = Pointer.fromFunction<Void Function(Int64)>(
      _gattDisconnect,
    );
    nativeFunctions.ref.request_mtu =
        Pointer.fromFunction<Int32 Function(Int64, Int32)>(_gattRequestMtu, 0);
    nativeFunctions.ref.write_characteristic = Pointer.fromFunction<
      Int32 Function(Int64, Pointer<FFIGattCharacteristic>)
    >(_gattWriteCharacteristic, 0);
    nativeFunctions.ref.set_characteristic_notification = Pointer.fromFunction<
      Int32 Function(Int64, Pointer<FFIGattCharacteristic>, Int32)
    >(_gattSetCharacteristicNotification, 0);
    nativeFunctions.ref.get_service =
        Pointer.fromFunction<Int64 Function(Int64, Pointer<Char>)>(
          _gattGetService,
          0,
        );

    _flutterGattRegisterFunctions(nativeFunctions);
    calloc.free(nativeFunctions);
  }

  /// 通知连接状态变化
  void notifyConnectionStateChange(int nativeObject, int state, int newState) {
    _flutterGattOnConnectionStateChange(nativeObject, state, newState);
  }

  /// 通知服务发现完成
  void notifyServicesDiscovered(int nativeObject) {
    _flutterGattOnServicesDiscovered(nativeObject);
  }

  /// 通知特征值变化
  void notifyCharacteristicChanged(
    int nativeObject,
    GattCharacteristic characteristic,
  ) {
    final nativeChar = calloc<FFIGattCharacteristic>();

    try {
      // 使用映射器转换特征值
      final nativeChar = FFIMapper.toFFIGattCharacteristic(
        characteristic,
        calloc,
      );
      _flutterGattOnCharacteristicChanged(nativeObject, nativeChar);
    } catch (e) {
      Log.e("BleBindings.notifyCharacteristicChanged - 调用失败: $e");
    } finally {
      // 清理已由toFFIGattCharacteristic分配的内存
      if (nativeChar.ref.uuid != nullptr) {
        calloc.free(nativeChar.ref.uuid.cast<NativeType>());
      }
      if (nativeChar.ref.value != nullptr) {
        calloc.free(nativeChar.ref.value);
      }
      calloc.free(nativeChar);
    }
  }
}
