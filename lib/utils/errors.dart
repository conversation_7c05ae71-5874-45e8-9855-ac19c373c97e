/// 蓝牙错误
class BluetoothError extends Error {
  final String message;
  final int code;

  BluetoothError(this.message, {this.code = 0});

  @override
  String toString() => 'BluetoothError($code): $message';
}

/// 连接错误
class ConnectionError extends BluetoothError {
  ConnectionError(super.message, {super.code});
}

/// 扫描错误
class ScanError extends BluetoothError {
  ScanError(super.message, {super.code});
}

/// 蓝牙错误处理器
class BleErrorHandler {
  static void handleError(dynamic error) {
    if (error is BluetoothError) {
      throw error;
    } else {
      throw BluetoothError(error.toString());
    }
  }
}
