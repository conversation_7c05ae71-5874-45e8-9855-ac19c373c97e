import '../base/topping_verify_result_type.dart';

/// 重新导出通用设备验证结果类型
/// 为保持向后兼容，保留Dx5iiVerifyResultType别名
typedef Dx5iiVerifyResultType = ToppingVerifyResultType;

/// 提供从数值转换为验证结果类型的便捷方法
class Dx5iiVerifyResult {
  /// 从整数值转换为验证结果类型
  static ToppingVerifyResultType fromValue(int value) {
    switch (value) {
      case 0:
        return ToppingVerifyResultType.success;
      case 1:
        return ToppingVerifyResultType.failed;
      case 2:
        return ToppingVerifyResultType.timeout;
      default:
        return ToppingVerifyResultType.unknown;
    }
  }
}
