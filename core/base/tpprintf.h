#ifndef __TPPRINTF_H__
#define __TPPRINTF_H__
#include <stdio.h>

#ifdef ANDROID
#include <android/log.h>
#elif defined(IOS)
#import <Foundation/Foundation.h>  // 需要 Foundation 框架支持 NSLog
#endif

#define USE_TPPRINTF 1

#if USE_TPPRINTF
#ifdef ANDROID
#define TPPRINTF(...) __android_log_print(ANDROID_LOG_INFO, "Topping Controller", ##__VA_ARGS__)
#elif defined(IOS)
#define TPPRINTF(...) NSLog(@"" __VA_ARGS__)  // iOS 專用日誌
#else
#define TPPRINTF printf  // 其他平台使用標準 printf
#endif
#else
#define TPPRINTF(...)  // 禁用日誌
#endif

#endif // __TPPRINTF_H__
