W/LOG_FLOWCTRL(16188): ==LOGS OVER PROC QUOTA(300), rows(138) bytes(32014) com.example.test1_example DROPPED==
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.e (package:topping_ble_control/utils/log_util.dart:32:13)
I/flutter (16188): │ #1   _BluetoothAppState._addLog (package:test1_example/main.dart:1152:9)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ ⛔ 尝试断开连接...
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   DeviceFactory.disconnectCurrentDevice (package:topping_ble_control/device/device_factory.dart:165:13)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 DeviceFactory: Attempting to disconnect Dx5iiDeviceManager
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   Dx5iiDeviceManager.disconnect (package:topping_ble_control/device/dx5/dx5ii_device_manager.dart:601:9)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 DX5II: 断开设备连接
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BleFfiInitializer._registerGattFunctions.<anonymous closure> (package:topping_ble_control/bluetooth/ffi/ble_ffi_initializer.dart:146:13)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 C++调用disconnect, gatt flutterObject: 1747364464603
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BleConnector.disconnectDevice (package:topping_ble_control/bluetooth/connection/ble_connector.dart:279:9)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 断开设备连接
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BleConnector.disconnectDevice (package:topping_ble_control/bluetooth/connection/ble_connector.dart:295:11)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 断开连接请求已发送
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BleOperationManager.disconnect (package:topping_ble_control/bluetooth/ble_operation_manager.dart:166:11)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 断开设备连接成功
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   DeviceFactory.disconnectCurrentDevice (package:topping_ble_control/device/device_factory.dart:181:15)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 DeviceFactory: Manually emitted disconnected state via _connectionStateController for handle: 219393102.
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   ToppingDeviceManager.dispose (package:topping_ble_control/device/topping_device_manager.dart:447:9)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 释放ToppingDeviceManager资源
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   DeviceFactory.disconnectCurrentDevice (package:topping_ble_control/device/device_factory.dart:188:13)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 DeviceFactory: Dx5iiDeviceManager disposed.
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   DeviceFactory.disconnectCurrentDevice (package:topping_ble_control/device/device_factory.dart:193:15)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 DeviceFactory: _currentDeviceManager set to null.
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
D/ViewRootImplExtImpl(16188): the up motion event handled by client, just return
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.e (package:topping_ble_control/utils/log_util.dart:32:13)
I/flutter (16188): │ #1   _BluetoothAppState._addLog (package:test1_example/main.dart:1152:9)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ ⛔ 工厂连接状态变化: BleConnectionState.disconnected
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.e (package:topping_ble_control/utils/log_util.dart:32:13)
I/flutter (16188): │ #1   _BluetoothAppState._addLog (package:test1_example/main.dart:1152:9)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ ⛔ 设备断开连接，重置所有状态
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
D/[FBP-Android](16188): [FBP] onMethodCall: disconnect
D/BluetoothGatt(16188): cancelOpen() - device: XX:XX:XX:XX:00:1C
D/BluetoothGatt(16188): onClientConnectionState() - status=0 clientIf=15 connected=false device=53:4A:52:FE:00:1C
D/[FBP-Android](16188): [FBP] onConnectionStateChange:disconnected
D/[FBP-Android](16188): [FBP]   status: SUCCESS
D/BluetoothGatt(16188): close()
D/BluetoothGatt(16188): unregisterApp() - mClientIf=15
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BleConnector._setupConnectionStateListener.<anonymous closure> (package:topping_ble_control/bluetooth/connection/ble_connector.dart:65:15)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 监听到连接状态变化: BluetoothConnectionState.disconnected, 时间戳: 1747364495341
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 设备[DX5 II] 连接状态: 状态变化为 BluetoothConnectionState.disconnected
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BleConnector._notifyDisconnected (package:topping_ble_control/bluetooth/connection/ble_connector.dart:254:9)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 通知断开连接状态
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BleNativeNotifier.notifyConnectionStateChange (package:topping_ble_control/bluetooth/ffi/ble_native_notifier.dart:24:9)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 通知连接状态变化: FfiBluetoothProfileState.stateDisconnected -> FfiBluetoothProfileState.stateDisconnected nativeObject: -5476376613194078384
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 设备[设备] 连接状态: 状态变化为 FfiBluetoothProfileState.stateDisconnected
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/Topping Controller(16188): onStateChange; state:2
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   Dx5iiDeviceBindings._onStateChange (package:topping_ble_control/device/dx5/dx5ii_device_bindings.dart:297:9)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 Flutter回调: onStateChange, flutterObject: 1747364464538, state: 2
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   Dx5iiDeviceManager._handleStateChange (package:topping_ble_control/device/dx5/dx5ii_device_manager.dart:124:9)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 DX5II: 收到状态变更: BleConnectionState.disconnected, 设备句柄: 219393102, 时间戳: 1747364495344
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 设备[DX5 II] 连接状态: 状态变化为 BleConnectionState.disconnected
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.w (package:topping_ble_control/utils/log_util.dart:28:13)
I/flutter (16188): │ #1   Dx5iiDeviceManager._handleStateChange (package:topping_ble_control/device/dx5/dx5ii_device_manager.dart:139:13)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ ⚠️ DX5II: 连接丢失/失败于验证期间，停止验证会话。
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   VerifyInterceptor.stopVerifying (package:topping_ble_control/utils/verify_interceptor.dart:95:9)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 VerifyInterceptor: 结束验证模式，会话ID：1747364473005_0
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 [VERIFY] ✓ ==== 验证结束 ==== ✓ [会话ID: 1747364473005_0]
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BleConnector._notifyDisconnected (package:topping_ble_control/bluetooth/connection/ble_connector.dart:274:9)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 断开连接状态通知完成，所有相关状态已重置
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.e (package:topping_ble_control/utils/log_util.dart:32:13)
I/flutter (16188): │ #1   _BluetoothAppState._addLog (package:test1_example/main.dart:1152:9)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ ⛔ 尝试连接到设备: DX5 II (句柄: 219393102)
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   DeviceFactory._setupDeviceStateListener (package:topping_ble_control/device/device_factory.dart:260:9)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 [DeviceFactory] Setting up device state listener for Dx5iiDeviceManager
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   Dx5iiDeviceManager.connect (package:topping_ble_control/device/dx5/dx5ii_device_manager.dart:595:9)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 DX5II: 连接设备, 句柄: 219393102
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   ToppingDeviceManager.connectDevice (package:topping_ble_control/device/topping_device_manager.dart:334:9)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 DX5II 连接时间打印 --- 准备连接设备，句柄: 219393102, 时间戳: 1747364499735
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   ToppingDeviceManager.connectDevice (package:topping_ble_control/device/topping_device_manager.dart:395:9)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 DX5II 发送连接中事件完成, 时间戳: 1747364499736
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   ToppingDeviceManager.connectDevice (package:topping_ble_control/device/topping_device_manager.dart:398:9)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 DX5II 调用底层连接函数, 时间戳: 1747364499736
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BleOperationManager.connect (package:topping_ble_control/bluetooth/ble_operation_manager.dart:152:11)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 开始连接设备，句柄: 219393102
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BleConnector.connectToDevice (package:topping_ble_control/bluetooth/connection/ble_connector.dart:34:9)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 尝试连接设备，句柄: 219393102
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 设备[设备] 连接状态: 尝试连接设备 (句柄: 219393102)
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BleConnector._prepareDeviceForConnection (package:topping_ble_control/bluetooth/connection/ble_connector.dart:115:9)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 准备设备连接，句柄: 219393102
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BleConnector._prepareDeviceForConnection (package:topping_ble_control/bluetooth/connection/ble_connector.dart:160:9)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 设备准备就绪
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BleConnector._connectWithTimeout (package:topping_ble_control/bluetooth/connection/ble_connector.dart:179:9)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 尝试连接设备: DX5 II, 时间戳: 1747364499739
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   DeviceFactory.connectDevice (package:topping_ble_control/device/device_factory.dart:155:9)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 [DeviceFactory] connectDevice called for DX5 II
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
D/ViewRootImplExtImpl(16188): the up motion event handled by client, just return
I/Topping Controller(16188): [BLE日志] ControllerClient::connect被调用, callback: 0xb400007cdabf1648
I/Topping Controller(16188): [BLE日志] 构造FlutterGatt对象, 回调指针: 0xb400007cdabf1668
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BleFfiInitializer._registerGattFunctions.<anonymous closure> (package:topping_ble_control/bluetooth/ffi/ble_ffi_initializer.dart:129:13)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 C++调用init, gatt nativeObject: -5476376613194096080
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/Topping Controller(16188): [BLE日志] FlutterGatt回调状态:
I/Topping Controller(16188):   - this指针: 0xb400007c424f2230
I/Topping Controller(16188):   - mCallback指针: 0xb400007cdabf1668
I/Topping Controller(16188):   - mFlutterObject: 1747364499743
I/Topping Controller(16188):   - mCallback有效
I/Topping Controller(16188):   - mFunctions.init: 0x7c46280488
I/Topping Controller(16188):   - mFunctions.write_characteristic: 0x7c462804b8
I/Topping Controller(16188):   - mFunctions.set_characteristic_notification: 0x7c462804c0
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BleBindings._gattConnect (package:topping_ble_control/bluetooth/ble_bindings.dart:98:9)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 Connecting to device
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BleFfiInitializer._registerGattFunctions.<anonymous closure> (package:topping_ble_control/bluetooth/ffi/ble_ffi_initializer.dart:142:13)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 C++调用connect, gatt flutterObject: 1747364499743
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BleFfiInitializer._handleConnect (package:topping_ble_control/bluetooth/ffi/ble_ffi_initializer.dart:186:9)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 连接请求，句柄: 219393102
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/Topping Controller(16188): [BLE日志] ControllerClient状态(connect完成):
I/Topping Controller(16188):   - this指针: 0xb400007cdabf1668
I/Topping Controller(16188):   - mBluetoothGatt: 0xb400007c424f2230
I/Topping Controller(16188):   - mCallback: 0xb400007cdabf1648
I/Topping Controller(16188):   - mState: 2
I/Topping Controller(16188):   - mBleMtu: 260
I/Topping Controller(16188):   - BluetoothGatt有效
I/Topping Controller(16188):   - Callback有效
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   Dx5iiDeviceManager.connectNative (package:topping_ble_control/device/dx5/dx5ii_device_manager.dart:65:9)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 DX5II 底层连接函数调用完成, 时间戳: 1747364499743
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   ToppingDeviceManager.connectDevice (package:topping_ble_control/device/topping_device_manager.dart:420:11)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 DX5II 底层连接函数调用完成, 时间戳: 1747364499744
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
D/[FBP-Android](16188): [FBP] onMethodCall: connect
D/BluetoothGatt(16188): connect() - device: XX:XX:XX:XX:00:1C, auto: false
D/BluetoothGatt(16188): registerApp()
D/BluetoothGatt(16188): registerApp() - UUID=d6f1b31f-2238-4fcb-8936-d60528f689f1
D/BluetoothGatt(16188): onClientRegistered() - status=0 clientIf=15
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BleOperationManager.connect (package:topping_ble_control/bluetooth/ble_operation_manager.dart:152:11)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 开始连接设备，句柄: 219393102
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BleConnector.connectToDevice (package:topping_ble_control/bluetooth/connection/ble_connector.dart:34:9)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 尝试连接设备，句柄: 219393102
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 设备[设备] 连接状态: 尝试连接设备 (句柄: 219393102)
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BleConnector._prepareDeviceForConnection (package:topping_ble_control/bluetooth/connection/ble_connector.dart:115:9)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 准备设备连接，句柄: 219393102
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BleConnector._prepareDeviceForConnection (package:topping_ble_control/bluetooth/connection/ble_connector.dart:160:9)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 设备准备就绪
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BleConnector._connectWithTimeout (package:topping_ble_control/bluetooth/connection/ble_connector.dart:179:9)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 尝试连接设备: DX5 II, 时间戳: 1747364499761
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.e (package:topping_ble_control/utils/log_util.dart:32:13)
I/flutter (16188): │ #1   _BluetoothAppState._addLog (package:test1_example/main.dart:1152:9)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ ⛔ 尝试连接到设备: DX5 II (句柄: 219393102)
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   DeviceFactory._setupDeviceStateListener (package:topping_ble_control/device/device_factory.dart:260:9)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 [DeviceFactory] Setting up device state listener for Dx5iiDeviceManager
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   Dx5iiDeviceManager.connect (package:topping_ble_control/device/dx5/dx5ii_device_manager.dart:595:9)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 DX5II: 连接设备, 句柄: 219393102
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   ToppingDeviceManager.connectDevice (package:topping_ble_control/device/topping_device_manager.dart:334:9)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 DX5II 连接时间打印 --- 准备连接设备，句柄: 219393102, 时间戳: 1747364500080
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   ToppingDeviceManager.connectDevice (package:topping_ble_control/device/topping_device_manager.dart:395:9)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 DX5II 发送连接中事件完成, 时间戳: 1747364500081
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   ToppingDeviceManager.connectDevice (package:topping_ble_control/device/topping_device_manager.dart:398:9)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 DX5II 调用底层连接函数, 时间戳: 1747364500081
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BleOperationManager.connect (package:topping_ble_control/bluetooth/ble_operation_manager.dart:152:11)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 开始连接设备，句柄: 219393102
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BleConnector.connectToDevice (package:topping_ble_control/bluetooth/connection/ble_connector.dart:34:9)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 尝试连接设备，句柄: 219393102
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 设备[设备] 连接状态: 尝试连接设备 (句柄: 219393102)
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BleConnector._prepareDeviceForConnection (package:topping_ble_control/bluetooth/connection/ble_connector.dart:115:9)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 准备设备连接，句柄: 219393102
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BleConnector._prepareDeviceForConnection (package:topping_ble_control/bluetooth/connection/ble_connector.dart:160:9)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 设备准备就绪
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BleConnector._connectWithTimeout (package:topping_ble_control/bluetooth/connection/ble_connector.dart:179:9)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 尝试连接设备: DX5 II, 时间戳: 1747364500084
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   DeviceFactory.connectDevice (package:topping_ble_control/device/device_factory.dart:155:9)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 [DeviceFactory] connectDevice called for DX5 II
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
D/ViewRootImplExtImpl(16188): the up motion event handled by client, just return
I/Topping Controller(16188): [BLE日志] ControllerClient::connect被调用, callback: 0xb400007cdabf1648
I/Topping Controller(16188): [BLE日志 GATT] FlutterGatt::close被调用
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BleFfiInitializer._registerGattFunctions.<anonymous closure> (package:topping_ble_control/bluetooth/ffi/ble_ffi_initializer.dart:139:13)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 C++调用close, gatt flutterObject: 1747364499743
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/Topping Controller(16188): [BLE日志 GATT] 析构FlutterGatt对象
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BleFfiInitializer._registerGattFunctions.<anonymous closure> (package:topping_ble_control/bluetooth/ffi/ble_ffi_initializer.dart:135:13)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 C++调用uninit, gatt flutterObject: 1747364499743
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/Topping Controller(16188): [BLE日志] 构造FlutterGatt对象, 回调指针: 0xb400007cdabf1668
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BleFfiInitializer._registerGattFunctions.<anonymous closure> (package:topping_ble_control/bluetooth/ffi/ble_ffi_initializer.dart:129:13)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 C++调用init, gatt nativeObject: -5476376613194096080
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/Topping Controller(16188): [BLE日志] FlutterGatt回调状态:
I/Topping Controller(16188):   - this指针: 0xb400007c424f2230
I/Topping Controller(16188):   - mCallback指针: 0xb400007cdabf1668
I/Topping Controller(16188):   - mFlutterObject: 1747364500089
I/Topping Controller(16188):   - mCallback有效
I/Topping Controller(16188):   - mFunctions.init: 0x7c46280488
I/Topping Controller(16188):   - mFunctions.write_characteristic: 0x7c462804b8
I/Topping Controller(16188):   - mFunctions.set_characteristic_notification: 0x7c462804c0
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BleBindings._gattConnect (package:topping_ble_control/bluetooth/ble_bindings.dart:98:9)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 Connecting to device
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BleFfiInitializer._registerGattFunctions.<anonymous closure> (package:topping_ble_control/bluetooth/ffi/ble_ffi_initializer.dart:142:13)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 C++调用connect, gatt flutterObject: 1747364500089
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BleFfiInitializer._handleConnect (package:topping_ble_control/bluetooth/ffi/ble_ffi_initializer.dart:186:9)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 连接请求，句柄: 219393102
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
W/LOG_FLOWCTRL(16188): ==LOGS OVER PROC QUOTA(300), rows(86) bytes(17596) com.example.test1_example DROPPED==
D/[FBP-Android](16188): [FBP] onMethodCall: requestMtu
D/BluetoothGatt(16188): configureMTU() - device: XX:XX:XX:XX:00:1C mtu: 512
D/BluetoothGatt(16188): onConnectionUpdated() - Device=53:4A:52:FE:00:1C interval=72 latency=4 timeout=400 status=0
D/BluetoothGatt(16188): onConfigureMTU() - Device=53:4A:52:FE:00:1C mtu=263 status=0
D/[FBP-Android](16188): [FBP] onMtuChanged:
D/[FBP-Android](16188): [FBP]   mtu: 263
D/[FBP-Android](16188): [FBP]   status: GATT_SUCCESS (0)
D/[FBP-Android](16188): [FBP] onMethodCall: discoverServices
D/BluetoothGatt(16188): discoverServices() - device: XX:XX:XX:XX:00:1C
D/BluetoothGatt(16188): onSearchComplete() = Device=53:4A:52:FE:00:1C Status=0
D/[FBP-Android](16188): [FBP] onServicesDiscovered:
D/[FBP-Android](16188): [FBP]   count: 5
D/[FBP-Android](16188): [FBP]   status: 0GATT_SUCCESS
D/[FBP-Android](16188): [FBP] onMethodCall: requestMtu
D/BluetoothGatt(16188): configureMTU() - device: XX:XX:XX:XX:00:1C mtu: 512
D/BluetoothGatt(16188): onConfigureMTU() - Device=53:4A:52:FE:00:1C mtu=263 status=0
D/[FBP-Android](16188): [FBP] onMtuChanged:
D/[FBP-Android](16188): [FBP]   mtu: 263
D/[FBP-Android](16188): [FBP]   status: GATT_SUCCESS (0)
D/[FBP-Android](16188): [FBP] onMethodCall: connect
D/[FBP-Android](16188): [FBP] already connected
D/[FBP-Android](16188): [FBP] onMethodCall: setNotifyValue
D/BluetoothGatt(16188): setCharacteristicNotification() - uuid: 00002a05-0000-1000-8000-00805f9b34fb enable: true
D/BluetoothGatt(16188): onConnectionUpdated() - Device=53:4A:52:FE:00:1C interval=72 latency=4 timeout=400 status=59
D/[FBP-Android](16188): [FBP] onDescriptorWrite:
D/[FBP-Android](16188): [FBP]   chr: 2a05
D/[FBP-Android](16188): [FBP]   desc: 2902
D/[FBP-Android](16188): [FBP]   status: GATT_SUCCESS (0)
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BleConnector._discoverServices (package:topping_ble_control/bluetooth/connection/ble_connector.dart:205:11)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 服务发现完成，发现 5 个服务, 时间戳: 1747364506841
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BleConnector._registerServices (package:topping_ble_control/bluetooth/connection/ble_connector.dart:218:9)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 注册 5 个服务
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BleNativeNotifier.notifyConnectionStateChange (package:topping_ble_control/bluetooth/ffi/ble_native_notifier.dart:24:9)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 通知连接状态变化: FfiBluetoothProfileState.stateConnected -> FfiBluetoothProfileState.stateConnected nativeObject: -5476376613194096080
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 设备[设备] 连接状态: 状态变化为 FfiBluetoothProfileState.stateConnected
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/Topping Controller(16188): onStateChange; state:0
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   Dx5iiDeviceBindings._onStateChange (package:topping_ble_control/device/dx5/dx5ii_device_bindings.dart:297:9)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 Flutter回调: onStateChange, flutterObject: 1747364464538, state: 0
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   Dx5iiDeviceManager._handleStateChange (package:topping_ble_control/device/dx5/dx5ii_device_manager.dart:124:9)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 DX5II: 收到状态变更: BleConnectionState.connectedUnsafe, 设备句柄: 219393102, 时间戳: 1747364506850
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 设备[DX5 II] 连接状态: 状态变化为 BleConnectionState.connectedUnsafe
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   Dx5iiDeviceManager.verify (package:topping_ble_control/device/dx5/dx5ii_device_manager.dart:450:9)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 DX5II: 开始验证设备
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 执行命令: 开始验证设备
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 开始验证设备: DX5 II
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   VerifyInterceptor.startVerifying (package:topping_ble_control/utils/verify_interceptor.dart:67:11)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 VerifyInterceptor: 开始验证模式，会话ID：1747364506857_1
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 [VERIFY] 🔍 ==== 验证开始 ==== 🔍 [会话ID: 1747364506857_1]
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   VerifyInterceptor.startVerifying (package:topping_ble_control/utils/verify_interceptor.dart:74:11)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 [VERIFY] ====== 开始验证阶段 1 ======
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BleFfiInitializer._registerGattFunctions.<anonymous closure> (package:topping_ble_control/bluetooth/ffi/ble_ffi_initializer.dart:166:15)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 C++调用getService: flutterObject: 1747364500089, uuid: 000090FB-0000-1000-8000-00805F9B34FB
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BleFfiInitializer._registerGattFunctions.<anonymous closure> (package:topping_ble_control/bluetooth/ffi/ble_ffi_initializer.dart:152:13)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 C++调用writeCharacteristic, flutterObject: 1747364500089
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BleNativeNotifier.notifyServicesDiscovered (package:topping_ble_control/bluetooth/ffi/ble_native_notifier.dart:51:9)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 通知服务发现完成 nativeObject: -5476376613194096080
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 蓝牙服务发现完成
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BleFfiInitializer._registerGattFunctions.<anonymous closure> (package:topping_ble_control/bluetooth/ffi/ble_ffi_initializer.dart:166:15)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 C++调用getService: flutterObject: 1747364500089, uuid: 000090FB-0000-1000-8000-00805F9B34FB
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/Topping Controller(16188): [BLE日志] FlutterGatt::setCharacteristicNotification被调用, UUID: 00009CF1-0000-1000-8000-00805F9B34FB, enable: 1
I/Topping Controller(16188): [BLE日志] FlutterGatt回调状态:
I/Topping Controller(16188):   - this指针: 0xb400007c424f2230
I/Topping Controller(16188):   - mCallback指针: 0xb400007cdabf1668
I/Topping Controller(16188):   - mFlutterObject: 1747364500089
I/Topping Controller(16188):   - mCallback有效
I/Topping Controller(16188):   - mFunctions.init: 0x7c46280488
I/Topping Controller(16188):   - mFunctions.write_characteristic: 0x7c462804b8
I/Topping Controller(16188):   - mFunctions.set_characteristic_notification: 0x7c462804c0
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BleFfiInitializer._registerGattFunctions.<anonymous closure> (package:topping_ble_control/bluetooth/ffi/ble_ffi_initializer.dart:157:13)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 C++调用setCharacteristicNotification, flutterObject: 1747364500089, enable: 1
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/Topping Controller(16188): [BLE日志] setCharacteristicNotification结果: 1
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BleCharacteristicHandler.writeCharacteristic (package:topping_ble_control/bluetooth/gatt/characteristic_handler.dart:38:9)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 写入特征值: 00008efa-0000-1000-8000-00805f9b34fb, 数据: [94, 199, 255, 254, 0, 101, 120, 218, 37, 203, 75, 10, 128, 32, 0, 132, 225, 187, 204, 218, 133, 165, 34, 120, 25, 17, 149, 112, 225, 3, 149, 32, 196, 187, 167, 181, 27, 254, 143, 25, 176, 209, 65, 29, 4, 253, 41, 30, 138, 18, 52, 223, 90, 200, 73, 135, 213, 57, 129, 175, 213, 102, 247, 211, 218, 177, 93, 80, 0, 129, 51, 221, 64, 13, 152, 82, 244, 237, 235, 190, 44, 56, 40, 93, 182, 91, 53, 201, 229, 248, 165, 147, 113, 33, 165, 96, 152, 243, 5, 173, 23, 34, 11, 35, 112, 179, 67]
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BleCharacteristicHandler.writeCharacteristic (package:topping_ble_control/bluetooth/gatt/characteristic_handler.dart:42:9)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 写入特征值: 00008efa-0000-1000-8000-00805f9b34fb, 数据: 5e c7 ff fe 00 65 78 da 25 cb 4b 0a 80 20 00 84 e1 bb cc da 85 a5 22 78 19 11 95 70 e1 03 95 20 c4 bb a7 b5 1b fe 8f 19 b0 d1 41 1d 04 fd 29 1e 8a 12 34 df 5a c8 49 87 d5 39 81 af d5 66 f7 d3 da b1 5d 50 00 81 33 dd 40 0d 98 52 f4 ed eb be 2c 38 28 5d b6 5b 35 c9 e5 f8 a5 93 71 21 a5 60 98 f3 05 ad 17 22 0b 23 70 b3 43,
I/flutter (16188): │ 💡 时间戳: 1747364506865
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 发送到[特征值]: 00008efa-0000-1000-8000-00805f9b34fb - 5e c7 ff fe 00 65 78 da 25 cb 4b 0a 80 20 00 84 e1 bb cc da 85 a5 22 78 19 11 95 70 e1 03 95 20 c4 bb a7 b5 1b fe 8f 19 b0 d1 41 1d 04 fd 29 1e 8a 12 34 df 5a c8 49 87 d5 39 81 af d5 66 f7 d3 da b1 5d 50 00 81 33 dd 40 0d 98 52 f4 ed eb be 2c 38 28 5d b6 5b 35 c9 e5 f8 a5 93 71 21 a5 60 98 f3 05 ad 17 22 0b 23 70 b3 43
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 [VERIFY] 🔄 验证数据(发送) [会话ID: 1747364506857_1] [阶段1]
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BleCharacteristicHandler.setCharacteristicNotification (package:topping_ble_control/bluetooth/gatt/characteristic_handler.dart:321:11)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 设置特征值通知: 00009CF1-0000-1000-8000-00805F9B34FB, 启用: true, 时间戳: 1747364506867
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BleNotificationHelper.enableNotifications (package:topping_ble_control/bluetooth/ble_notification_helper.dart:28:11)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 已启用 DX5 II 的通知特征
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BleConnector._enableNotifications.<anonymous closure> (package:topping_ble_control/bluetooth/connection/ble_connector.dart:248:11)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 设备通知启用成功
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.d (package:topping_ble_control/utils/log_util.dart:20:13)
I/flutter (16188): │ #1   BleCharacteristicHandler.setCharacteristicNotification (package:topping_ble_control/bluetooth/gatt/characteristic_handler.dart:331:13)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 🐛 已取消之前的订阅: 00009CF1-0000-1000-8000-00805F9B34FB
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BluetoothLogManager.deleteVerifyLogFile (package:topping_ble_control/utils/app_log_manager.dart:153:13)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 已删除旧的验证日志文件
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   VerifyInterceptor.interceptSendData (package:topping_ble_control/utils/verify_interceptor.dart:152:11)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 [VERIFY] [阶段1] 数据内容 [会话ID: 1747364506857_1]: 5e c7 ff fe 00 65 78 da 25 cb 4b 0a 80 20 00 84 e1 bb cc da 85 a5 22 78 19 11 95 70 e1 03 95 20 c4 bb a7 b5 1b fe 8f 19 b0 d1 41 1d 04 fd 29 1e 8a 12 34 df 5a c8 49 87 d5 39 81 af d5 66 f7 d3 da b1 5d 50 00 81 33 dd 40 0d 98 52 f4 ed eb be 2c 38 28 5d b6 5b 35 c9 e5 f8 a5 93 71 21 a5 60 98 f3 05 ad 17 22 0b 23 70 b3 43
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   VerifyInterceptor.interceptSendData (package:topping_ble_control/utils/verify_interceptor.dart:161:11)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 [VERIFY] [阶段1] 数据信息 [会话ID: 1747364506857_1]: 数据长度: 107 字节, 时间: 2025-05-16 11:01:46.882516
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
D/[FBP-Android](16188): [FBP] onMethodCall: requestMtu
D/BluetoothGatt(16188): configureMTU() - device: XX:XX:XX:XX:00:1C mtu: 512
D/BluetoothGatt(16188): onConfigureMTU() - Device=53:4A:52:FE:00:1C mtu=263 status=0
D/[FBP-Android](16188): [FBP] onMtuChanged:
D/[FBP-Android](16188): [FBP]   mtu: 263
D/[FBP-Android](16188): [FBP]   status: GATT_SUCCESS (0)
D/[FBP-Android](16188): [FBP] onMethodCall: connect
D/[FBP-Android](16188): [FBP] already connected
D/[FBP-Android](16188): [FBP] onMethodCall: writeCharacteristic
D/[FBP-Android](16188): [FBP] onCharacteristicWrite:
D/[FBP-Android](16188): [FBP]   chr: 8efa
D/[FBP-Android](16188): [FBP]   status: GATT_SUCCESS (0)
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BleCharacteristicHandler._writeToCharacteristic.<anonymous closure> (package:topping_ble_control/bluetooth/gatt/characteristic_handler.dart:169:15)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 写入特征值成功，特征值UUID: 00008efa-0000-1000-8000-00805f9b34fb, 时间戳: 1747364507473
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BleCharacteristicHandler._writeToCharacteristic (package:topping_ble_control/bluetooth/gatt/characteristic_handler.dart:268:11)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 写入操作成功, 特征值UUID: 00008efa-0000-1000-8000-00805f9b34fb, 时间戳: 1747364507476
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 [VERIFY] ✅ 验证结果: 成功 [会话ID: 1747364506857_1] [阶段1]
I/flutter (16188): │ 💡 - 原因: 写入结果
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BleOperationManager.writeCharacteristic (package:topping_ble_control/bluetooth/ble_operation_manager.dart:179:11)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 写特征值成功
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
D/[FBP-Android](16188): [FBP] onMethodCall: discoverServices
D/BluetoothGatt(16188): discoverServices() - device: XX:XX:XX:XX:00:1C
D/BluetoothGatt(16188): onSearchComplete() = Device=53:4A:52:FE:00:1C Status=0
D/[FBP-Android](16188): [FBP] onServicesDiscovered:
D/[FBP-Android](16188): [FBP]   count: 5
D/[FBP-Android](16188): [FBP]   status: 0GATT_SUCCESS
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   VerifyInterceptor.interceptWriteResult (package:topping_ble_control/utils/verify_interceptor.dart:188:11)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 [VERIFY] ✅ ==== 阶段 1 写入成功 ==== [会话ID: 1747364506857_1]
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
D/[FBP-Android](16188): [FBP] onMethodCall: requestMtu
D/BluetoothGatt(16188): configureMTU() - device: XX:XX:XX:XX:00:1C mtu: 512
D/BluetoothGatt(16188): onConfigureMTU() - Device=53:4A:52:FE:00:1C mtu=263 status=0
D/[FBP-Android](16188): [FBP] onMtuChanged:
D/[FBP-Android](16188): [FBP]   mtu: 263
D/[FBP-Android](16188): [FBP]   status: GATT_SUCCESS (0)
D/[FBP-Android](16188): [FBP] onMethodCall: setNotifyValue
D/BluetoothGatt(16188): setCharacteristicNotification() - uuid: 00009cf1-0000-1000-8000-00805f9b34fb enable: true
D/[FBP-Android](16188): [FBP] onDescriptorWrite:
D/[FBP-Android](16188): [FBP]   chr: 9cf1
D/[FBP-Android](16188): [FBP]   desc: 2902
D/[FBP-Android](16188): [FBP]   status: GATT_SUCCESS (0)
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BleCharacteristicHandler.setCharacteristicNotification (package:topping_ble_control/bluetooth/gatt/characteristic_handler.dart:419:19)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 成功启用特征值通知: 00009CF1-0000-1000-8000-00805F9B34FB, 时间戳: 1747364508371
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   BluetoothLogManager._writeLog (package:topping_ble_control/utils/app_log_manager.dart:282:11)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 [VERIFY] ✅ 验证结果: 成功 [会话ID: 1747364506857_1] [阶段1]
I/flutter (16188): │ 💡 - 原因: 写入结果：成功设置特征值通知
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (16188): │ #0   Log.i (package:topping_ble_control/utils/log_util.dart:24:13)
I/flutter (16188): │ #1   VerifyInterceptor.interceptWriteResult (package:topping_ble_control/utils/verify_interceptor.dart:188:11)
I/flutter (16188): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (16188): │ 💡 [VERIFY] ✅ ==== 阶段 1 写入成功 ==== [会话ID: 1747364506857_1]
I/flutter (16188): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
