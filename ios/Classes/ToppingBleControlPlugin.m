#import "ToppingBleControlPlugin.h"

@interface ToppingBleControlPlugin ()
@property (nonatomic, strong) FlutterMethodChannel *channel;
@end

@implementation ToppingBleControlPlugin

static NSString* const TAG = @"ToppingBleControlPlugin";

+ (void)registerWithRegistrar:(NSObject<FlutterPluginRegistrar>*)registrar {
    FlutterMethodChannel* channel = [FlutterMethodChannel
                                     methodChannelWithName:@"com.topping.ble_control/method_channel"
                                     binaryMessenger:[registrar messenger]];
    ToppingBleControlPlugin* instance = [[ToppingBleControlPlugin alloc] initWithChannel:channel];
    [registrar addMethodCallDelegate:instance channel:channel];

    NSLog(@"ToppingBleControlPlugin: 插件已注册");
}

- (instancetype)initWithChannel:(FlutterMethodChannel*)channel {
    self = [super init];
    if (self) {
        self.channel = channel;
    }
    return self;
}

- (void)handleMethodCall:(FlutterMethodCall*)call result:(FlutterResult)result {
    NSLog(@"%@: 收到方法调用: %@", TAG, call.method);

    result(FlutterMethodNotImplemented);
}

@end