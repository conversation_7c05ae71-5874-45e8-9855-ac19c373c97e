import '../model/enums/device_mode_type.dart';

/// 蓝牙操作接口
abstract class BluetoothOperations {
  /// 开始扫描设备
  /// [deviceTypes] 设备类型列表
  /// 如果提供多个设备类型，将同时扫描这些类型的设备
  Future<void> startScan({List<DeviceModeType> deviceTypes});

  /// 停止扫描设备
  void stopScan();

  /// 连接设备
  void connect({required int deviceHandle});

  /// 断开连接
  void disconnect();

  /// 写入特征值
  Future<void> writeCharacteristic(String uuid, List<int> value);

  /// 设置特征值通知
  Future<void> setCharacteristicNotification(String uuid, bool enable);
}
