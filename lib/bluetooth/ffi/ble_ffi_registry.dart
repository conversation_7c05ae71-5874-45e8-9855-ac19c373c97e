/// FFI对象注册表
class BleFfiRegistry {
  static final BleFfiRegistry _instance = BleFfiRegistry._internal();
  factory BleFfiRegistry() => _instance;

  BleFfiRegistry._internal();

  int? _gattNativeObject;
  int? _gattFlutterObject;

  // 为了保持兼容性，仍然提供Map接口，但内部只有一个键值对
  Map<int, int> get nativeToFlutterGattMap {
    if (_gattNativeObject != null && _gattFlutterObject != null) {
      return {_gattNativeObject!: _gattFlutterObject!};
    }
    return {};
  }

  // 获取GATT原生对象
  int? get gattNativeObject => _gattNativeObject;

  // 获取GATT Flutter对象
  int? get gattFlutterObject => _gattFlutterObject;

  // 注册GATT映射 - 只保存一个
  void registerGattMapping(int nativeObject, int flutterObject) {
    // 清除旧的映射
    _gattNativeObject = nativeObject;
    _gattFlutterObject = flutterObject;
  }

  // 移除GATT映射
  void removeGattMapping(int flutterObject) {
    if (_gattFlutterObject == flutterObject) {
      _gattNativeObject = null;
      _gattFlutterObject = null;
    }
  }

  // 清除所有映射
  void clearAllMappings() {
    _gattNativeObject = null;
    _gattFlutterObject = null;
  }
}
