#include "BluetoothGattService.h"
namespace Topping
{
    BluetoothGattService::BluetoothGattService(const std::string &uuid)
    {
        mUUID = uuid;
    }

    BluetoothGattService::~BluetoothGattService()
    {
    }

    void BluetoothGattService::setUUID(const std::string &uuid)
    {
        mUUID = uuid;
    }

    const std::string &BluetoothGattService::getUUID()
    {
        return mUUID;
    }

    void BluetoothGattService::addCharacteristic(const BluetoothGattCharacteristic &characteristic)
    {
        mCharacteristics.push_back(characteristic);
    }

    BluetoothGattCharacteristic *BluetoothGattService::getCharacteristic(const std::string &uuid)
    {
        for (BluetoothGattCharacteristic &characteristic : mCharacteristics)
        {
            if (characteristic.getUUID() == uuid)
            {
                return &characteristic;
            }
        }
        return nullptr;
    }

    size_t BluetoothGattService::getCharacteristicCount()
    {
        return mCharacteristics.size();
    }
}
