import 'dart:async';

import '../event/connection_state_event.dart';
import '../model/bluetooth/ble_device.dart';
import '../model/dx5ii/dx5ii_verify_result_type.dart';

/// 设备事件接口
abstract class DeviceEvents {
  // 扫描结果
  Stream<List<BleDevice>> get scanResults;

  // 设备状态
  Stream<ConnectionStateEvent> get deviceState;

  // 验证结果
  Stream<Dx5iiVerifyResultType> get verifyResult;

  // 电源状态
  Stream<bool> get powerState;

  // 设备名称
  Stream<String> get deviceName;

  // 设备设置
  Stream<dynamic>? get settings => null;

  // 音量
  Stream<int> get volume;

  // 静音
  Stream<bool> get mute;

  // 输入类型
  Stream<int> get inputType;

  // 输出类型
  Stream<int> get outputType;

  // 耳机状态
  Stream<bool> get headphoneEnabled;

  // 耳机增益
  Stream<int> get headphoneGain;

  // 显示模式
  Stream<int> get displayMode;

  // 主题
  Stream<int> get theme;

  // 电源触发
  Stream<int> get powerTrigger;

  // 声道平衡
  Stream<int> get balance;

  // 滤波器
  Stream<int> get filter;

  // 解码模式
  Stream<int> get decodeMode;

  // 蓝牙音频
  Stream<bool> get audioBluetooth;

  // 蓝牙APTX
  Stream<bool> get bluetoothAptx;

  // 继电器
  Stream<bool> get relay;

  // 多功能键
  Stream<int> get multifunctionKey;

  // USB模式
  Stream<int> get usbMode;

  // 屏幕亮度
  Stream<int> get screenBrightness;

  // 语言
  Stream<int> get language;

  // 重置设置
  Stream<void> get onResetSettings;

  // 恢复出厂设置
  Stream<void> get onRestoreFactorySettings;

  //  采样率
  Stream<int> get samplingRate;

  // 是否正在扫描
  Stream<bool> get isScanning;
}
