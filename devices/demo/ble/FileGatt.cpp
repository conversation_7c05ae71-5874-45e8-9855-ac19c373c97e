#include "FileGatt.h"
#include <signal.h>
#include "common.h"
#include "tpprintf.h"
#include "BluetoothGattCharacteristic.h"
#include <unistd.h>
#include <string.h>
#include <sys/stat.h>
#include <fcntl.h>
namespace Topping
{
    static FileGatt *sFileGatt;
    static void sig_handle(int signo)
    {
        uint8_t msg[512];
        ::read(sFileGatt->mNotifyFd, msg, 512);
        int cmd = *((int *)&msg[sizeof(int)]);
        int len = *((int *)&msg[sizeof(int) * 2]);
        switch (cmd)
        {
        case 0:
            sFileGatt->mBluetoothGattCallback->onConnectionStateChange(sFileGatt, BluetoothLeAdapter::BLUETOOTH_PROFILE_STATE_DISCONNECTED, BluetoothLeAdapter::BLUETOOTH_PROFILE_STATE_CONNECTED);
            sFileGatt->mBluetoothGattCallback->onServicesDiscovered(sFileGatt);
            break;
        case 1:
            sFileGatt->mBluetoothGattCallback->onConnectionStateChange(sFileGatt, BluetoothLeAdapter::BLUETOOTH_PROFILE_STATE_CONNECTED, BluetoothLeAdapter::BLUETOOTH_PROFILE_STATE_DISCONNECTED);
            break;
        case 2:
        {
            BluetoothGattService *service = sFileGatt->getService(SERVICE_UUID);
            if (NULL == service)
            {
                TPPRINTF("Service not found by uuid %s\n", SERVICE_UUID);
                return;
            }
            struct BluetoothGattCharacteristic *notifyCharacteristic = service->getCharacteristic(NOTIFY_CHARACTERISTIC_UUID);
            if (NULL == notifyCharacteristic)
            {
                TPPRINTF("Notify characteristic not found by uuid %s\n", NOTIFY_CHARACTERISTIC_UUID);
                return;
            }
            notifyCharacteristic->setValue(&msg[sizeof(int) * 3], len);
            sFileGatt->mBluetoothGattCallback->onCharacteristicChanged(sFileGatt, *notifyCharacteristic);
            break;
        }
        default:
            break;
        }
    }

    FileGatt::FileGatt(int pid, BluetoothLeAdapter::BluetoothGattCallback *bluetoothGattCallback) : mBluetoothGattCallback(bluetoothGattCallback), mNotifyFd(-1), mWriteFd(-1), mPid(pid), mBluetoothGattService(SERVICE_UUID)
    {
        BluetoothGattCharacteristic writeCharacteristic(WRITE_CHARACTERISTIC_UUID, BluetoothGattCharacteristic::CHARACTERISTIC_PROPERTY_WRITE_NO_RESPONSE);
        mBluetoothGattService.addCharacteristic(writeCharacteristic);
        BluetoothGattCharacteristic notifyCharacteristic(NOTIFY_CHARACTERISTIC_UUID, BluetoothGattCharacteristic::CHARACTERISTIC_PROPERTY_NOTIFY);
        mBluetoothGattService.addCharacteristic(notifyCharacteristic);

        mNotifyFd = open("/tmp/" NOTIFY_CHARACTERISTIC_UUID, O_RDONLY);
        mWriteFd = open("/tmp/" WRITE_CHARACTERISTIC_UUID, O_WRONLY);

        connect();
    }

    FileGatt::~FileGatt()
    {
        if (mNotifyFd != -1)
        {
            ::close(mNotifyFd);
        }
        if (mWriteFd != -1)
        {
            ::close(mWriteFd);
        }
    }

    void FileGatt::close()
    {
        delete this;
    }
    
    void FileGatt::connect()
    {
        mBluetoothGattCallback->onConnectionStateChange(this, BluetoothLeAdapter::BLUETOOTH_PROFILE_STATE_DISCONNECTED, BluetoothLeAdapter::BLUETOOTH_PROFILE_STATE_CONNECTED);
        mBluetoothGattCallback->onServicesDiscovered(this);

        union sigval val;
        sigqueue(mPid, 60, val);

        uint8_t msg[512];
        *((int *)msg) = getpid();
        *((int *)&msg[sizeof(int)]) = 0;
        *((int *)&msg[sizeof(int) * 2]) = 0;
        ::write(mWriteFd, msg, 512);
    }

    void FileGatt::disconnect()
    {
        mBluetoothGattCallback->onConnectionStateChange(this, BluetoothLeAdapter::BLUETOOTH_PROFILE_STATE_CONNECTED, BluetoothLeAdapter::BLUETOOTH_PROFILE_STATE_DISCONNECTED);

        union sigval val;
        sigqueue(mPid, 60, val);

        uint8_t msg[512];
        *((int *)msg) = getpid();
        *((int *)&msg[sizeof(int)]) = 1;
        *((int *)&msg[sizeof(int) * 2]) = 0;
        ::write(mWriteFd, msg, 512);
    }
    
    bool FileGatt::requestMtu(int mtu)
    {
        return true;
    }

    bool FileGatt::writeCharacteristic(BluetoothGattCharacteristic &characteristic)
    {
        union sigval val;
        sigqueue(mPid, 60, val);

        uint8_t msg[512];
        *((int *)msg) = getpid();
        *((int *)&msg[sizeof(int)]) = 2;
        *((int *)&msg[sizeof(int) * 2]) = characteristic.getValue().size();
        memcpy(&msg[sizeof(int) * 3], &characteristic.getValue()[0], characteristic.getValue().size());
        ::write(mWriteFd, msg, 512);
        return true;
    }

    bool FileGatt::setCharacteristicNotification(BluetoothGattCharacteristic &characteristic, bool enable)
    {
        sFileGatt = this;
        struct sigaction action;
        sigfillset(&action.sa_mask);
        action.sa_flags = 0;
        action.sa_handler = sig_handle;
        sigaction(60, &action, NULL);
        return true;
    }

    BluetoothGattService *FileGatt::getService(const std::string &uuid)
    {
        return &mBluetoothGattService;
    } 
}