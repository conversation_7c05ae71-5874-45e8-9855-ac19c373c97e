#ifndef __DX5II_DEVICE_H__
#define __DX5II_DEVICE_H__
#include "ControllerScanner.h"
#include "ControllerClient.h"
#include <string>
extern "C" {
enum dx5ii_device_cmd_t
{
    DX5II_DEVICE_CMD_POWER_ON = 0x100,
    DX5II_DEVICE_CMD_SET_DEVICE_NAME = 0x102,
    DX5II_DEVICE_CMD_SET_VOLUME = 0x104,
    DX5II_DEVICE_CMD_SET_MUTE = 0x106,
    DX5II_DEVICE_CMD_SET_INPUT_TYPE = 0x108,
    DX5II_DEVICE_CMD_SET_OUTPUT_TYPE = 0x10a,
    // DX5II_DEVICE_CMD_ENABLE_HEADPHONE = 0x10c, // 已废弃: 新版输出逻辑不再有独立的耳放开关，已整合至 SET_OUTPUT_TYPE
    DX5II_DEVICE_CMD_SET_HEADPHONE_GAIN = 0x10e,
    DX5II_DEVICE_CMD_SET_DISPLAY_MODE = 0x110, // UI主页模式设置(常规/VU/FFT)
    DX5II_DEVICE_CMD_SET_THEME = 0x112,
    DX5II_DEVICE_CMD_SET_POWER_TRIGGER = 0x114,
    DX5II_DEVICE_CMD_SET_BALANCE = 0x116,
    DX5II_DEVICE_CMD_SET_PCM_FILTER = 0x11a,
    DX5II_DEVICE_CMD_SET_DECODE_MODE = 0x11c, // UI线路模式设置(前级/DAC)
    DX5II_DEVICE_CMD_ENABLE_AUDIO_BLUETOOTH = 0x11e,
    DX5II_DEVICE_CMD_ENABLE_BLUETOOTH_APTX = 0x120,
    DX5II_DEVICE_CMD_ENABLE_REMOTE = 0x122,
    // DX5II_DEVICE_CMD_SET_MULTIFUNCTION_KEY = 0x124, // 已废弃: 被新的按键自定义逻辑取代
    DX5II_DEVICE_CMD_SET_USB_MODE = 0x126,
    DX5II_DEVICE_CMD_SET_SCREEN_BRIGHTNESS = 0x128,
    DX5II_DEVICE_CMD_SET_LANGUAGE = 0x12a,
    // DX5II_DEVICE_CMD_RESET_SETTINGS = 0x12c, // 已废弃: 新版无此功能
    DX5II_DEVICE_CMD_RESTORE_FACTORY_SETTINGS = 0x12e,
    DX5II_DEVICE_CMD_GET_SETTINGS = 0x130,
    DX5II_DEVICE_CMD_GET_SAMPLING = 0x132,

    // --- 新增命令 ---
    DX5II_DEVICE_CMD_SET_VU_METER_LEVEL = 0x134, // 新增: 设置VU表0dDB幅值
    DX5II_DEVICE_CMD_SET_VU_METER_DISPLAY_MODE = 0x136, // 新增: 设置VU条显示模式
    DX5II_DEVICE_CMD_SET_OUTPUT_OPTIONS = 0x138, // 新增: 设置可用的输出选项(位掩码)
    DX5II_DEVICE_CMD_SET_INPUT_OPTIONS = 0x13a, // 新增: 设置可用的输入选项(位掩码)
    DX5II_DEVICE_CMD_SET_VOLUME_STEP = 0x13c, // 新增: 设置音量步进
    DX5II_DEVICE_CMD_SET_POLARITY = 0x13e, // 新增: 设置极性
    DX5II_DEVICE_CMD_ENABLE_PEQ = 0x140, // 新增: 启用/禁用PEQ
    DX5II_DEVICE_CMD_SET_PEQ_PRESET = 0x142, // 新增: 设置PEQ预设
    DX5II_DEVICE_CMD_SET_VOLUME_MEMORY_MODE = 0x144, // 新增: 设置音量记忆方式
    DX5II_DEVICE_CMD_SET_PEQ_MEMORY_MODE = 0x146, // 新增: 设置PEQ记忆方式
    DX5II_DEVICE_CMD_SET_MAIN_KEY_FUNCTION = 0x148, // 新增: 设置主按键功能
    DX5II_DEVICE_CMD_SET_REMOTE_A_KEY_FUNCTION = 0x14a, // 新增: 设置遥控A键功能
    DX5II_DEVICE_CMD_SET_REMOTE_B_KEY_FUNCTION = 0x14c, // 新增: 设置遥控B键功能
};

enum dx5ii_device_input_t
{
    DX5II_DEVICE_INPUT_USB = 0,
    DX5II_DEVICE_INPUT_OPTICAL = 1,
    DX5II_DEVICE_INPUT_COAXIAL = 2,
    DX5II_DEVICE_INPUT_BT = 3,
};

// --- 新版输出模式定义 ---
// 用于 SET_OUTPUT_TYPE 命令, 选择一个当前生效的输出模式
enum dx5ii_device_output_t
{
    DX5II_DEVICE_OUTPUT_ALL = 0,                    // 全部输出
    DX5II_DEVICE_OUTPUT_HEADPHONE_ALL = 1,          // 耳放全部输出
    DX5II_DEVICE_OUTPUT_LINE_OUT_ALL = 2,           // 线路全部输出
    DX5II_DEVICE_OUTPUT_HEADPHONE_SINGLE_ENDED = 3, // 耳放单端
    DX5II_DEVICE_OUTPUT_HEADPHONE_BALANCE = 4,      // 耳放平衡
    DX5II_DEVICE_OUTPUT_LINE_OUT_SINGLE_ENDED = 5,  // 线路单端
    DX5II_DEVICE_OUTPUT_LINE_OUT_BALANCE = 6,       // 线路平衡
};

enum dx5ii_device_headphone_gain_t
{
    DX5II_DEVICE_HEADPHONE_GAIN_LOW = 0,
    DX5II_DEVICE_HEADPHONE_GAIN_HIGH = 1,
};

enum dx5ii_device_display_mode_t
{
    DX5II_DEVICE_DISPLAY_MODE_NORMAL    = 0, // 常规
    DX5II_DEVICE_DISPLAY_MODE_VU        = 1, // VU
    DX5II_DEVICE_DISPLAY_MODE_FFT       = 2, // FFT
};

enum dx5ii_device_theme_t
{
    DX5II_DEVICE_THEME_AURORA = 0,
    DX5II_DEVICE_THEME_ORANGE = 1,
    DX5II_DEVICE_THEME_PERU = 2,
    DX5II_DEVICE_THEME_GREEN = 3,
    DX5II_DEVICE_THEME_KHAKI = 4,
    DX5II_DEVICE_THEME_ROSE = 5,
    DX5II_DEVICE_THEME_BLUE = 6,
    DX5II_DEVICE_THEME_PURPLE = 7,
    DX5II_DEVICE_THEME_WHITE = 8,
};

enum dx5ii_device_power_trigger_t
{
    DX5II_DEVICE_POWER_TRIGGER_SIGNAL = 0,
    DX5II_DEVICE_POWER_TRIGGER_VOLTAGE = 1,
    DX5II_DEVICE_POWER_TRIGGER_CLOSE = 2,
};

enum dx5ii_device_filter_t
{
    DX5II_DEVICE_FILTER_PEAKING = 0,
    DX5II_DEVICE_FILTER_LOW_PASS = 1,
    DX5II_DEVICE_FILTER_HIGH_PASS = 2,
    DX5II_DEVICE_FILTER_LOW_SHELF = 3,
    DX5II_DEVICE_FILTER_HIGH_SHELF = 4,
};

enum dx5ii_device_pcm_filter_t
{
    DX5II_DEVICE_PCM_FILTER_1 = 0,
    DX5II_DEVICE_PCM_FILTER_2 = 1,
    DX5II_DEVICE_PCM_FILTER_3 = 2,
    DX5II_DEVICE_PCM_FILTER_4 = 3,
    DX5II_DEVICE_PCM_FILTER_5 = 4,
    DX5II_DEVICE_PCM_FILTER_6 = 5,
    DX5II_DEVICE_PCM_FILTER_7 = 6,
    DX5II_DEVICE_PCM_FILTER_8 = 7,
};

// UI上称为 "线路模式"
enum dx5ii_device_decode_mode_t
{
    DX5II_DEVICE_DECODE_MODE_PRE = 0, // 前级
    DX5II_DEVICE_DECODE_MODE_DAC = 1, // DAC
};

// --- 旧版多功能按键定义 (已废弃) ---
//enum dx5ii_device_multifunction_key_t
//{
//    DX5II_DEVICE_MULTIFUNCTION_KEY_MUTE                 = 0,
//    DX5II_DEVICE_MULTIFUNCTION_KEY_INPUT_SELECT         = 1,
//    DX5II_DEVICE_MULTIFUNCTION_KEY_LINE_OUT_SELECT      = 2,
//    DX5II_DEVICE_MULTIFUNCTION_KEY_HEADPHONE_OUT_SELECT = 3,
//    DX5II_DEVICE_MULTIFUNCTION_KEY_HOME_SELECT          = 4,
//    DX5II_DEVICE_MULTIFUNCTION_KEY_BRIGHTNESS_SELECT    = 5,
//    DX5II_DEVICE_MULTIFUNCTION_KEY_SLEEP                = 6,
//    DX5II_DEVICE_MULTIFUNCTION_KEY_PCM_FILTER_SELECT    = 7,
//    DX5II_DEVICE_MULTIFUNCTION_KEY_PEQ_SELECT           = 8,
//};

enum dx5ii_device_usb_mode_t
{
    DX5II_DEVICE_USB_MODE_UAC1 = 0,
    DX5II_DEVICE_USB_MODE_UAC2 = 1,
};

enum dx5ii_device_screen_brightness_t
{
    DX5II_DEVICE_SCREEN_BRIGHTNESS_LOW = 0,
    DX5II_DEVICE_SCREEN_BRIGHTNESS_MEDIUM = 1,
    DX5II_DEVICE_SCREEN_BRIGHTNESS_HIGH = 2,
    DX5II_DEVICE_SCREEN_BRIGHTNESS_AUTO = 3,
};

enum dx5ii_device_language_t
{
    DX5II_DEVICE_LANGUAGE_EH = 0,
    DX5II_DEVICE_LANGUAGE_CN = 1,
};

enum dx5ii_device_sampling_t
{
    DX5II_DEVICE_FSR_ERR    = 0 ,
    DX5II_DEVICE_FSR_44_1K      ,
    DX5II_DEVICE_FSR_48K        ,
    DX5II_DEVICE_FSR_88_2K      ,
    DX5II_DEVICE_FSR_96K        ,
    DX5II_DEVICE_FSR_176_4K     ,
    DX5II_DEVICE_FSR_192K       ,
    DX5II_DEVICE_FSR_352_8K     ,
    DX5II_DEVICE_FSR_384K       ,
    DX5II_DEVICE_FSR_705_6K     ,
    DX5II_DEVICE_FSR_768K       ,
    DX5II_DEVICE_FSR_2_82M      ,
    DX5II_DEVICE_FSR_5_64M      ,
    DX5II_DEVICE_FSR_11_28M     ,
    DX5II_DEVICE_FSR_22_57M     ,
    DX5II_DEVICE_FSR_45_15M     ,
    DX5II_DEVICE_FSR_49_15M     ,
};

// --- 新增枚举定义 (DX5 III 新功能) ---

// 新增: VU表0dDB幅值
enum dx5ii_vu_meter_level_t
{
    DX5II_VU_METER_LEVEL_4DBU = 0,
    DX5II_VU_METER_LEVEL_10DBU = 1,
};

// 新增: VU条显示模式
enum dx5ii_vu_meter_display_mode_t
{
    DX5II_VU_METER_DISPLAY_ALWAYS_ON = 0,       // 全开
    DX5II_VU_METER_DISPLAY_ON_NORMAL_UI = 1,    // 常规界面
    DX5II_VU_METER_DISPLAY_ON_FFT_UI = 2,       // FFT界面
    DX5II_VU_METER_DISPLAY_OFF = 3,             // 全关
};

// 新增: 输入选项位掩码 (用于多选组合)
enum dx5ii_input_options_t
{
    DX5II_INPUT_OPTION_USB = 0x01,
    DX5II_INPUT_OPTION_OPTICAL = 0x02,
    DX5II_INPUT_OPTION_COAXIAL = 0x04,
    DX5II_INPUT_OPTION_BT = 0x08,
};

// 新增: 输出选项位掩码 (用于多选组合)
enum dx5ii_output_options_t
{
    DX5II_OUTPUT_OPTION_ALL = 0x01,
    DX5II_OUTPUT_OPTION_HEADPHONE_ALL = 0x02,
    DX5II_OUTPUT_OPTION_LINE_OUT_ALL = 0x04,
    DX5II_OUTPUT_OPTION_HEADPHONE_SINGLE_ENDED = 0x08,
    DX5II_OUTPUT_OPTION_HEADPHONE_BALANCE = 0x10,
    DX5II_OUTPUT_OPTION_LINE_OUT_SINGLE_ENDED = 0x20,
    DX5II_OUTPUT_OPTION_LINE_OUT_BALANCE = 0x40,
};

// 新增: 音量步进
enum dx5ii_volume_step_t
{
    DX5II_VOLUME_STEP_0_5_DB = 0,
    DX5II_VOLUME_STEP_1_0_DB = 1,
};

// 新增: 极性设置
enum dx5ii_polarity_t
{
    DX5II_POLARITY_NORMAL = 0,   // 标准
    DX5II_POLARITY_INVERTED = 1, // 反相
};

// 新增: 记忆方式 (音量/PEQ共用)
enum dx5ii_memory_mode_t
{
    DX5II_MEMORY_FOLLOW_INPUT = 0,
    DX5II_MEMORY_FOLLOW_OUTPUT = 1,
    DX5II_MEMORY_OFF = 2,
};

// 新增: 新版按键可分配的功能列表
enum dx5ii_assignable_function_t
{
    DX5II_FUNCTION_INPUT_SELECT = 0,
    DX5II_FUNCTION_OUTPUT_SELECT = 1,
    DX5II_FUNCTION_HOME_SELECT = 2,
    DX5II_FUNCTION_BRIGHTNESS_SELECT = 3,
    DX5II_FUNCTION_SLEEP_SCREEN = 4, // 息屏
    DX5II_FUNCTION_MUTE = 5,
    DX5II_FUNCTION_PEQ_SELECT = 6,
    DX5II_FUNCTION_POWER_TRIGGER_SELECT = 7,
    DX5II_FUNCTION_PCM_FILTER_SELECT = 8,
    DX5II_FUNCTION_HP_GAIN_SELECT = 9,
};

// 新增: PEQ预设选择 (暂时不考虑，但保留定义)
enum dx5ii_peq_preset_t
{
    DX5II_PEQ_PRESET_BASS1 = 0,
    DX5II_PEQ_PRESET_BASS2 = 1,
    DX5II_PEQ_PRESET_AIRY = 2,
    DX5II_PEQ_PRESET_WARM = 3,
    DX5II_PEQ_PRESET_DYNAMIC = 4,
    // Config6-10 为用户自定义
};

struct dx5ii_scan_result_t
{
    char *name;
    long device;
    int rssi;
};

struct dx5ii_settings_t
{
    int is_on;
    char device_name[32];//蓝牙模块只支持名称长度为32，
    int volume;
    int is_mute;
    int input_type;
    int output_type;
    int headphone_enable;
    int headphone_gain;
    int display_mode;
    int theme;
    int power_trigger;
    int balance;
    int pcm_filter;
    int decode_mode;
    int audio_bt_enable;
    int aptx_enable;
    int remote_enable;
    int multifunction_key;
    int usb_mode;
    int screen_brightness;
    int language;
    int sampling;

    // --- 新增字段 (DX5 III 新功能) ---
    int vu_meter_level;              // 新增: VU表0dDB幅值
    int vu_meter_display_mode;       // 新增: VU条显示模式
    int input_options;               // 新增: 输入选项位掩码
    int output_options;              // 新增: 输出选项位掩码
    int volume_step;                 // 新增: 音量步进
    int polarity;                    // 新增: 极性设置
    // int peq_enable;               // 新增: PEQ启用状态 (暂时不考虑)
    // int peq_preset;               // 新增: PEQ预设选择 (暂时不考虑)
    int volume_memory_mode;          // 新增: 音量记忆方式
    int peq_memory_mode;             // 新增: PEQ记忆方式 (保留字段但暂不使用)
    int main_key_function;           // 新增: 主按键功能
    int remote_a_key_function;       // 新增: 遥控A键功能
    int remote_b_key_function;       // 新增: 遥控B键功能
};

struct dx5ii_device_callback_t
{
    void (*on_scan_results)(long flutter_object, struct dx5ii_scan_result_t *results, size_t count);
    void (*on_scan_failed)(long flutter_object, int errorCode);
    void (*on_state_change)(long flutter_object, int state);
    void (*on_verify_result)(long flutter_object, int type);
    void (*on_power_change)(long flutter_object, int is_on);
    void (*on_device_name_change)(long flutter_object, const char *name);
    void (*on_device_volume_change)(long flutter_object, int volume);
    void (*on_device_mute_change)(long flutter_object, int is_mute);
    void (*on_device_input_type_change)(long flutter_object, int input_type);
    void (*on_device_output_type_change)(long flutter_object, int output_type);
    void (*on_device_enable_headphone)(long flutter_object, int enable);
    void (*on_device_headphone_gain_change)(long flutter_object, int headphone_gain);
    void (*on_device_display_mode_change)(long flutter_object, int display_mode);
    void (*on_device_theme_change)(long flutter_object, int theme);
    void (*on_device_power_trigger_change)(long flutter_object, int trigger_type);
    void (*on_device_balance_change)(long flutter_object, int balance);
    void (*on_device_pcm_filter_change)(long flutter_object, int filter_type);
    void (*on_device_decode_mode_change)(long flutter_object, int decode_mode);
    void (*on_device_enable_audio_bluetooth)(long flutter_object, int enable);
    void (*on_device_enable_bluetooth_aptx)(long flutter_object, int enable);
    void (*on_device_enable_remote)(long flutter_object, int enable);
    void (*on_device_multifunction_key_change)(long flutter_object, int key_type);
    void (*on_device_usb_mode_change)(long flutter_object, int usb_mode);
    void (*on_device_screen_brightness_change)(long flutter_object, int brightness_type);
    void (*on_device_language_change)(long flutter_object, int language);
    void (*on_device_reset_settings)(long flutter_object);
    void (*on_device_restore_factory_settings)(long flutter_object);
    void (*on_device_settings_response)(long flutter_object, struct dx5ii_settings_t *dx5ii_settings);
    void (*on_device_sampling_response)(long flutter_object, int sampling);

    // --- 新增回调函数 (DX5 III 新功能) ---
    void (*on_device_vu_meter_level_change)(long flutter_object, int level);                    // 新增: VU表0dDB幅值变化回调
    void (*on_device_vu_meter_display_mode_change)(long flutter_object, int mode);             // 新增: VU条显示模式变化回调
    void (*on_device_input_options_change)(long flutter_object, int options);                  // 新增: 输入选项变化回调
    void (*on_device_output_options_change)(long flutter_object, int options);                 // 新增: 输出选项变化回调
    void (*on_device_volume_step_change)(long flutter_object, int step);                       // 新增: 音量步进变化回调
    void (*on_device_polarity_change)(long flutter_object, int polarity);                      // 新增: 极性变化回调
    // void (*on_device_peq_enable_change)(long flutter_object, int enable);                   // 新增: PEQ启用变化回调 (暂时不考虑)
    // void (*on_device_peq_preset_change)(long flutter_object, int preset);                   // 新增: PEQ预设变化回调 (暂时不考虑)
    void (*on_device_volume_memory_mode_change)(long flutter_object, int mode);                // 新增: 音量记忆方式变化回调
    void (*on_device_peq_memory_mode_change)(long flutter_object, int mode);                   // 新增: PEQ记忆方式变化回调 (保留但暂不使用)
    void (*on_device_main_key_function_change)(long flutter_object, int function);             // 新增: 主按键功能变化回调
    void (*on_device_remote_a_key_function_change)(long flutter_object, int function);         // 新增: 遥控A键功能变化回调
    void (*on_device_remote_b_key_function_change)(long flutter_object, int function);         // 新增: 遥控B键功能变化回调
};

struct dx5ii_device_t
{
    void *handle;
    long flutter_object;
    struct dx5ii_device_callback_t callback;
};

long dx5ii_device_create(long flutter_object);
void dx5ii_device_destory(long native_object);
void dx5ii_device_register_callback(long native_object, struct dx5ii_device_callback_t *callback);
void dx5ii_device_connect(long native_object, long device);
void dx5ii_device_disconnect(long native_object);
void dx5ii_device_verify(long native_object);
void dx5ii_device_power_on(long native_object, int is_on);
void dx5ii_device_set_device_name(long native_object, const char *name);
void dx5ii_device_set_volume(long native_object, int volume);
void dx5ii_device_set_mute(long native_object, int is_mute);
void dx5ii_device_set_input_type(long native_object, int input_type);
void dx5ii_device_set_output_type(long native_object, int output_type);
void dx5ii_device_enable_headphone(long native_object, int enable);
void dx5ii_device_set_headphone_gain(long native_object, int gain_type);
void dx5ii_device_set_diaplay_mode(long native_object, int diaplay_mode);
void dx5ii_device_set_theme(long native_object, int theme);
void dx5ii_device_set_power_trigger(long native_object, int trigger_type);
void dx5ii_device_set_balance(long native_object, int balance);
void dx5ii_device_set_pcm_filter(long native_object, int filter_type);
void dx5ii_device_set_decode_type(long native_object, int decode_type);
void dx5ii_device_enable_audio_bluetooth(long native_object, int enable);
void dx5ii_device_enable_bluetooth_aptx(long native_object, int enable);
void dx5ii_device_enable_remote(long native_object, int enable);
void dx5ii_device_set_multifunction_key(long native_object, int key_type);
void dx5ii_device_set_usb_mode(long native_object, int usb_mode);
void dx5ii_device_set_screen_brightness(long native_object, int brightness_type);
void dx5ii_device_set_language(long native_object, int language);
void dx5ii_device_reset_settings(long native_object);
void dx5ii_device_restore_factory_settings(long native_object);
void dx5ii_device_request_settings(long native_object);
void dx5ii_device_request_sampling(long native_object);

// --- 新增函数声明 (DX5 III 新功能) ---
void dx5ii_device_set_vu_meter_level(long native_object, int level);                          // 新增: 设置VU表0dDB幅值
void dx5ii_device_set_vu_meter_display_mode(long native_object, int mode);                    // 新增: 设置VU条显示模式
void dx5ii_device_set_input_options(long native_object, int options);                         // 新增: 设置输入选项
void dx5ii_device_set_output_options(long native_object, int options);                        // 新增: 设置输出选项
void dx5ii_device_set_volume_step(long native_object, int step);                              // 新增: 设置音量步进
void dx5ii_device_set_polarity(long native_object, int polarity);                             // 新增: 设置极性
// void dx5ii_device_enable_peq(long native_object, int enable);                              // 新增: 启用/禁用PEQ (暂时不考虑)
// void dx5ii_device_set_peq_preset(long native_object, int preset);                          // 新增: 设置PEQ预设 (暂时不考虑)
void dx5ii_device_set_volume_memory_mode(long native_object, int mode);                       // 新增: 设置音量记忆方式
void dx5ii_device_set_peq_memory_mode(long native_object, int mode);                          // 新增: 设置PEQ记忆方式 (保留但暂不使用)
void dx5ii_device_set_main_key_function(long native_object, int function);                    // 新增: 设置主按键功能
void dx5ii_device_set_remote_a_key_function(long native_object, int function);                // 新增: 设置遥控A键功能
void dx5ii_device_set_remote_b_key_function(long native_object, int function);                // 新增: 设置遥控B键功能
}

namespace Topping
{
    class Dx5iiDevice : public ControllerScanner::ScanCallback, public ControllerClient::Callback
    {
    public:
        Dx5iiDevice(dx5ii_device_t *dx5ii_device);
        ~Dx5iiDevice();

        void startScan();
        void stopScan();
        void connect(BluetoothDevice &device);
        void disconnect();
        void verify();
        void powerOn(bool isOn);
        void setDeviceName(const std::string &name);
        void setVolume(int volume);
        void setMute(bool isMute);
        void setInputType(int inputType);
        void setOutputType(int outputType);
        void enableHeadphone(bool enable);
        void setHeadphoneGain(int gainType);
        void setDisplayMode(int displayMode);
        void setTheme(int theme);
        void setPowerTrigger(int triggerType);
        void setBalance(int balance);
        void setPcmFilter(int filterType);
        void setDecodeMode(int decodeMode);
        void enableAudioBluetooth(bool enable);
        void enableBluetoothAPTX(bool enable);
        void enableRemote(bool enable);
        void setMultifunctionKey(int keyType);
        void setUsbMode(int usbMode);
        void setScreenBrightness(int brightnessType);
        void setLanguage(int language);
        void resetSettings();
        void restoreFactorySettings();
        void requestSettings();
        void requestSampling();

        // --- 新增方法声明 (DX5 III 新功能) ---
        void setVuMeterLevel(int level);                          // 新增: 设置VU表0dDB幅值
        void setVuMeterDisplayMode(int mode);                     // 新增: 设置VU条显示模式
        void setInputOptions(int options);                        // 新增: 设置输入选项
        void setOutputOptions(int options);                       // 新增: 设置输出选项
        void setVolumeStep(int step);                             // 新增: 设置音量步进
        void setPolarity(int polarity);                           // 新增: 设置极性
        // void enablePeq(bool enable);                           // 新增: 启用/禁用PEQ (暂时不考虑)
        // void setPeqPreset(int preset);                          // 新增: 设置PEQ预设 (暂时不考虑)
        void setVolumeMemoryMode(int mode);                       // 新增: 设置音量记忆方式
        void setPeqMemoryMode(int mode);                          // 新增: 设置PEQ记忆方式 (保留但暂不使用)
        void setMainKeyFunction(int function);                    // 新增: 设置主按键功能
        void setRemoteAKeyFunction(int function);                 // 新增: 设置遥控A键功能
        void setRemoteBKeyFunction(int function);                 // 新增: 设置遥控B键功能
    protected:
        virtual void onBatchScanResults(const std::vector<ControllerScanner::ScanResult> &results) override;
        virtual void onScanFailed(int errorCode) override;

        virtual void onStateChange(int state) override;
        virtual void onVerifyResult(int type) override;
        virtual void onReceiveRequest(int session_id, int cmd, const std::string &msg) override;
        virtual void onReceiveResponse(int cmd, int errcode, const std::string &errmsg, const std::string &msg) override;

    private:
        void parseRequest(int sessionId, int cmd, const std::string &data);
        void parseResponse(int cmd, const std::string &data);
        void parseResponsePowerOn(const std::string &data);
        void parseResponseSetDeviceName(const std::string &data);
        void parseResponseSetVolume(const std::string &data);
        void parseResponseSetMute(const std::string &data);
        void parseResponseSetInputType(const std::string &data);
        void parseResponseSetOutputType(const std::string &data);
        void parseResponseSetHeadphoneGain(const std::string &data);
        void parseResponseSetDisplayMode(const std::string &data);
        void parseResponseSetTheme(const std::string &data);
        void parseResponseSetPowerTrigger(const std::string &data);
        void parseResponseSetBalance(const std::string &data);
        void parseResponseSetPcmFilter(const std::string &data);
        void parseResponseSetDecodeMode(const std::string &data);
        void parseResponseEnableAudioBluetooth(const std::string &data);
        void parseResponseEnableBluetoothAPTX(const std::string &data);
        void parseResponseEnableRemote(const std::string &data);
        void parseResponseSetUsbMode(const std::string &data);
        void parseResponseSetScreenBrightness(const std::string &data);
        void parseResponseSetLanguage(const std::string &data);
        void parseResponseRestoreFactorySettings(const std::string &data);
        void parseResponseRequestSettings(const std::string &data);
        void parseResponseRequestSampling(const std::string &data);
        void responseCmdError(int session_id, int cmd);
    private:
        ControllerScanner mControllerScanner;
        ControllerClient mControllerClient;
        const SupportedDevice *mSupportedDevice;
        dx5ii_device_t *m_dx5ii_device;
    };
}
#endif