#include "ControllerClient.h"
#include "common.h"
#include "tpprintf.h"
#include "signature_tool.h"
#include <string.h>
#include <stdlib.h>
#include "crc32.h"
#define GATT_PACKAGE_MAGIC 0x5ec7
namespace Topping
{
    ControllerClient::ControllerClient(size_t mtu) : mBluetoothGatt(nullptr), mCallback(nullptr), mState(STATE_DISCONNECTED), mSessionId(0), mRandom(0), mSupportedDevice(nullptr), mBleMtu(mtu)
    {
    }

    ControllerClient::~ControllerClient()
    {
        if (mBluetoothGatt != nullptr)
        {
            mBluetoothGatt->close();
            mBluetoothGatt = nullptr;
        }
    }

    void ControllerClient::connect(BluetoothDevice device, Callback *callback)
    {
        TPPRINTF("[BLE日志] ControllerClient::connect被调用, callback: %p\n", callback);
        mCallback = callback;
        BluetoothManager &bluetoothManager = BluetoothManager::getInstance();
        BluetoothLeAdapter *bluetoothLeAdapter = bluetoothManager.getBluetoothLeAdapter();
        if (nullptr == bluetoothLeAdapter)
        {
            TPPRINTF("BluetoothLeAdapter not found.\n");
            return;
        }
        if(mBluetoothGatt != nullptr)
        {
            mBluetoothGatt->close();
        }
        mBluetoothGatt = bluetoothLeAdapter->connectGatt(device, this);
        printState("connect完成");
    }
    
    void ControllerClient::disconnect()
    {
        if (mBluetoothGatt != nullptr)
        {
            mBluetoothGatt->disconnect();
            mBluetoothGatt = nullptr;
        }
    }

    bool ControllerClient::verify(const SupportedDevice *supportedDevice)
    {
        mSupportedDevice = (SupportedDevice *)supportedDevice;
        cJSON *data_json = cJSON_CreateObject();
        char buff[16];
        sprintf(buff, "%d", PROTOCOL_VERSION);
        cJSON_AddStringToObject(data_json, "app_version", buff);
        mRandom = ::rand();
        sprintf(buff, "%d", mRandom);
        cJSON_AddStringToObject(data_json, "app_random", buff);
        const char *dump = cJSON_Print(data_json);
        cJSON_Delete(data_json);
        bool ret = sendRequest(CMD_VERIFY_DEVICE_REQUEST, dump, false);
        cJSON_free((void *)dump);
        return ret;
    }
    
    bool ControllerClient::sendRequest(int cmd, const std::string &data)
    {
        return sendRequest(cmd, data, true);
    }

    bool ControllerClient::sendResponse(int session_id, int cmd, int errcode, const std::string &errmsg, const std::string &data)
    {
        return sendResponse(session_id, cmd, errcode, errmsg, data, true);
    }

    void ControllerClient::onConnectionStateChange(BluetoothGatt *bluetoothGatt, int state, int newState)
    {
        switch (newState)
        {
        case BluetoothLeAdapter::BLUETOOTH_PROFILE_STATE_CONNECTED:
            mState = STATE_CONNECTED_UNSAFE;
            if (mCallback != nullptr)
            {
                mCallback->onStateChange(STATE_CONNECTED_UNSAFE);
            }
            // bluetoothGatt->requestMtu(512);
            break;
        case BluetoothLeAdapter::BLUETOOTH_PROFILE_STATE_DISCONNECTED:
            mState = STATE_DISCONNECTED;
            if (mCallback != nullptr)
            {
                mCallback->onStateChange(STATE_DISCONNECTED);
            }
            break;
        default:
            break;
        }
    }
    
    void ControllerClient::onServicesDiscovered(BluetoothGatt *bluetoothGatt)
    {
        BluetoothGattService *service = bluetoothGatt->getService(SERVICE_UUID);
        if (nullptr == service)
        {
            TPPRINTF("Service not found by uuid %s\n", SERVICE_UUID);
            return;
        }
        BluetoothGattCharacteristic *notifyCharacteristic = service->getCharacteristic(NOTIFY_CHARACTERISTIC_UUID);
        if (nullptr == notifyCharacteristic)
        {
            TPPRINTF("Notify Characteristic not found by uuid %s\n", NOTIFY_CHARACTERISTIC_UUID);
            return;
        }
        bluetoothGatt->setCharacteristicNotification(*notifyCharacteristic, true);
    }

    void ControllerClient::onCharacteristicChanged(BluetoothGatt *bluetoothGatt, BluetoothGattCharacteristic &characteristic)
    {
        TPPRINTF("[BLE日志] ControllerClient::onCharacteristicChanged被调用 - UUID: %s\n", 
                 characteristic.getUUID().c_str());
        printState("onCharacteristicChanged开始");
        
        if (characteristic.getUUID() == NOTIFY_CHARACTERISTIC_UUID)
        {
            TPPRINTF("[BLE日志] UUID匹配NOTIFY_CHARACTERISTIC_UUID, 准备调用mergeCharacteristicPackage\n");
            const std::vector<uint8_t> &value = characteristic.getValue();
            
            // 打印数据的前几个字节
            if (!value.empty()) {
                TPPRINTF("[BLE日志] 特征值数据长度: %zu, 前8字节(或更少): ", value.size());
                for (size_t i = 0; i < value.size() && i < 8; i++) {
                    TPPRINTF("%02x ", value[i]);
                }
                TPPRINTF("\n");
            }
            
            mergeCharacteristicPackage(&characteristic.getValue()[0], characteristic.getValue().size());
            TPPRINTF("[BLE日志] mergeCharacteristicPackage调用完成\n");
        }
        else
        {
            TPPRINTF("[BLE日志] UUID不匹配，期望: %s, 实际: %s\n", 
                    NOTIFY_CHARACTERISTIC_UUID, characteristic.getUUID().c_str());
        }
        
        printState("onCharacteristicChanged结束");
    }

    bool ControllerClient::sendRequest(int cmd, const std::string &data, bool isSafe)
    {
        if ((isSafe && mState == STATE_CONNECTED) || !isSafe)
        {
            cJSON *rootJson = cJSON_CreateObject();
            cJSON_AddNumberToObject(rootJson, "cmd", cmd);
            cJSON_AddNumberToObject(rootJson, "type", 0.0);
            cJSON_AddNumberToObject(rootJson, "session_id", mSessionId++);
            cJSON_AddNumberToObject(rootJson, "errcode", 0.0);
            cJSON_AddStringToObject(rootJson, "errmsg", "");
            cJSON_AddRawToObject(rootJson, "data", data.c_str());
            char *dump = cJSON_Print(rootJson);
            cJSON_Minify(dump);
            cJSON_Delete(rootJson);
            bool ret = writeCharacteristic(dump);
            cJSON_free((void *)dump);
            return ret;
        }
        return false;
    }

    bool ControllerClient::sendResponse(int session_id, int cmd, int errcode, const std::string &errmsg, const std::string &data, bool isSafe)
    {
        if ((isSafe && mState == STATE_CONNECTED) || !isSafe)
        {
            cJSON *rootJson = cJSON_CreateObject();
            cJSON_AddNumberToObject(rootJson, "cmd", cmd);
            cJSON_AddNumberToObject(rootJson, "type", 1.0);
            cJSON_AddNumberToObject(rootJson, "session_id", session_id);
            cJSON_AddNumberToObject(rootJson, "errcode", errcode);
            cJSON_AddStringToObject(rootJson, "errmsg", errmsg.c_str());
            cJSON_AddRawToObject(rootJson, "data", data.c_str());
            char *dump = cJSON_Print(rootJson);
            cJSON_Minify(dump);
            cJSON_Delete(rootJson);
            bool ret = writeCharacteristic(dump);
            cJSON_free((void *)dump);
            return ret;
        }
        return false;
    }

    void ControllerClient::parseMsg(const std::string &msg)
    {
        cJSON *rootJson = cJSON_Parse(msg.c_str());
        if (NULL == rootJson)
        {
            TPPRINTF("Message format error.\n");
            return;
        }
        if (!cJSON_IsObject(rootJson))
        {
            TPPRINTF("Message format error.\n");
            cJSON_Delete(rootJson);
            return;
        }
        cJSON *cmdJson = cJSON_GetObjectItem(rootJson, "cmd");
        if (NULL == cmdJson)
        {
            TPPRINTF("Message format error.\n");
            cJSON_Delete(rootJson);
            return;
        }
        int cmd = (int)cJSON_GetNumberValue(cmdJson);

        cJSON *typeJson = cJSON_GetObjectItem(rootJson, "type");
        if (NULL == typeJson)
        {
            TPPRINTF("Message format error.\n");
            cJSON_Delete(rootJson);
            return;
        }
        int type = (int)cJSON_GetNumberValue(typeJson);

        cJSON *sessionIdJson = cJSON_GetObjectItem(rootJson, "session_id");
        if (NULL == sessionIdJson)
        {
            TPPRINTF("Message format error.\n");
            cJSON_Delete(rootJson);
            return;
        }
        int sessionId = (int)cJSON_GetNumberValue(sessionIdJson);

        cJSON *errcodeJson = cJSON_GetObjectItem(rootJson, "errcode");
        if (NULL == errcodeJson)
        {
            TPPRINTF("Message format error.\n");
            cJSON_Delete(rootJson);
            return;
        }
        int errcode = (int)cJSON_GetNumberValue(errcodeJson);

        cJSON *errmsgJson = cJSON_GetObjectItem(rootJson, "errmsg");
        if (NULL == errmsgJson)
        {
            TPPRINTF("Message format error.\n");
            cJSON_Delete(rootJson);
            return;
        }
        const char *errmsg = cJSON_GetStringValue(errmsgJson);

        cJSON *data_json = cJSON_GetObjectItem(rootJson, "data");
        if (NULL == data_json)
        {
            TPPRINTF("Message format error.\n");
            cJSON_Delete(rootJson);
            return;
        }

        if (0 == type)
        {
            parseRequest(sessionId, cmd, data_json);
        }
        else
        {
            parseResponse(cmd, errcode, errmsg, data_json);
        }
        cJSON_Delete(rootJson);
    }

    void ControllerClient::parseRequest(int sessionId, int cmd, cJSON *json)
    {
        if (mCallback != nullptr)
        {
            const char *dump = cJSON_Print(json);
            mCallback->onReceiveRequest(sessionId, cmd, dump);
            cJSON_free((void *)dump);
        }
    }

    void ControllerClient::parseResponse(int cmd, int errcode, const std::string &errmsg, cJSON *json)
    {
        switch (cmd)
        {
            TPPRINTF("parseResponse; cmd:%d\n", cmd);
        case CMD_VERIFY_DEVICE_REQUEST:
            if (errcode != 0)
            {
                TPPRINTF("%s\n", errmsg.c_str());
                return;
            }
            parseVerifyDeviceResponse(json);
            break;
        case CMD_VERIFY_APP_REQUEST:
            if (errcode != 0)
            {
                TPPRINTF("%s\n", errmsg.c_str());
                if (mCallback != nullptr)
                {
                    mCallback->onVerifyResult(VERIFY_RESULT_APP_UNSUPPORTED);
                }
                return;
            }
            parseVerifyAppResponse(json);
            break;
        default:
            if (mCallback != nullptr)
            {
                const char *dump = cJSON_Print(json);
                mCallback->onReceiveResponse(cmd, errcode, errmsg, dump);
                cJSON_free((void *)dump);
            }
            break;
        }
    }

    void ControllerClient::parseVerifyDeviceResponse(cJSON *json)
    {
        cJSON *devVersionJson = cJSON_GetObjectItem(json, "dev_version");
        if (NULL == devVersionJson)
        {
            TPPRINTF("Message format error.\n");
            return;
        }
        const char *devVersion = cJSON_GetStringValue(devVersionJson);

        cJSON *devRandomJson = cJSON_GetObjectItem(json, "dev_random");
        if (NULL == devRandomJson)
        {
            TPPRINTF("Message format error.\n");
            return;
        }
        const char *devRandom = cJSON_GetStringValue(devRandomJson);

        cJSON *vendorIdJson = cJSON_GetObjectItem(json, "vendor_id");
        if (NULL == vendorIdJson)
        {
            TPPRINTF("Message format error.\n");
            return;
        }
        const char *vendorId = cJSON_GetStringValue(vendorIdJson);

        cJSON *productIdJson = cJSON_GetObjectItem(json, "product_id");
        if (NULL == productIdJson)
        {
            TPPRINTF("Message format error.\n");
            return;
        }
        const char *productId = cJSON_GetStringValue(productIdJson);

        cJSON *signatureJson = cJSON_GetObjectItem(json, "signature");
        if (NULL == signatureJson)
        {
            TPPRINTF("Message format error.\n");
            return;
        }
        const char *signature = cJSON_GetStringValue(signatureJson);

        if (nullptr == mSupportedDevice)
        {
            return;
        }

        int devVersionNum = atoi(devVersion);
        if (devVersionNum / 100 < PROTOCOL_VERSION / 100)
        {
            if (mCallback != nullptr)
            {
                mCallback->onVerifyResult(VERIFY_RESULT_DEVICE_PROTOCOL_VERSION_LESSER);
            }
            return;
        }
        else if (devVersionNum / 100 > PROTOCOL_VERSION / 100)
        {
            if (mCallback != nullptr)
            {
                mCallback->onVerifyResult(VERIFY_RESULT_APP_PROTOCOL_VERSION_LESSER);
            }
            return;
        }
        
        if (std::to_string(mSupportedDevice->vendorId) != vendorId || std::to_string(mSupportedDevice->productId) != productId)
        {
            if (mCallback != nullptr)
            {
                mCallback->onVerifyResult(VERIFY_RESULT_DEVICE_UNSUPPORTED);
            }
            return;
        }
        
        std::string msg = devVersion;
        msg += devRandom;
        msg += vendorId;
        msg += productId;
        msg += std::to_string(mRandom);
        if (msg_verify(msg.c_str(), signature, mSupportedDevice->publicKey) == 0)
        {
            if (mCallback != nullptr)
            {
                mCallback->onVerifyResult(VERIFY_RESULT_DEVICE_UNSUPPORTED);
            }
            return;
        }
        msg = std::to_string(VENDOR_ID);
        msg += devRandom;
        signature = msg_sign(msg.c_str(), PRIVATE_KEY);
        cJSON *data_json = cJSON_CreateObject();
        char buff[16];
        sprintf(buff, "%d", VENDOR_ID);
        cJSON_AddStringToObject(data_json, "vendor_id", buff);
        cJSON_AddStringToObject(data_json, "signature", signature);
        const char *dump = cJSON_Print(data_json);
        cJSON_Delete(data_json);
        sendRequest(CMD_VERIFY_APP_REQUEST, dump, false);
        cJSON_free((void *)dump);
        free((void *)signature);
    }

    void ControllerClient::parseVerifyAppResponse(cJSON *json)
    {
        mState = STATE_CONNECTED;
        if (mCallback != nullptr)
        {
            mCallback->onStateChange(STATE_CONNECTED);
        }
    }

    bool ControllerClient::writeCharacteristic(const char *data)
    {
        bool ret = false;
        size_t srcLen = strlen(data);
        mz_ulong destLen = mz_compressBound(srcLen) * 2;
        unsigned char *dest = (unsigned char *)malloc(destLen + 4);
        if (NULL == dest)
        {
            TPPRINTF("Out of memory.\n");
            return false;
        }
        mz_compress2(dest, &destLen, (unsigned char *)data, srcLen, MZ_UBER_COMPRESSION);
        size_t index = 0;
        size_t len;
        for (size_t i = 0; ; i += len, index++)
        {
            len = mBleMtu - 6 < destLen - i ? mBleMtu - 6 : destLen - i;
            if (len <= mBleMtu - 10)
            {
                if (0 == i)
                {
                    // 单包
                    uint32_t *crc32 = (uint32_t *)&dest[len];
                    *crc32 = toBe32(cal_crc32(dest, destLen));
                    ret = writeCharacteristicPackage(0xfffe, dest, len + 4);
                    break;
                }
                else
                {
                    // 最后一个包
                    uint32_t *crc32 = (uint32_t *)&dest[i + len];
                    *crc32 = toBe32(cal_crc32(dest, destLen));
                    ret = writeCharacteristicPackage(0xffff, &dest[i], len + 4);
                    break;
                }
            }
            else
            {
                // 满包
                if (!writeCharacteristicPackage((uint16_t)index, &dest[i], len))
                {
                    ret = false;
                    break;
                }
            }
        }
        free(dest);
        return ret;
    }

    bool ControllerClient::isLe()
    {
        union
        {
            char c;
            int i;
        } va;
        va.i = 1;
        return va.c > 0 ? true : false;
    }

    uint16_t ControllerClient::fromBe16(uint16_t value)
    {
        if (isLe())
        {
            return (value << 8 & 0xff00) | (value >> 8 & 0x00ff);
        }
        else
        {
            return value;
        }
    }

    uint16_t ControllerClient::toBe16(uint16_t value)
    {
        return fromBe16(value);
    }

    uint32_t ControllerClient::fromBe32(uint32_t value)
    {
        if (isLe())
        {
            return (value << 24 & 0xff000000) | (value << 8 & 0x00ff0000) | (value >> 8 & 0x0000ff00) | (value >> 24 & 0x000000ff);
        }
        else
        {
            return value;
        }
    }

    uint32_t ControllerClient::toBe32(uint32_t value)
    {
        return fromBe32(value);
    }

    bool ControllerClient::writeCharacteristicPackage(uint16_t index, const uint8_t *data, size_t len)
    {
        BluetoothGattService *service = mBluetoothGatt->getService(SERVICE_UUID);
        if (nullptr == service)
        {
            TPPRINTF("Service not found by uuid %s\n", SERVICE_UUID);
            return false;
        }
        BluetoothGattCharacteristic *characteristic = service->getCharacteristic(WRITE_CHARACTERISTIC_UUID);
        if (nullptr == characteristic)
        {
            TPPRINTF("Write Characteristic not found by uuid %s\n", WRITE_CHARACTERISTIC_UUID);
            return false;
        }
        uint8_t *buff = new uint8_t[len + 6];
        ::memset(buff, 0, len + 6);
        *((uint16_t *)buff) = toBe16(GATT_PACKAGE_MAGIC);
        *((uint16_t *)&buff[2]) = toBe16(index);
        *((uint16_t *)&buff[4]) = toBe16(len);
        ::memcpy(&buff[6], data, len);
        characteristic->setValue(buff, len + 6);
        bool ret = mBluetoothGatt->writeCharacteristic(*characteristic);
        delete[] buff;
        return ret;
    }

    void ControllerClient::mergeCharacteristicPackage(const uint8_t *data, size_t len)
    {
        if (len < 6)
        {
            TPPRINTF("[BLE日志] 错误: 数据长度(%zu)小于6, 不是有效的gatt包\n", len);
            return;
        }

        uint16_t magic = fromBe16(*((uint16_t *)data));
        if (magic != GATT_PACKAGE_MAGIC)
        {
            TPPRINTF("[BLE日志] 错误: 无效的包魔数, 期望: 0x%04x, 实际: 0x%04x\n", 
                    GATT_PACKAGE_MAGIC, magic);
            return;
        }
        
        uint16_t index = fromBe16(*((uint16_t *)&data[2]));
        TPPRINTF("[BLE日志] 包索引: %u\n", index);
        size_t dataLen = fromBe16(*((uint16_t *)&data[4]));
        if (dataLen + 6 != len)
        {
            TPPRINTF("The notify data is corrupted\n");
            return;
        }
        if (index < 0xfffe)
        {
            //满包
            if (0 == index || mNotifyData.size() >= NOTIFY_MAX_BUFF_SIZE)
            {
                TPPRINTF("[BLE日志] 索引为0，清空接收缓冲区\n");
                mNotifyData.clear();
            }
            for (size_t i = 0; i < dataLen; i++)
            {
                mNotifyData.push_back(data[6 + i]);
            }
        }
        else
        {
            if (0xfffe == index)
            {
                // 单包
                mNotifyData.clear();
                for (size_t i = 0; i < dataLen - 4; i++)
                {
                    mNotifyData.push_back(data[6 + i]);
                }
            }
            else
            {
                // 最后一个包
                if (dataLen < 4)
                {
                    TPPRINTF("The notify data is corrupted\n");
                    return;
                }
                for (size_t i = 0; i < dataLen - 4; i++)
                {
                    mNotifyData.push_back(data[6 + i]);
                }
            }
            TPPRINTF("[BLE日志] 收到最终包，准备验证CRC32\n");
            uint32_t crc32 = fromBe32(*((uint32_t *)&data[6 + dataLen - 4]));
            if (crc32 == cal_crc32((uint8_t *)&mNotifyData[0], mNotifyData.size()))
            {
                mz_ulong destLen = mz_deflateBound(NULL, mNotifyData.size()) * 8;
                unsigned char *dest = (unsigned char *)malloc(destLen + 1);
                if (NULL == dest)
                {
                    TPPRINTF("Out of memory.\n");
                    return;
                }
                if (mz_uncompress(dest, &destLen, &mNotifyData[0], mNotifyData.size()) == MZ_OK)
                {
                    dest[destLen] = 0;
                    parseMsg((char *)dest);
                }
                else
                {
                    TPPRINTF("The notify data is corrupted\n");
                }
                free(dest);
            }
            else
            {
                TPPRINTF("[BLE日志] 错误: 数据损坏，CRC32校验失败\n");
            }
        }
        TPPRINTF("[BLE日志] mergeCharacteristicPackage处理完成\n");
    }

    void ControllerClient::printState(const char* caption)
    {
        TPPRINTF("[BLE日志] ControllerClient状态(%s):\n", caption);
        TPPRINTF("  - this指针: %p\n", this);
        TPPRINTF("  - mBluetoothGatt: %p\n", mBluetoothGatt);
        TPPRINTF("  - mCallback: %p\n", mCallback);
        TPPRINTF("  - mState: %d\n", mState);
        TPPRINTF("  - mBleMtu: %zu\n", mBleMtu);
        
        if (mBluetoothGatt != nullptr) {
            TPPRINTF("  - BluetoothGatt有效\n");
        } else {
            TPPRINTF("  - 警告: BluetoothGatt为空!\n");
        }
        
        if (mCallback != nullptr) {
            TPPRINTF("  - Callback有效\n");
        } else {
            TPPRINTF("  - 警告: Callback为空!\n");
        }
    }
}