#include "DemoDevice.h"
#include <stdio.h>
#include <common.h>
namespace Topping
{
    DemoDevice::DemoDevice() : mControllerClient(260), mSupportedDevice(&SUPPORTED_DEVICES[0])
    {
    }

    DemoDevice::~DemoDevice()
    {
    }

    void DemoDevice::startScan()
    {
        mControllerScanner.startScan(this);
    }

    void DemoDevice::stopScan()
    {
        mControllerScanner.stopScan(this);
    }

    void DemoDevice::connect()
    {
        mControllerClient.connect(mDevice, this);
    }

    void DemoDevice::disconnect()
    {
        mControllerClient.disconnect();
    }

    void DemoDevice::verify()
    {
        mControllerClient.verify(mSupportedDevice);
    }

    void DemoDevice::sendMsg(const std::string &msg)
    {
        mControllerClient.sendRequest(DEMO_DEVICE_CMD_SEND_MSG, msg);
    }

    void DemoDevice::onBatchScanResults(const std::vector<ControllerScanner::ScanResult> &results)
    {
        for (auto result : results)
        {
            mDevice = result.device;
            printf("name:%s, rssi:%d\n", result.device.getName().c_str(), result.rssi);
        }
    }

    void DemoDevice::onScanFailed(int errorCode)
    {
        printf("onScanFailed; errorCode:%d\n", errorCode);
    }
    
    void DemoDevice::onStateChange(int state)
    {
        printf("onStateChange; state:%d\n", state);
    }

    void DemoDevice::onVerifyResult(int type)
    {
        printf("onVerifyResult; type:%d\n", type);
    }

    void DemoDevice::onReceiveRequest(int session_id, int cmd, const std::string &msg)
    {
        printf("onReceiveRequest; session_id:%d; cmd:%d; msg:%s\n", session_id, cmd, msg.c_str());
    }

    void DemoDevice::onReceiveResponse(int cmd, int errcode, const std::string &errmsg, const std::string &msg)
    {
        printf("onReceiveResponse; cmd:%d; errcode:%d; errmsg:%s; msg:%s\n", cmd, errcode, errmsg.c_str(), msg.c_str());
    }
}