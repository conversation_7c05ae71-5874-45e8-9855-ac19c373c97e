import 'dart:async';
import 'dart:ffi';

import '../../event/connection_state_event.dart';
import '../../model/bluetooth/ble_device.dart';
import '../../model/dx5ii/dx5ii_callback.dart';
import '../../model/dx5ii/dx5ii_settings.dart';
import '../../model/enums/ble_connection_state.dart';
import '../../model/ffi/ffi_dx5ii_settings.dart';
import '../../model/base/topping_verify_result_type.dart';
import '../../registry/current_connecting_device.dart';
import '../../registry/device_data_manager.dart';
import '../../utils/log_util.dart';
import '../../utils/verify_interceptor.dart';
import '../../utils/app_log_manager.dart';
import '../topping_device_manager.dart';
import 'dx5ii_device_bindings.dart';

/// Dx5ii设备管理器
class Dx5iiDeviceManager extends ToppingDeviceManager {
  // 单例实现
  static final Dx5iiDeviceManager _instance = Dx5iiDeviceManager._internal();

  // 工厂构造函数
  factory Dx5iiDeviceManager() {
    return _instance;
  }

  // 蓝牙日志管理器
  final BluetoothLogManager _logManager = BluetoothLogManager.instance;

  // 私有构造函数
  Dx5iiDeviceManager._internal() {
    Log.i("[BLE日志 GATT] DX5II设备管理器初始化");
    // 初始化父类控制器和监听器
    initializeControllers();
    initializeBleListeners();

    // 初始化 DX5II 特有的绑定
    _initializeBindings();
  }

  // 将构造函数改为公共的，并初始化 StreamControllers 和绑定
  // Dx5iiDeviceManager() {
  //   // 初始化父类控制器和监听器
  //   initializeControllers();
  //   initializeBleListeners();

  //   // 初始化 DX5II 特有的绑定
  //   _initializeBindings();
  // }

  /// 获取设备绑定实例（模板方法实现）
  @override
  Dx5iiDeviceBindings get deviceBindings => Dx5iiDeviceBindings.instance;

  /// 获取设备类型名称（模板方法实现）
  @override
  String get deviceTypeName => "DX5II";

  /// 模板方法 - 原生连接实现
  @override
  void connectNative(int deviceHandle) {
    Dx5iiDeviceBindings.instance.connect(deviceHandle);
    Log.i("DX5II 底层连接函数调用完成, 时间戳: ${DateTime.now().millisecondsSinceEpoch}");
  }

  /// 初始化绑定
  void _initializeBindings() {
    Dx5iiDeviceBindings.instance.initialize(
      Dx5iiCallback(
        onScanResults: _handleScanResults,
        onScanFailed: _handleScanFailed,
        onStateChange: _handleStateChange,
        onVerifyResult: _handleVerifyResult,
        onPowerChange: _handlePowerChange,
        onDeviceSettingsResponse: _handleSettingsResponse,
        onVolumeChange: _handleVolumeChange,
        onMuteChange: _handleMuteChange,
        onInputTypeChange: _handleInputTypeChange,
        onOutputTypeChange: _handleOutputTypeChange,
        onHeadphoneEnabledChange: _handleHeadphoneEnabledChange,
        onHeadphoneGainChange: _handleHeadphoneGainChange,
        onDisplayModeChange: _handleDisplayModeChange,
        onThemeChange: _handleThemeChange,
        onPowerTriggerChange: _handlePowerTriggerChange,
        onBalanceChange: _handleBalanceChange,
        onPcmFilterChange: _handlePcmFilterChange,
        onDecodeModeChange: _handleDecodeModeChange,
        onAudioBluetoothChange: _handleAudioBluetoothChange,
        onBluetoothAptxChange: _handleBluetoothAptxChange,
        onRemoteEnabledChange: _handleRelayChange,
        onMultifunctionKeyChange: _handleMultifunctionKeyChange,
        onUsbModeChange: _handleUsbModeChange,
        onScreenBrightnessChange: _handleScreenBrightnessChange,
        onLanguageChange: _handleLanguageChange,
        onSamplingResponse: _handleSamplingResponse,
      ),
    );
  }

  // _handleScanResults 和 _handleScanFailed 由父类 BleOperationManager 通过 Stream 提供，
  // 这里可以移除，除非有DX5II特有的处理逻辑
  void _handleScanResults(List<dynamic> nativeResults) {
    // 如果有DX5II特有的扫描结果处理逻辑，请在此实现
    // 否则，依赖父类的通用处理
    Log.i("DX5II: 处理扫描结果: count=${nativeResults.length}");
  }

  void _handleScanFailed(int errorCode) {
    Log.i("DX5II: 处理扫描失败: $errorCode");
  }

  /// 处理状态变更
  void _handleStateChange(int state) {
    BleConnectionState connectionState = BleConnectionState.fromValue(state);
    int? handle = CurrentConnectingDevice().handle;
    if (handle == null) return;
    String? deviceName;

    BleDevice? device = DeviceDataManager().getDeviceByHandle(handle);
    deviceName = device?.name;

    Log.i(
      "DX5II: 收到状态变更: ${connectionState.toString()}, 设备句柄: $handle, 时间戳: ${DateTime.now().millisecondsSinceEpoch}",
    );

    // 记录连接状态变化到蓝牙日志
    _logManager.logConnectionState(
      deviceName ?? "设备",
      "状态变化为 ${connectionState.toString()}",
    );

    // 处理连接断开或失败时的清理工作
    if (connectionState == BleConnectionState.disconnected ||
        connectionState == BleConnectionState.disconnecting) {
      // 清理 VerifyInterceptor 状态
      if (VerifyInterceptor.instance.isVerifying) {
        Log.w("$deviceTypeName: 连接丢失/失败于验证期间，停止验证会话。");
        VerifyInterceptor.instance.stopVerifying();
      }

      // 不在状态回调中处理资源释放
      // 这将由device_factory中的disconnectCurrentDevice方法处理
    }

    if (connectionState == BleConnectionState.connectedUnsafe) {
      verify();
      return;
    }

    final event = ConnectionStateEvent(
      connectionState,
      handle,
      deviceName: deviceName,
    );

    // 使用安全的方法传递事件
    if (!deviceStateController.isClosed) {
      deviceStateController.add(event);
    }
  }

  // 修改回调参数类型以匹配父类定义
  void _handleVerifyResult(int type) {
    Log.i(
      "DX5II: 收到验证结果: $type, 时间戳: ${DateTime.now().millisecondsSinceEpoch}",
    );
    // 记录验证结果
    final ToppingVerifyResultType result = mapVerifyTypeToEnum(type);
    safeAddEvent(verifyResultController, result);

    // 记录验证结果到蓝牙日志
    if (result == ToppingVerifyResultType.success) {
      _logManager.logInfo("设备验证成功: DX5II设备已通过验证");
      _logManager.logCommand("设备验证过程完成");

      // 使用新的方法设置验证结果，而不是通过写入结果
      VerifyInterceptor.instance
          .setVerifyResult(true, reason: "设备返回验证成功结果")
          .then((_) {
            // 检查是否有更多阶段需要验证
            final currentStage = VerifyInterceptor.instance.currentVerifyStage;

            // 假设总共有2个阶段，可以根据实际情况调整
            final int totalStages = 2;

            if (currentStage >= totalStages) {
              // 如果是最后一个阶段，结束验证
              Log.i("DX5II: 所有验证阶段完成，结束验证");
              VerifyInterceptor.instance.stopVerifying();
              // 移除自动请求设备设置和采样率的逻辑
            } else {
              // 如果还有后续阶段，不结束验证模式
              Log.i("DX5II: 验证阶段$currentStage成功，等待后续阶段继续验证");
              _logManager.logCommand("验证阶段$currentStage成功，等待后续阶段");
              // 这里可以触发下一阶段验证逻辑，如果需要主动触发的话
              // 例如：_startNextVerificationStage();
            }
          });
    } else {
      final errorMsg = "设备验证失败: ${result.toString()}";
      _logManager.logError(errorMsg);
      _logManager.logCommand("设备验证过程失败，原因: $result");

      // 失败时设置验证结果并结束验证
      VerifyInterceptor.instance
          .setVerifyResult(false, reason: "设备返回验证失败结果: $result")
          .then((_) {
            // 验证失败时立即结束验证模式
            VerifyInterceptor.instance.stopVerifying();
          });
    }
  }

  void _handlePowerChange(bool isOn) {
    Log.i("DX5II: 处理电源状态变化: $isOn");
    safeAddEvent(powerStateController, isOn);
  }

  /// 设备设置响应处理
  void _handleSettingsResponse(Pointer<FFIDx5iiSettings> ffiSettingsPtr) {
    Log.i("DX5II: 处理设备设置响应");

    // 记录到蓝牙日志系统
    _logManager.logCommand("正在处理设备设置响应");

    if (ffiSettingsPtr == nullptr) {
      Log.e("DX5II: 处理设备设置响应失败：接收到的指针为空");
      _logManager.logError("处理设备设置响应失败：接收到的指针为空");
      return;
    }

    try {
      final settings = Dx5iiSettings.fromFFI(ffiSettingsPtr);

      // toString
      Log.i(settings.toString());

      // 转换音量为UI显示所需的负值
      int displayVolume = -settings.volume;

      // 使用safeAddEvent分别更新各个控制器
      safeAddEvent(deviceNameController, settings.deviceName);
      safeAddEvent(volumeController, displayVolume);
      safeAddEvent(muteController, settings.mute);
      safeAddEvent(inputTypeController, settings.inputType);
      safeAddEvent(outputTypeController, settings.outputType);
      safeAddEvent(headphoneEnabledController, settings.headphoneEnabled);
      safeAddEvent(headphoneGainController, settings.headphoneGain);
      safeAddEvent(displayModeController, settings.displayMode);
      safeAddEvent(themeController, settings.theme);
      safeAddEvent(powerTriggerController, settings.powerTrigger);
      safeAddEvent(balanceController, settings.balance);
      safeAddEvent(filterController, settings.pcmFilter);
      safeAddEvent(decodeModeController, settings.decodeMode);
      safeAddEvent(audioBluetoothController, settings.audioBluetooth);
      safeAddEvent(bluetoothAptxController, settings.bluetoothAptx);
      safeAddEvent(relayController, settings.remoteEnabled);
      safeAddEvent(multifunctionKeyController, settings.multifunctionKey);
      safeAddEvent(usbModeController, settings.usbMode);
      safeAddEvent(screenBrightnessController, settings.screenBrightness);
      safeAddEvent(languageController, settings.language);
      safeAddEvent(samplingRateController, settings.sampling);
      safeAddEvent(settingsController, settings);

      // 记录设备设置详情到蓝牙日志
      final detailedSettings =
          "设备设置已更新: "
          "音量=${settings.volume}, "
          "输入=${settings.inputType}, "
          "输出=${settings.outputType}, "
          "主题=${settings.theme}, "
          "显示模式=${settings.displayMode}";
      _logManager.logInfo(detailedSettings);

      Log.i(
        "DX5II: 设备设置已更新, 音量: ${settings.volume}, 输入: ${settings.inputType}, 输出: ${settings.outputType}",
      );
    } catch (e, s) {
      Log.e("DX5II: 处理设备设置时发生严重错误: $e\nStack trace:\n$s");
      _logManager.logError("处理设备设置响应失败", e);
    }
  }

  void _handleVolumeChange(int volume) {
    Log.i("DX5II: 处理音量变化: $volume");
    // 将设备返回的正值转换为UI需要的负值
    int displayVolume = -volume;
    safeAddEvent(volumeController, displayVolume);
  }

  void _handleMuteChange(bool isMute) {
    Log.i("DX5II: 处理静音变化: $isMute");
    safeAddEvent(muteController, isMute);
  }

  void _handleInputTypeChange(int inputType) {
    Log.i("DX5II: 处理输入类型变化: $inputType");
    safeAddEvent(inputTypeController, inputType);
  }

  void _handleOutputTypeChange(int outputType) {
    Log.i("DX5II: 处理输出类型变化: $outputType");
    safeAddEvent(outputTypeController, outputType);
  }

  void _handleHeadphoneEnabledChange(bool isEnabled) {
    Log.i("DX5II: 处理耳机启用变化: $isEnabled");
    safeAddEvent(headphoneEnabledController, isEnabled);
  }

  void _handleHeadphoneGainChange(int gainType) {
    Log.i("DX5II: 处理耳机增益变化: $gainType");
    safeAddEvent(headphoneGainController, gainType);
  }

  void _handleDisplayModeChange(int displayMode) {
    Log.i("DX5II: 处理显示模式变化: $displayMode");
    safeAddEvent(displayModeController, displayMode);
  }

  void _handleThemeChange(int theme) {
    Log.i("DX5II: 处理主题变化: $theme");
    safeAddEvent(themeController, theme);
  }

  void _handlePowerTriggerChange(int triggerType) {
    Log.i("DX5II: 处理电源触发器变化: $triggerType");
    safeAddEvent(powerTriggerController, triggerType);
  }

  void _handleBalanceChange(int balance) {
    Log.i("DX5II: 处理声道平衡变化: $balance");
    safeAddEvent(balanceController, balance);
  }

  void _handlePcmFilterChange(int filterType) {
    Log.i("DX5II: 处理PCM滤波器变化: $filterType");
    safeAddEvent(filterController, filterType);
  }

  void _handleDecodeModeChange(int decodeMode) {
    Log.i("DX5II: 处理解码模式变化: $decodeMode");
    safeAddEvent(decodeModeController, decodeMode);
  }

  void _handleAudioBluetoothChange(bool enable) {
    Log.i("DX5II: 处理音频蓝牙变化: $enable");
    safeAddEvent(audioBluetoothController, enable);
  }

  void _handleBluetoothAptxChange(bool enable) {
    Log.i("DX5II: 处理蓝牙APTX变化: $enable");
    safeAddEvent(bluetoothAptxController, enable);
  }

  void _handleRelayChange(bool enable) {
    Log.i("DX5II: 处理中继变化: $enable");
    safeAddEvent(relayController, enable);
  }

  void _handleMultifunctionKeyChange(int keyType) {
    Log.i("DX5II: 处理多功能键变化: $keyType");
    safeAddEvent(multifunctionKeyController, keyType);
  }

  void _handleUsbModeChange(int usbMode) {
    Log.i("DX5II: 处理USB模式变化: $usbMode");
    safeAddEvent(usbModeController, usbMode);
  }

  void _handleScreenBrightnessChange(int brightnessType) {
    Log.i("DX5II: 处理屏幕亮度变化: $brightnessType");
    safeAddEvent(screenBrightnessController, brightnessType);
  }

  void _handleLanguageChange(int language) {
    Log.i("DX5II: 处理语言变化: $language");
    safeAddEvent(languageController, language);
  }

  /// 处理采样率响应
  void _handleSamplingResponse(int sampling) {
    Log.i("DX5II: 处理采样率响应: $sampling");
    // 记录到蓝牙日志系统
    _logManager.logInfo("收到采样率信息: $sampling");
    safeAddEvent(samplingRateController, sampling);
  }

  @override
  void powerOn(bool isOn) {
    Log.i("DX5II: 设置电源: $isOn");
    Dx5iiDeviceBindings.instance.powerOn(isOn);
  }

  @override
  void setVolume(int volume) {
    Log.i("DX5II: 设置音量: $volume");
    Dx5iiDeviceBindings.instance.setVolume(volume);
  }

  @override
  void setMute(bool isMute) {
    Log.i("DX5II: ${isMute ? '静音' : '取消静音'}");
    Dx5iiDeviceBindings.instance.setMute(isMute);
  }

  @override
  void setInputType(int inputType) {
    Log.i("DX5II: 设置输入类型: $inputType");
    Dx5iiDeviceBindings.instance.setInputType(inputType);
  }

  @override
  void setOutputType(int outputType) {
    Log.i("DX5II: 设置输出类型: $outputType");
    Dx5iiDeviceBindings.instance.setOutputType(outputType);
  }

  @override
  void enableHeadphone(bool enable) {
    Log.i("DX5II: ${enable ? '启用' : '禁用'}耳机");
    Dx5iiDeviceBindings.instance.enableHeadphone(enable);
  }

  @override
  void setHeadphoneGain(int gainType) {
    Log.i("DX5II: 设置耳机增益: $gainType");
    Dx5iiDeviceBindings.instance.setHeadphoneGain(gainType);
  }

  @override
  void setDeviceName(String name) {
    Log.i("DX5II: 设置设备名称: $name");
    return;
  }

  @override
  void verify() {
    Log.i("DX5II: 开始验证设备");
    _logManager.logCommand("开始验证设备");

    // 获取设备名称用于日志记录
    BleDevice? device = getConnectedDevice();
    String deviceName = device?.name ?? "未知设备";
    _logManager.logInfo("开始验证设备: $deviceName");

    // 启动验证拦截器
    VerifyInterceptor.instance.startVerifying();

    // 执行验证操作
    Dx5iiDeviceBindings.instance.verify();
  }

  @override
  void requestSettings() {
    Log.i("DX5II: 请求设备设置");
    final deviceInfo = getConnectedDevice()?.name ?? "未知设备";
    _logManager.logCommand("正在请求DX5II设备[$deviceInfo]的设置信息");

    try {
      Dx5iiDeviceBindings.instance.requestSettings();
      _logManager.logInfo("设备设置请求已发送，等待响应...");
    } catch (e) {
      Log.e("DX5II: 请求设备设置出错: $e");
      _logManager.logError("请求设备设置出错", e);
    }
  }

  /// 请求采样率信息
  void requestSampling() {
    Log.i("DX5II: 请求设备采样率");
    final deviceInfo = getConnectedDevice()?.name ?? "未知设备";
    _logManager.logCommand("正在请求DX5II设备[$deviceInfo]的采样率信息");

    try {
      Dx5iiDeviceBindings.instance.requestSampling();
      _logManager.logInfo("采样率请求已发送，等待响应...");
    } catch (e) {
      Log.e("DX5II: 请求设备采样率出错: $e");
      _logManager.logError("请求设备采样率出错", e);
    }
  }

  @override
  void setDisplayMode(int displayMode) {
    Log.i("DX5II: 设置显示模式: $displayMode");
    Dx5iiDeviceBindings.instance.setDisplayMode(displayMode);
  }

  @override
  void setTheme(int theme) {
    Log.i("DX5II: 设置主题: $theme");
    Dx5iiDeviceBindings.instance.setTheme(theme);
  }

  @override
  void setPowerTrigger(int triggerType) {
    Log.i("DX5II: 设置电源触发器: $triggerType");
    Dx5iiDeviceBindings.instance.setPowerTrigger(triggerType);
  }

  @override
  void setBalance(int balance) {
    Log.i("DX5II: 设置平衡: $balance");
    Dx5iiDeviceBindings.instance.setBalance(balance);
  }

  @override
  void setFilter(int filterType) {
    Log.i("DX5II: 设置滤波器: $filterType");
    Dx5iiDeviceBindings.instance.setPcmFilter(filterType);
  }

  @override
  void setDecodeMode(int decodeMode) {
    Log.i("DX5II: 设置解码模式: $decodeMode");
    Dx5iiDeviceBindings.instance.setDecodeMode(decodeMode);
  }

  @override
  void enableAudioBluetooth(bool enable) {
    Log.i("DX5II: ${enable ? '启用' : '禁用'}音频蓝牙");
    Dx5iiDeviceBindings.instance.enableAudioBluetooth(enable);
  }

  @override
  void enableBluetoothAptx(bool enable) {
    Log.i("DX5II: ${enable ? '启用' : '禁用'}蓝牙APTX");
    Dx5iiDeviceBindings.instance.enableBluetoothAptx(enable);
  }

  @override
  void enableRelay(bool enable) {
    Log.i("DX5II: ${enable ? '启用' : '禁用'}中继");
    Dx5iiDeviceBindings.instance.enableRelay(enable);
  }

  @override
  void setMultifunctionKey(int keyType) {
    Log.i("DX5II: 设置多功能键: $keyType");
    Dx5iiDeviceBindings.instance.setMultifunctionKey(keyType);
  }

  @override
  void setUsbMode(int usbMode) {
    Log.i("DX5II: 设置USB模式: $usbMode");
    Dx5iiDeviceBindings.instance.setUsbMode(usbMode);
  }

  @override
  void setScreenBrightness(int brightnessType) {
    Log.i("DX5II: 设置屏幕亮度: $brightnessType");
    Dx5iiDeviceBindings.instance.setScreenBrightness(brightnessType);
  }

  @override
  void setLanguage(int language) {
    Log.i("DX5II: 设置语言: $language");
    Dx5iiDeviceBindings.instance.setLanguage(language);
  }

  @override
  void restoreFactorySettings() {
    Log.i("DX5II: 恢复出厂设置");
    Dx5iiDeviceBindings.instance.restoreFactorySettings();
    triggerRestoreFactorySettings();
  }

  @override
  BleDevice? getConnectedDevice() {
    int? handle = CurrentConnectingDevice().handle;
    if (handle == null) return null;
    return DeviceDataManager().getDeviceByHandle(handle);
  }

  @override
  void connect(int deviceHandle) {
    Log.i("DX5II: 连接设备, 句柄: $deviceHandle");
    connectDevice(deviceHandle);
  }

  @override
  void disconnect() {
    Log.i("DX5II: 断开设备连接");
    Dx5iiDeviceBindings.instance.disconnect();
  }
}
