#ifndef __BLUETOOTH_GATT_SERVICE_H__
#define __BLUETOOTH_GATT_SERVICE_H__
#include "BluetoothGattCharacteristic.h"
namespace Topping
{
    class BluetoothGattService
    {
    public:
        BluetoothGattService(const std::string &uuid);
        ~BluetoothGattService();
        void setUUID(const std::string &uuid);
        const std::string &getUUID();
        void addCharacteristic(const BluetoothGattCharacteristic &characteristic);
        BluetoothGattCharacteristic *getCharacteristic(const std::string &uuid);
        size_t getCharacteristicCount();

    private:
        std::string mUUID;
        std::vector<BluetoothGattCharacteristic> mCharacteristics;
    };
}
#endif