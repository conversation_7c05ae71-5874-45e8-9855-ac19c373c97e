/// 此类定义了RWCP协议中使用的常量。
class Rwcp {
  /// 窗口的最大大小。
  static const int windowMax = 32;

  /// 窗口的默认大小。
  static const int windowDefault = 15;

  /// SYN操作超时的毫秒延迟。
  static const int synTimeoutMs = 1000;

  /// RST操作超时的毫秒延迟。
  static const int rstTimeoutMs = 1000;

  /// DATA操作超时的默认毫秒延迟。
  static const int dataTimeoutMsDefault = 100;

  /// DATA操作超时的最大毫秒延迟。
  static const int dataTimeoutMsMax = 2000;

  /// 序列号的最大值是63，对应于6位所能表示的最大值。
  static const int sequenceNumberMax = 63;

  /// 该方法构建一个与给定状态值对应的人类可读标签，如"CLOSING"、"ESTABLISHED"、"LISTEN"和"SYN_SENT"。
  ///
  /// 参数：
  ///   state - 需要人类可读值的状态。
  ///
  /// 返回：
  ///   给定值的人类可读标签。
  static String getStateLabel(int state) {
    switch (state) {
      case RwcpState.closing:
        return "CLOSING";
      case RwcpState.established:
        return "ESTABLISHED";
      case RwcpState.listen:
        return "LISTEN";
      case RwcpState.synSent:
        return "SYN_SENT";
      default:
        return "Unknown state ($state)";
    }
  }
}

class RwcpState {
  /// 客户端准备好让应用程序请求发送写命令到服务器。
  static const int listen = 0;

  /// 客户端已经启动会话，并且正在等待服务器确认启动。
  static const int synSent = 1;

  /// 客户端向服务器发送数据。
  static const int established = 2;

  /// 客户端已经终止连接，并且正在等待服务器确认终止请求。
  static const int closing = 3;
}

class RwcpOpCodeClient {
  /// 客户端发送到服务器的数据。
  static const int data = 0;

  /// 用于客户端同步和启动会话。
  static const int syn = 1;

  /// RST用于客户端终止会话。
  static const int rst = 2;

  /// 未定义的操作代码，不应使用。
  static const int reserved = 3;
}

class RwcpOpCodeServer {
  /// 用于服务器确认发送到服务器的数据。
  static const int dataAck = 0;

  /// 用于服务器确认SYN段。
  static const int synAck = 1;

  /// RST用于服务器终止会话。
  /// RST_ACK用于服务器确认客户端的终止请求。
  static const int rst = 2;

  static const int rstAck = 2;

  /// 用于服务器指示服务器接收到一个不按顺序的DATA段。
  static const int gap = 3;
}

class RwcpSegment {
  /// 头信息的偏移量。
  static const int headerOffset = 0;

  /// 头信息的字节数。
  static const int headerLength = 1;

  /// 负载信息的偏移量。
  static const int payloadOffset = headerOffset + headerLength;

  /// 段的最小长度。
  static const int requiredInformationLength = headerLength;

/// RWCP段的头部包含识别段的信息：序列号和操作代码。头部包含在一个字节中，其位分配如下：
/// ```dart
/// 0 位     ...         6          7          8
/// +----------+----------+----------+----------+
/// |   序列号   |   操作代码    |
/// +----------+----------+----------+----------+
/// ```
}

class SegmentHeader {
  /// 序列号的位偏移量。
  static const int sequenceNumberBitOffset = 0;

  /// 包含序列号信息的位数。
  static const int sequenceNumberBitsLength = 6;

  /// 操作代码的位偏移量。
  static const int operationCodeBitOffset =
      sequenceNumberBitOffset + sequenceNumberBitsLength;

  /// 包含操作代码的位数。
  static const int operationCodeBitsLength = 2;
}
