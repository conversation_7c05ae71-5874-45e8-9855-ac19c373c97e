import 'package:topping_ble_control/utils/log_util.dart';
import 'package:topping_ble_control/utils/app_log_manager.dart';

/// 验证拦截器类，用于拦截和记录验证过程中的蓝牙通信
class VerifyInterceptor {
  static final String _logTag = "VerifyInterceptor";
  static final VerifyInterceptor _instance = VerifyInterceptor._internal();
  static VerifyInterceptor get instance => _instance;

  // 蓝牙日志管理器
  final BluetoothLogManager _logManager = BluetoothLogManager.instance;

  // 验证会话ID，每次验证过程都有一个唯一ID
  String? _currentVerifySessionId;

  // 记录是否正在处理开始验证请求，防止多次调用
  bool _processingStartRequest = false;

  // 当前验证阶段（从1开始）
  int _currentVerifyStage = 0;

  // 当前阶段是否已经收到验证成功结果
  bool _stageVerifySuccess = false;

  // 当前阶段写入是否成功
  bool _stageWriteSuccess = false;

  // 是否已收到设备响应
  bool _stageReceivedResponse = false;

  // 私有构造函数
  VerifyInterceptor._internal();

  // 是否处于验证模式
  bool _isVerifying = false;

  /// 开始验证模式
  void startVerifying() {
    // 防止短时间内多次调用创建多个会话
    if (_processingStartRequest) {
      Log.w("$_logTag: 已有验证会话正在启动中，忽略重复请求");
      return;
    }

    // 如果已经在验证中，先结束当前验证
    if (_isVerifying && _currentVerifySessionId != null) {
      Log.w("$_logTag: 上一个验证会话未正常结束，强制结束");
      stopVerifying();
    }

    try {
      _processingStartRequest = true;
      _isVerifying = true;

      // 删除旧的验证日志文件
      _logManager.deleteVerifyLogFile();

      // 重置验证阶段
      _currentVerifyStage = 1;
      _stageVerifySuccess = false;
      _stageWriteSuccess = false;
      _stageReceivedResponse = false;

      // 使用BluetoothLogManager生成唯一的会话ID
      _currentVerifySessionId = _logManager.generateVerifySessionId();

      Log.i("$_logTag: 开始验证模式，会话ID：$_currentVerifySessionId");

      // 记录验证开始和阶段信息到验证日志文件
      _logManager.logVerifyStart(_currentVerifySessionId!);

      // 记录验证阶段开始
      final stageMessage = "[VERIFY] ====== 开始验证阶段 $_currentVerifyStage ======";
      Log.i(stageMessage);
      _logManager.writeRawVerifyLog(stageMessage);
    } finally {
      // 确保处理标志被重置
      _processingStartRequest = false;
    }
  }

  /// 结束验证模式
  void stopVerifying() {
    if (!_isVerifying) {
      return;
    }

    final sessionId = _currentVerifySessionId;
    _isVerifying = false;
    _currentVerifyStage = 0;
    _stageVerifySuccess = false;
    _stageWriteSuccess = false;
    _stageReceivedResponse = false;

    Log.i("$_logTag: 结束验证模式，会话ID：$sessionId");

    // 记录到验证日志文件
    if (sessionId != null) {
      _logManager.logVerifyEnd(sessionId);
    }

    // 清除当前会话ID
    _currentVerifySessionId = null;
  }

  /// 是否处于验证模式
  bool get isVerifying => _isVerifying;

  /// 当前验证阶段
  int get currentVerifyStage => _currentVerifyStage;

  /// 拦截并记录发送的数据
  Future<void> interceptSendData(List<int> data) async {
    if (!_isVerifying || _currentVerifySessionId == null) return;

    try {
      // 检查是否需要更新验证阶段
      if (_stageVerifySuccess) {
        _currentVerifyStage++;
        _stageVerifySuccess = false;
        _stageWriteSuccess = false;
        _stageReceivedResponse = false;
        final stageMessage =
            "[VERIFY] ====== 开始验证阶段 $_currentVerifyStage ====== [会话ID: $_currentVerifySessionId]";
        Log.i(stageMessage);
        // 等待写入完成，避免日志混合
        await _logManager.writeRawVerifyLog(stageMessage);
      }

      // 检查数据是否为空
      if (data == null || data.isEmpty) {
        Log.w("$_logTag: 发送的数据为空，不记录");
        return;
      }

      // 转换为十六进制字符串
      final hexString = data
          .map((e) => e.toRadixString(16).padLeft(2, '0'))
          .join(' ');

      // 先记录操作类型和会话ID，包含阶段信息
      // 等待写入完成，避免日志混合
      await _logManager.logVerifyData(
        "发送",
        _currentVerifySessionId!,
        "阶段$_currentVerifyStage",
      );

      // 单独记录十六进制数据，确保数据不被截断
      final dataContentMessage =
          "[VERIFY] [阶段$_currentVerifyStage] 数据内容 [会话ID: $_currentVerifySessionId]: $hexString";
      Log.i(dataContentMessage);
      // 等待写入完成，避免日志混合
      await _logManager.writeRawVerifyLog(dataContentMessage);

      // 记录数据长度和时间
      final dataInfo =
          "数据长度: ${data.length} 字节, 时间: ${DateTime.now().toString()}";
      final dataInfoMessage =
          "[VERIFY] [阶段$_currentVerifyStage] 数据信息 [会话ID: $_currentVerifySessionId]: $dataInfo";
      Log.i(dataInfoMessage);
      // 等待写入完成，避免日志混合
      await _logManager.writeRawVerifyLog(dataInfoMessage);
    } catch (e) {
      Log.e("$_logTag: 拦截发送数据时出错: $e");
      await _logManager.writeRawVerifyLog("[VERIFY] 拦截发送数据时出错: $e");
    }
  }

  /// 拦截并记录写入结果（仅表示数据发送成功，不代表验证成功）
  Future<void> interceptWriteResult(bool success, {String? reason}) async {
    if (!_isVerifying || _currentVerifySessionId == null) return;

    // 记录写入结果
    _stageWriteSuccess = success;

    // 只记录日志，不设置验证结果
    await _logManager.logVerifyResult(
      _currentVerifySessionId!,
      success,
      reason: reason != null ? "写入结果：$reason" : "写入结果",
      stage: _currentVerifyStage,
    );

    if (success) {
      final successMessage =
          "[VERIFY] ✅ ==== 阶段 $_currentVerifyStage 写入成功 ==== [会话ID: $_currentVerifySessionId]";
      Log.i(successMessage);
      await _logManager.writeRawVerifyLog(successMessage);
    } else {
      final failMessage =
          "[VERIFY] ❌ ==== 阶段 $_currentVerifyStage 写入失败 ==== [会话ID: $_currentVerifySessionId]";
      Log.i(failMessage);
      await _logManager.writeRawVerifyLog(failMessage);

      if (reason != null) {
        final reasonMessage = "[VERIFY] 写入失败原因: $reason";
        Log.i(reasonMessage);
        await _logManager.writeRawVerifyLog(reasonMessage);
      }
    }
  }

  /// 拦截并记录收到的回复
  Future<void> interceptResponse(List<int> data) async {
    if (!_isVerifying || _currentVerifySessionId == null) return;

    try {
      // 标记已收到响应
      _stageReceivedResponse = true;

      // 检查数据是否为空
      if (data == null || data.isEmpty) {
        Log.w("$_logTag: 收到的响应数据为空，不记录");
        return;
      }

      // 转换为十六进制字符串
      final hexString = data
          .map((e) => e.toRadixString(16).padLeft(2, '0'))
          .join(' ');

      // 先记录操作类型和会话ID，包含阶段信息
      // 等待写入完成，避免日志混合
      await _logManager.logVerifyData(
        "响应",
        _currentVerifySessionId!,
        "阶段$_currentVerifyStage",
      );

      // 单独记录十六进制数据，确保数据不被截断
      final dataContentMessage =
          "[VERIFY] [阶段$_currentVerifyStage] 数据内容 [会话ID: $_currentVerifySessionId]: $hexString";
      Log.i(dataContentMessage);
      // 等待写入完成，避免日志混合
      await _logManager.writeRawVerifyLog(dataContentMessage);

      // 记录数据长度和时间
      final dataInfo =
          "数据长度: ${data.length} 字节, 时间: ${DateTime.now().toString()}";
      final dataInfoMessage =
          "[VERIFY] [阶段$_currentVerifyStage] 数据信息 [会话ID: $_currentVerifySessionId]: $dataInfo";
      Log.i(dataInfoMessage);
      // 等待写入完成，避免日志混合
      await _logManager.writeRawVerifyLog(dataInfoMessage);

      final responseMessage =
          "[VERIFY] 🔄 阶段 $_currentVerifyStage 已收到设备响应 [会话ID: $_currentVerifySessionId]";
      Log.i(responseMessage);
      await _logManager.writeRawVerifyLog(responseMessage);
    } catch (e) {
      Log.e("$_logTag: 拦截收到回复时出错: $e");
      await _logManager.writeRawVerifyLog("[VERIFY] 拦截收到回复时出错: $e");
    }
  }

  /// 设置验证结果（从C++回调）
  Future<void> setVerifyResult(bool success, {String? reason}) async {
    if (!_isVerifying || _currentVerifySessionId == null) return;

    // 只有写入成功并收到响应时，才接受验证结果
    if (!_stageWriteSuccess) {
      Log.w("$_logTag: 阶段$_currentVerifyStage未成功写入，忽略验证结果");
      await _logManager.writeRawVerifyLog(
        "[VERIFY] 警告：阶段$_currentVerifyStage未成功写入，忽略验证结果",
      );
      return;
    }

    // 记录当前阶段的验证结果
    _stageVerifySuccess = success;

    // 使用专用验证日志记录方法，包含阶段信息
    await _logManager.logVerifyResult(
      _currentVerifySessionId!,
      success,
      reason: reason,
      stage: _currentVerifyStage,
    );

    if (success) {
      final successMessage =
          "[VERIFY] ✅ ==== 验证阶段 $_currentVerifyStage 成功 ==== [会话ID: $_currentVerifySessionId]";
      Log.i(successMessage);
      await _logManager.writeRawVerifyLog(successMessage);
    } else {
      final failMessage =
          "[VERIFY] ❌ ==== 验证阶段 $_currentVerifyStage 失败 ==== [会话ID: $_currentVerifySessionId]";
      Log.i(failMessage);
      await _logManager.writeRawVerifyLog(failMessage);

      if (reason != null) {
        final reasonMessage = "[VERIFY] 失败原因: $reason";
        Log.i(reasonMessage);
        await _logManager.writeRawVerifyLog(reasonMessage);
      }
    }
  }

  /// 手动设置当前验证阶段
  void setVerifyStage(int stage) {
    if (!_isVerifying || _currentVerifySessionId == null) return;

    if (stage > 0 && stage != _currentVerifyStage) {
      _currentVerifyStage = stage;
      _stageVerifySuccess = false;
      _stageWriteSuccess = false;
      _stageReceivedResponse = false;
      final stageMessage =
          "[VERIFY] ====== 手动设置为验证阶段 $_currentVerifyStage ====== [会话ID: $_currentVerifySessionId]";
      Log.i(stageMessage);
      _logManager.writeRawVerifyLog(stageMessage);
    }
  }
}
