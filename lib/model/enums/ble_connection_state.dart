/// BLE连接状态
enum BleConnectionState {
  disconnected(0),
  connecting(1),
  connected(2),
  disconnecting(3),
  connectedUnsafe(4);

  final int value;

  const BleConnectionState(this.value);

  /// fromValue
  static BleConnectionState fromValue(int value) {
    switch (value) {
      case 0:
        return BleConnectionState.connectedUnsafe;
      case 1:
        return BleConnectionState.connected;
      case 2:
        return BleConnectionState.disconnected;
      default:
        return BleConnectionState.disconnected;
    }
  }
}
