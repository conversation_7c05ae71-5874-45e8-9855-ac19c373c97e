import 'package:topping_ble_control/utils/log_util.dart';

/// Topping设备回调基类
/// 封装DX5II和D900共有的回调方法
abstract class ToppingDeviceCallback {
  /// 扫描结果回调
  final Function(List<dynamic>) onScanResults;

  /// 扫描失败回调
  final Function(int) onScanFailed;

  /// 状态变化回调
  final Function(int) onStateChange;

  /// 验证结果回调
  final Function(int) onVerifyResult;

  /// 电源状态变化回调
  final Function(bool) onPowerChange;

  /// 音量变化回调
  final Function(int) onVolumeChange;

  /// 静音状态变化回调
  final Function(bool) onMuteChange;

  /// 输入类型变化回调
  final Function(int) onInputTypeChange;

  /// 输出类型变化回调
  final Function(int) onOutputTypeChange;

  /// 显示模式变化回调
  final Function(int) onDisplayModeChange;

  /// 主题变化回调
  final Function(int) onThemeChange;

  /// 电源触发器变化回调
  final Function(int) onPowerTriggerChange;

  /// 声道平衡变化回调
  final Function(int) onBalanceChange;

  /// 蓝牙音频启用状态变化回调
  final Function(bool) onAudioBluetoothChange;

  /// 蓝牙APTX启用状态变化回调
  final Function(bool) onBluetoothAptxChange;

  /// 远程启用状态变化回调
  final Function(bool) onRemoteEnabledChange;

  /// 多功能键变化回调
  final Function(int) onMultifunctionKeyChange;

  /// USB模式变化回调
  final Function(int) onUsbModeChange;

  /// 屏幕亮度变化回调
  final Function(int) onScreenBrightnessChange;

  /// 语言变化回调
  final Function(int) onLanguageChange;

  /// 构造函数
  ToppingDeviceCallback({
    required this.onScanResults,
    required this.onScanFailed,
    required this.onStateChange,
    required this.onVerifyResult,
    this.onPowerChange = _defaultOnPowerChange,
    this.onVolumeChange = _defaultOnVolumeChange,
    this.onMuteChange = _defaultOnMuteChange,
    this.onInputTypeChange = _defaultOnInputTypeChange,
    this.onOutputTypeChange = _defaultOnOutputTypeChange,
    this.onDisplayModeChange = _defaultOnDisplayModeChange,
    this.onThemeChange = _defaultOnThemeChange,
    this.onPowerTriggerChange = _defaultOnPowerTriggerChange,
    this.onBalanceChange = _defaultOnBalanceChange,
    this.onAudioBluetoothChange = _defaultOnAudioBluetoothChange,
    this.onBluetoothAptxChange = _defaultOnBluetoothAptxChange,
    this.onRemoteEnabledChange = _defaultOnRemoteEnabledChange,
    this.onMultifunctionKeyChange = _defaultOnMultifunctionKeyChange,
    this.onUsbModeChange = _defaultOnUsbModeChange,
    this.onScreenBrightnessChange = _defaultOnScreenBrightnessChange,
    this.onLanguageChange = _defaultOnLanguageChange,
  });

  // --- 默认处理函数 ---
  static void _defaultOnPowerChange(bool isOn) => Log.e("未处理: 电源状态 $isOn");
  static void _defaultOnVolumeChange(int volume) => Log.e("未处理: 音量 $volume");
  static void _defaultOnMuteChange(bool isMute) => Log.e("未处理: 静音 $isMute");
  static void _defaultOnInputTypeChange(int inputType) =>
      Log.e("未处理: 输入类型 $inputType");
  static void _defaultOnOutputTypeChange(int outputType) =>
      Log.e("未处理: 输出类型 $outputType");
  static void _defaultOnDisplayModeChange(int mode) => Log.e("未处理: 显示模式 $mode");
  static void _defaultOnThemeChange(int theme) => Log.e("未处理: 主题 $theme");
  static void _defaultOnPowerTriggerChange(int trigger) =>
      Log.e("未处理: 电源触发 $trigger");
  static void _defaultOnBalanceChange(int balance) => Log.e("未处理: 平衡 $balance");
  static void _defaultOnAudioBluetoothChange(bool enabled) =>
      Log.e("未处理: 音频蓝牙 $enabled");
  static void _defaultOnBluetoothAptxChange(bool enabled) =>
      Log.e("未处理: AptX $enabled");
  static void _defaultOnRemoteEnabledChange(bool enabled) =>
      Log.e("未处理: 远程启用 $enabled");
  static void _defaultOnMultifunctionKeyChange(int key) =>
      Log.e("未处理: 多功能键 $key");
  static void _defaultOnUsbModeChange(int mode) => Log.e("未处理: USB模式 $mode");
  static void _defaultOnScreenBrightnessChange(int brightness) =>
      Log.e("未处理: 屏幕亮度 $brightness");
  static void _defaultOnLanguageChange(int language) =>
      Log.e("未处理: 语言 $language");
}
